<template>
    <div class='manage-products-container'>
        <div class='categories-container' v-if="shopData && shopData.id">
            <client-only>
                <Swiper class="my-carousel categories-tab" :modules="[SwiperFreeMode]" :freeMode="true"
                    :slides-per-view="'auto'" :loop="false" :spaceBetween="10" :effect="'creative'" :autoplay="false">
                    <SwiperSlide class="category-item-tab" :class="{ 'active': filterCategory == null }" :value="'all'"
                        v-on:click="async () => {
                            if (filterCategory != null) {
                                filterCategory = null;
                                await getShopProduct(0, 20);
                            }
                        }" :key="'all'" :id="'tab_all'">
                        <div class="tab-title">
                            <span class='name'>
                                {{ $t('ManageProductsComponent.tat_ca') }}
                            </span>
                        </div>
                    </SwiperSlide>
                    <SwiperSlide class="category-item-tab" :class="{ 'active': checkCategoryFilter(itemCategory.id) }"
                        :value="itemCategory.id" v-for="(itemCategory, indexTab) of dataShopCategories" v-on:click="async () => {
                            let selected = checkCategoryFilter(itemCategory.id);
                            if (!selected) {
                                filterCategory = itemCategory.id;
                                await getShopProduct(0, 20);
                            }
                        }" :key="itemCategory.id" :id="'tab_' + itemCategory.id">
                        <div class="tab-title">
                            <span class='name'>
                                {{ showTranslateProductName(itemCategory) }}
                            </span>
                        </div>
                    </SwiperSlide>
                </Swiper>
            </client-only>
        </div>

        <div class="h-stack search-bar-container" v-if="shopData && shopData.id">
            <button class='search-button' :disabled=filterProductLoading>
                <Icon name="ion:search" size="20" v-show="!filterProductLoading" />
                <Icon name="eos-icons:loading" size="20" v-show="filterProductLoading" />
            </button>
            <input type="search" name='search-text' :placeholder="$t('ManageProductsComponent.tim_san_pham')"
                :maxlength="appConst.max_text_short"
                autoComplete='off' class='search-input-container' :value="search_text" v-on:input="($event: any) => {
                    search_text = $event.target.value;
                    filterProductLoading = true;
                    searchProduct()
                }" />
        </div>

        <div class="v-stack list-product-container" v-if="shopData && shopData.id" id="list_product_container">
            <span class="list-product-amount" v-if="!isRefreshing && listProduct && listProduct.length">
                {{ $t('ManageProductsComponent.hien_thi', {
                    current: listProduct ? listProduct.length : 0, total:
                        countProducts ? countProducts : 0
                }) }}
            </span>
            <span class="product-loading" v-if="isRefreshing">
                {{ $t('ManageProductsComponent.dang_tai') }}
            </span>
            <div class="v-stack none-list-product" v-if="!isRefreshing && !(listProduct && listProduct.length)">
                <img loading="lazy" :src='list_empty' :placeholder="list_empty"
                    :alt="$t('ManageProductsComponent.khong_co_san_pham')" />
                <span>
                    {{ $t('ManageProductsComponent.khong_tim_thay_san_pham') }}
                </span>
            </div>
            <keep-alive>
                <div class="list-product-show" v-if="!isRefreshing && listProduct && listProduct.length">
                    <div class="product-item" v-for="(itemProduct, index) in listProduct"
                        :key="itemProduct.id + '_' + index" :class="{ 'grid': props.showGrid }" v-on:click="() => {
                            if (props.mode != 'agent') {
                                router.push(appRoute.EditProductComponent.replaceAll(':id', itemProduct.id))
                            }
                            else {
                                router.push(appRoute.AgentShopEditProductComponent.replaceAll(':id', shopData.slug).replaceAll(':product_id', itemProduct.id))
                            }
                        }" :id="itemProduct.id + '_' + index">
                        <img loading="lazy" :src="(itemProduct && itemProduct.profile_picture && itemProduct.profile_picture.length) ?
                            (domainImage + itemProduct.profile_picture) : icon_for_product"
                            :placeholder="icon_for_product" :alt="showTranslateProductName(itemProduct)" />
                        <div class="v-stack product-detail">
                            <span class="name">
                                {{ showTranslateProductName(itemProduct) }}
                            </span>
                            <span class="categories">
                                {{ $t('ManageProductsComponent.danh_muc') }}:
                                <span 
                                    class="categories-name" 
                                    v-for="(categoryItem, index) of itemProduct.categories"
                                    :key="`product_${itemProduct.id}_category_${categoryItem.id}`">
                                    {{ showTranslateProductName(categoryItem) + (index < itemProduct.categories.length - 1 ? ", " : " ") }}
								</span>
                                <span class="not-classified" v-if="!itemProduct.categories?.length">
                                    {{ $t('ManageProductsComponent.chua_phan_loai') }}
                                </span>
                            </span>
                            <span class="price">
                                {{
                                    (itemProduct.price_off != null && itemProduct.price_off < itemProduct.price) ?
                                        formatCurrency(parseFloat(itemProduct.price_off), shopData.currency) :
                                        (parseFloat(itemProduct.price) == 0 || itemProduct.price == null) ?
                                            $t('ManageProductsComponent.gia_lien_he') :
                                            formatCurrency(parseFloat(itemProduct.price), shopData.currency) }} <em class="off"
                                    v-if="(itemProduct.price_off != null && parseFloat(itemProduct.price_off) < parseFloat(itemProduct.price))">
                                    {{
                                        itemProduct.price != null ? formatCurrency(itemProduct.price != null ?
                                            parseFloat(itemProduct.price) : 0, shopData.currency) :
                                            $t('ManageProductsComponent.gia_lien_he')
                                    }}</em>
                            </span>
                            <div class="product-stock">
                                <span class='label'>
                                    {{ $t('ManageProductsComponent.ton_kho') }}:
                                </span>
                                <span class="content">{{ itemProduct.stock != null ? formatNumber(itemProduct.stock)
                                    : '--' }}
                                </span>
                            </div>                            
                            <!-- Commission Percent Display -->
                            <div class="product-commission" v-if="shopData?.settings?.general?.commission_percent?.enabled && (itemProduct.commission_percent != null || shopData?.settings?.general?.commission_percent?.value != null)">
                                <span class='label'>
                                    {{ $t('ManageProductsComponent.commission_percent') }}:
                                </span>
                                <span class="content">{{ itemProduct.commission_percent != null ? itemProduct.commission_percent : shopData?.settings?.general?.commission_percent?.value }}%</span>
                            </div>
                            <div class="h-stack product-enable">
                                <span class='label'>
                                    {{ $t('ManageProductsComponent.hien_thi_enable') }}
                                </span>
                                <v-switch 
                                    v-model="itemProduct.enable" 
                                    
                                    flat 
                                    color="var(--primary-color-1)" 
                                    hide-details 
                                    class="my-switches"
                                    v-on:click="(event: Event)=>{
                                        event.stopPropagation()
                                    }"
                                    v-on:update:modelValue="(e:any)=>{
                                        updateProductEnable(itemProduct, e)
                                    }"
                                >
                                </v-switch>
                            </div>
                        </div>
                    </div>
                </div>
            </keep-alive>
            <span class='loading-more' v-if="loadMore">
                {{ $t('ManageProductsComponent.dang_tai') }}
            </span>
            <div id='last_of_list'></div>
            <v-menu class="bootstrap-dropdown-container" location="top right">
                <template v-slot:activator="{ props }">
                    <button class="add-product-button" v-bind="props">
                        <Icon name="material-symbols:add-rounded"></Icon>
                    </button>
                </template>
                <v-list>
                    <v-list-item key="add_new" class="add-product-option" v-on:click="() => {
                        if (mode == 'agent') {
                            router.push({
                                path: appRoute.AgentCreatePrivateProductComponent.replaceAll(':id', shopData.slug),
                            })
                        }
                        else {
                            router.push({
                                path: appRoute.CreatePrivateProductComponent
                            })
                        }
                    }">
                        <v-list-item-title>{{ $t('ManageProductsComponent.dang_moi') }}</v-list-item-title>
                    </v-list-item>
                    <!-- <v-list-item key="add-from-system" class="add-product-option" v-on:click="() => {
                        if (mode == 'agent') {
                            router.push({
                                path: appRoute.AgentAddProductFromSystemComponent.replaceAll(':id', shopData.slug),
                            })
                        }
                        else {
                            router.push({
                                path: appRoute.AddProductsFromSystemComponent
                            })
                        }
                    }">
                        <v-list-item-title>{{ $t('ManageProductsComponent.them_tu_he_thong') }}</v-list-item-title>
                    </v-list-item>
                    <v-list-item key="batch-import" class="add-product-option" v-on:click="() => {
                        if (mode == 'agent') {
                            router.push({
                                path: appRoute.AgentBatchImportComponent.replaceAll(':id', shopData.slug),
                            })
                        }
                        else {
                            router.push({
                                path: appRoute.BatchImportComponent
                            })
                        }
                    }">
                        <v-list-item-title>{{ $t('ManageProductsComponent.nhap_hang_loat') }}</v-list-item-title>
                    </v-list-item> -->
                </v-list>
            </v-menu>
        </div>
        <div class="access-denied" v-if="!(isRefreshing || (shopData && shopData.id))">
            <img loading="lazy" :src="none_shop" :placeholder="none_shop"
                :alt="$t('ManageProductsComponent.tu_choi_quyen_truy_cap')" />
            <span>
                {{ $t('ManageProductsComponent.ban_chua_dang_ky_cua_hang') }}
            </span>
        </div>
    </div>

</template>
<script lang="ts" setup>
const router = useRouter();
const route = useRoute();
const { t } = useI18n();
var emit = defineEmits(['close']);
var props = defineProps({
    showGrid: false as any,
    sortBy: null,
    filterCategory: null,
    shopData: null,
    mode: null,
    scroll: null
})
var nuxtApp = useNuxtApp();
useSeoMeta({
    title: t('AppRouteTitle.MyShopManageComponent')
});

import icon_for_product from '~/assets/image/icon-for-product.png';
import list_empty from "~/assets/image/list-empty-2.jpg"
import none_shop from "~/assets/image/none-shop.jpg"

import { VueFinalModal } from 'vue-final-modal';
import moment from 'moment';
import { toast } from 'vue3-toastify';
import { appConst, baseLogoUrl, domain, domainImage, formatCurrency, formatNumber, zaloConfig, showTranslateProductName, showTranslateProductDescription } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { UserService } from '~/services/userService/userService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { AgentService } from '~/services/agentService/agentService';
import { HttpStatusCode } from 'axios';

let ZaloSocialSDK: any;

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var agentService = new AgentService();
var categoryService = new CategoryService();
var searchProductTimeout: any;
var loadMoreTimeOut: any;

var webInApp = ref(null as any);

var shopData = useState('shop_data', () => { return null as any });
var listProduct = useState('shop_products', () => { return [] as any });
var selectedProduct = ref({});
var dataShopCategories = useState('shop_categories', () => { return [] as any });
var productSortBy = ref(props.sortBy ? props.sortBy : null);
var countProducts = useState('shop_products_count', () => { return 0 });
var isRefreshing = ref(false);
var loadMore = ref(false);
var filterProductLoading = ref(false);
var search_text = useState("search_text", () => { return "" });
var filterCategory = useState("filter_category", () => { return null as any });

onBeforeMount(() => {
    // nuxtApp.$listen('refresh_product_manage', () => {
    //     getShopProduct()
    // })
})
onMounted(async () => {
    console.log("mount product");
    if (props.shopData?.id) {
        shopData.value = JSON.parse(JSON.stringify(props.shopData));
    }
    if (props.mode == 'agent') {
        // await getShopDetailByAgent();
        getShopProduct();
        getListCategory();
    }
    else {
        getShopProduct();
        getListCategory();

    }
})
onUpdated(() => {
    if (router.options.history.state.refresh == true) {
        getShopProduct();
        router.options.history.state.refresh = false;
    }
    if (productSortBy.value != props.sortBy) {
        productSortBy.value = props.sortBy;
        getShopProduct();
    }
    if (props.scroll) {
        productContainerScroll()
    }
})
function getMyShop() {
    isRefreshing.value = true;
    shopService.myShop().then(res => {
        if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {

            shopData.value = res.body.data;
            getListCategory();
            getShopProduct()
            isRefreshing.value = false;
        }
        else {
            isRefreshing.value = false;
            shopData.value = null;
        }
        return
    })
}
function getShopDetailByAgent() {
    agentService.agentShopDetail(route.params.id as any).then(res => {
        if (res.status && res.status == HttpStatusCode.Ok) {
            shopData.value = res.body.data;
            getListCategory();
            getShopProduct()
            isRefreshing.value = false;
        }
        else {
            isRefreshing.value = false;
            shopData.value = null;
        }
        return
    })
}
function getShopProduct(offset?: any, limit?: any) {
    loadMore.value = true;
    filterProductLoading.value = true;
    offset = offset != null ? offset : 0;

    limit = limit != null ? limit : 20;

    let categories: any[] = filterCategory.value ? [filterCategory.value] : [];

    shopService.searchProductsInShop(
        search_text.value,
        shopData.value.id,
        categories,
        limit,
        offset,
        productSortBy.value,
        null,
        props.mode != 'agent' ? true : false
    ).then(res => {
        listProduct.value = JSON.parse(JSON.stringify(res.body.data.result));
        countProducts.value = res.body.data.count;
        document.getElementById('list_product_container')?.scrollTo({ top: 0, behavior: 'smooth' })
        isRefreshing.value = false;
        loadMore.value = false;
        filterProductLoading.value = false;
    }).catch(err => {
        filterProductLoading.value = false;
        isRefreshing.value = false;
        loadMore.value = false;
    })
}
function searchProduct() {
    clearTimeout(searchProductTimeout);
    searchProductTimeout = setTimeout(() => {
        getShopProduct(0, 20);
    }, 500)
}
function productContainerScroll() {
    let el = document.getElementById('last_of_list')?.getBoundingClientRect().bottom;
    if (el && el <= window.innerHeight + 300) { loadMoreProduct(); }
}
function loadMoreProduct() {
    clearTimeout(loadMoreTimeOut);


    if (listProduct.value.length < countProducts.value) {
        loadMore.value = true;
        loadMoreTimeOut = setTimeout(() => {
            let categories = filterCategory.value ? [filterCategory.value] : [];
            shopService.searchProductsInShop(
                search_text.value,
                shopData.value.id,
                categories,
                20,
                listProduct.value.length,
                productSortBy.value,
                null,
                true
            ).then(res => {
                let list = [...listProduct.value, ...res.body.data.result];
                listProduct.value = list;
                loadMore.value = false;
            }).catch(err => {
                loadMore.value = false;
            })
        }, 500);

    }
    else {
        loadMore.value = false;
    }

}

function getListCategory() {
    categoryService.getCategoryByShopId(shopData.value.id).then(res => {
        dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data))
    })
}

function checkCategoryFilter(id: any) {
    if (id) {
        return filterCategory.value == id ? true : false
    }
    return false
}

function updateProductEnable(product: any, enable = true){
    let product$ = {
        ...product,
        enable: enable,
        category_ids: product.categories.map((e:any) => e.id)
    }
    if(props.mode != 'agent'){
        productService.updateProduct(product$);
    }
    else {
        agentService.agentUpdateProduct(product$);
    }
}
</script>

<style lang="scss" src="./ManageProductsStyles.scss"></style>
