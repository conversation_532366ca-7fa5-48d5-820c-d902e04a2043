# GitHub Copilot Workspace Guide

## 1. Coding Guidelines for Copilot
- **Reuse Existing Code:** Always try to base new code or features on similar, existing code within the same page/component/module if possible. Avoid writing everything from scratch if a similar pattern already exists.
- **Preserve Line Breaks:** Do not arbitrarily remove or collapse line breaks. This helps prevent accidental merging of code and comments, and keeps the codebase readable and maintainable.
- **Component Consistency:** When adding new features, follow the structure, naming, and style of similar components already present in the project.
- **Validation & Modals:** For configuration modals (like commission percent), follow the pattern of other modals (e.g., UpdatePendingTimeComponent) for UI, validation, and event handling.
- **SCSS Structure:** When editing SCSS, maintain the existing nesting and indentation. Do not remove or merge closing braces unless you are certain of the block structure.
- **No Need to Manually Build:** You do not need to run `npm run build` or change directories before development. The terminal is already at the codebase root, and `npm run dev` is used by default. The UI updates automatically with code changes.
- **Add All Languages for New Items:** When creating a new language item (e.g., a translation key), ensure you add it to all four current language files in `locales/`. Do not add a key to only one language—always update every language file to keep translations consistent.

## 2. System Overview
- **Framework:** Nuxt.js (Vue 3, TypeScript, VeeValidate, VueFinalModal, etc.)
- **Component Structure:** Modular, with each feature/configuration in its own folder and style file.
- **Styling:** SCSS modules, with one style file per component or feature.
- **Localization:** Uses `$t` for translations, with locale files in `locales/`.
- **Auto-imports:** Many components are auto-imported by Nuxt, so explicit imports are not always required.

## 3. Example: Modal/Config Pattern
- When adding a new config modal, copy the structure of an existing one (e.g., UpdatePendingTimeComponent) for:
  - Modal wrapper (VueFinalModal)
  - Form validation (VeeForm, yup)
  - Event emit/close/submit
  - SCSS structure

## 4. General Advice
- **Do not remove or merge blank lines.**
- **Do not remove comments unless refactoring the code they refer to.**
- **Always check for similar code before implementing new features.**
- **Keep code readable and maintainable.**
- **authCheck = await authService.checkAuth()**

## 5. Codebase Logic & Structure Overview

### Main Folders & Roles
- **components/**: Chứa các component Vue, tổ chức theo từng tính năng hoặc page. Mỗi tính năng lớn thường có 1 thư mục riêng, bên trong có các component con và file style SCSS tương ứng.
- **pages/**: Chứa các page chính của Nuxt, mỗi file hoặc thư mục là một route.
- **services/**: Chứa các service xử lý API, business logic, hoặc thao tác dữ liệu (thường là class hoặc function).
- **assets/**: Chứa các file tĩnh, hằng số, DTO, hình ảnh, SCSS dùng chung.
- **locales/**: Chứa các file json cho đa ngôn ngữ, dùng với `$t` trong Vue.
- **plugins/**: Các plugin khởi tạo cho Nuxt (middleware, thư viện ngoài, v.v.).
- **composables/**: Các function composition API dùng lại nhiều nơi (nếu có).

### Luồng Dữ Liệu & Logic Chính
- **API Call**: Thực hiện qua các service trong `services/`, trả về dữ liệu cho component hoặc lưu vào state.
- **State/Config**: Thường lưu trực tiếp trong component (dùng ref/reactive), hoặc truyền qua props/emits giữa các component cha-con. Không dùng Vuex/Pinia mặc định (nếu có sẽ có hướng dẫn riêng).
- **Component Communication**: Sử dụng props, emits, hoặc inject/provide nếu cần chia sẻ sâu.
- **Modal/Config Pattern**: Khi cần sửa cấu hình, luôn tạo component modal riêng, truyền dữ liệu qua props và nhận kết quả qua emit (xem ví dụ UpdatePendingTimeComponent).

### Quy Tắc Đặt Tên & Tổ Chức
- **Component**: PascalCase, đặt theo chức năng (ví dụ: UpdateCommissionPercentComponent.vue)
- **SCSS**: Đặt cùng thư mục với component, tên dạng [ComponentName]Styles.scss
- **Service**: PascalCase, kết thúc bằng Service (ShopService, UserService...)
- **DTO/Model**: PascalCase, kết thúc bằng DTO hoặc Model nếu là kiểu dữ liệu.

### Lưu Ý Khác
- Khi thêm mới tính năng, hãy tìm thư mục/tệp tương ứng để đặt code đúng chỗ.
- Ưu tiên tái sử dụng logic, style, và cấu trúc đã có.
- Nếu cần chia sẻ logic giữa nhiều component, cân nhắc tạo composable hoặc service.
- sử dụng Loading display chung cho tất cả các component:
  '<div class="d-flex justify-center align-center" style="min-height: 100vh;">
    <v-progress-circular indeterminate size="64" color="primary"></v-progress-circular>
  </div>'


---
This file is for GitHub Copilot and other AI tools to read before making code changes in this workspace.
