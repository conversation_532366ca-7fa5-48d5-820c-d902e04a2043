<template>
  <div class="stock-export-component">
    <!-- Sub Header -->
    <SubHeaderV2Component
      :title="$t('StockManagement.xuat_kho')"
    >
      <template #header_left>
        <button class="back-button" @click="router.push(`/agent/shop/${shopSlug}/stock`)">
          <Icon name="solar:round-alt-arrow-left-linear"></Icon>
        </button>
      </template>
    </SubHeaderV2Component>

    <!-- Header -->
    <div class="component-header">
      <p>{{ $t('StockManagement.ghi_nhan_hang_xuat') }}</p>
    </div>

    <!-- Export Form -->
    <form @submit.prevent="submitExport" class="export-form">
      <!-- Product Selection -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.chon_san_pham') }} *</label>
        <div class="product-selector" @click="showProductPicker = true">
          <div v-if="selectedProduct" class="selected-product">
            <img :src="selectedProduct.image || '/default-product.png'" :alt="selectedProduct.name" class="product-image">
            <div class="product-info">
              <h4>{{ selectedProduct.name }}</h4>
              <p>{{ selectedProduct.category }}</p>
              <span class="stock-level" :class="getStockLevelClass(selectedProduct.stock)">
                {{ $t('StockManagement.ton_kho_hien_tai') }}: {{ selectedProduct.stock || 0 }} {{ selectedProduct.unit }}
              </span>
            </div>
          </div>
          <div v-else class="placeholder">
            <Icon name="solar:box-bold" size="24" />
            <span>{{ $t('StockManagement.chon_san_pham') }}</span>
          </div>
          <Icon name="solar:arrow-right-bold" size="20" class="arrow-icon" />
        </div>
      </div>

      <!-- Quantity -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.so_luong') }} *</label>
        <div class="quantity-input">
          <input
            v-model.number="formData.quantity"
            type="number"
            :placeholder="$t('StockManagement.nhap_so_luong')"
            :max="selectedProduct?.stock || 999999"
            min="1"
            step="1"
            required
            class="form-input"
            @input="validateQuantity"
          />
          <span class="unit-label">{{ selectedProduct?.unit || $t('StockManagement.don_vi') }}</span>
        </div>
        <div v-if="quantityError" class="error-message">
          {{ quantityError }}
        </div>
      </div>

      <!-- Sale Price -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.gia_ban') }} *</label>
        <div class="price-input">
          <input
            v-model.number="formData.sale_price"
            type="number"
            :placeholder="$t('StockManagement.nhap_gia_ban')"
            min="0"
            step="1000"
            required
            class="form-input"
          />
          <span class="currency-label">VND</span>
        </div>
      </div>

      <!-- Order ID (Optional) -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.ma_don_hang') }}</label>
        <div class="order-selector" @click="showOrderPicker = true">
          <div v-if="selectedOrder" class="selected-order">
            <div class="order-info">
              <h4>#{{ selectedOrder.id }}</h4>
              <p>{{ selectedOrder.customer_name }}</p>
              <span class="order-total">{{ formatCurrency(selectedOrder.total_amount) }}</span>
            </div>
          </div>
          <div v-else class="placeholder">
            <Icon name="solar:document-bold" size="24" />
            <span>{{ $t('StockManagement.chon_don_hang') }}</span>
          </div>
          <Icon name="solar:arrow-right-bold" size="20" class="arrow-icon" />
        </div>
      </div>

      <!-- Customer Info (Optional) -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.thong_tin_khach_hang') }}</label>
        <input
          v-model="formData.customer_info"
          type="text"
          :placeholder="$t('StockManagement.ten_khach_hang')"
          class="form-input"
        />
      </div>

      <!-- Notes -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.ghi_chu') }}</label>
        <textarea
          v-model="formData.notes"
          :placeholder="$t('StockManagement.them_ghi_chu')"
          rows="3"
          class="form-textarea"
        ></textarea>
      </div>

      <!-- Action Buttons -->
      <div class="form-actions">
        <button type="button" @click="router.push(`/agent/shop/${shopSlug}/stock`)" class="btn btn-secondary">
          {{ $t('StockManagement.huy_bo') }}
        </button>
        <button type="submit" :disabled="!isFormValid || loading" class="btn btn-primary">
          <Icon v-if="loading" name="solar:refresh-bold" size="20" class="loading-icon" />
          {{ $t('StockManagement.luu_thong_tin') }}
        </button>
      </div>
    </form>

    <!-- Product Picker Modal -->
    <ProductPickerModal
      v-if="showProductPicker"
      :shop-id="shopId"
      @close="showProductPicker = false"
      @select="selectProduct"
    />

    <!-- Order Picker Modal -->
    <OrderPickerModal
      v-if="showOrderPicker"
      :shop-id="shopId"
      @close="showOrderPicker = false"
      @select="selectOrder"
    />

    <!-- Success Toast -->
    <div v-if="showSuccessToast" class="success-toast">
      <Icon name="solar:check-circle-bold" size="24" />
      <span>{{ $t('StockManagement.cap_nhat_thanh_cong') }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import { MqttService } from '~/services/mqttService/mqttService'
import ProductPickerModal from './ProductPickerModal.vue'
import OrderPickerModal from './OrderPickerModal.vue'
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue'
import { HttpStatusCode } from "axios";

// Props
const props = defineProps<{
  mode?: string
}>()

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Use the centralized shop data composable
const {
  activeShop,
  isLoading: shopDataLoading,
  initializeShopData,
  getShopBySlug
} = useShopData()

// Services
const stockService = new StockService()
const mqttService = new MqttService()

// Reactive data
const loading = ref(false)
const showProductPicker = ref(false)
const showOrderPicker = ref(false)
const showSuccessToast = ref(false)
// Define types for products and orders
interface Product {
  id: string
  name: string
  image?: string
  category?: string
  stock: number
  unit: string
  sale_price?: number
}

interface Order {
  id: string
  customer_name: string
  total_amount: number
}

// Reactive states
const selectedProduct = ref<Product | null>(null)
const selectedOrder = ref<Order | null>(null)
const quantityError = ref('')

const formData = ref({
  product_id: '',
  quantity: null,
  sale_price: null,
  order_id: '',
  customer_info: '',
  notes: ''
})

// Computed properties
const isFormValid = computed(() => {
  return selectedProduct.value && 
         formData.value.quantity !== null &&
         formData.value.sale_price !== null &&
         formData.value.quantity > 0 && 
         formData.value.sale_price > 0 &&
         !quantityError.value
})

const shopSlug = route.params.id as string
const shopId = computed(() => {
  const shop = getShopBySlug(shopSlug)
  return shop?.id || shopSlug
})

// Navigation method
const goBackToStockDashboard = (mode?: string) => {
  if (mode === 'v2') {
    router.push(`/my-shop/${shopSlug}/dashboard`)
  } else {
    router.push(`/my-shop/${shopSlug}/stock`)
  }
}

// Methods
const selectProduct = (product: any) => {
  selectedProduct.value = product
  formData.value.product_id = product.id
  showProductPicker.value = false
  validateQuantity() // Revalidate quantity when product changes
}

const selectOrder = (order: any) => {
  selectedOrder.value = order
  formData.value.order_id = order.id.toString()
  showOrderPicker.value = false
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount || 0)
}

const getStockLevelClass = (stock: number) => {
  if (stock <= 0) return 'stock-empty'
  if (stock <= 10) return 'stock-low'
  return 'stock-normal'
}

const validateQuantity = () => {
  quantityError.value = ''

  if (!selectedProduct.value || !formData.value.quantity) return

  const currentStock = selectedProduct.value.stock || 0
  const requestedQuantity = formData.value.quantity

  if (requestedQuantity > currentStock) {
    quantityError.value = t('StockManagement.so_luong_vuot_qua_ton_kho', {
      available: currentStock,
      unit: selectedProduct.value.unit
    })
  }
}

const submitExport = async () => {
  if (!isFormValid.value) return

  loading.value = true
  try {
    const exportData = {
      ...formData.value,
      shop_id: shopId.value
    }

    const response = await stockService.stockExport(exportData)
    if (response.status == HttpStatusCode.Ok) {
      // Show success message
      showSuccessToast.value = true
      setTimeout(() => {
        showSuccessToast.value = false
      }, 3000)

      // Publish MQTT update
      mqttService.publish(`stock_updates_${shopId.value}`, JSON.stringify({
        type: 'stock_export_complete',
        data: {
          product_id: formData.value.product_id,
          quantity: formData.value.quantity,
          timestamp: new Date().toISOString()
        }
      }))

      // Reset form
      resetForm()
    } else {
      throw new Error(response.message || 'Export failed')
    }
  } catch (error) {
    console.error('Stock export error:', error)
    // alert(t('StockManagement.loi_cap_nhat'))
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  selectedProduct.value = null
  selectedOrder.value = null
  quantityError.value = ''
  formData.value = {
    product_id: '',
    quantity: null,
    sale_price: null,
    order_id: '',
    customer_info: '',
    notes: ''
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize shop data first
  await initializeShopData()
})
</script>

<style scoped>
.stock-export-component {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750px;
}

.component-header {
  margin-bottom: 20px;
  text-align: center;

  h1 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
  }

  p {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.export-form {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.product-selector {
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    border-color: #3498db;
  }

  .selected-product {
    display: flex;
    align-items: center;
    gap: 12px;

    .product-image {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      object-fit: cover;
    }

    .product-info {
      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0 0 4px 0;
      }

      .stock-level {
        font-size: 11px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;

        &.stock-normal {
          background: #d5f4e6;
          color: #27ae60;
        }

        &.stock-low {
          background: #fef9e7;
          color: #f39c12;
        }

        &.stock-empty {
          background: #fadbd8;
          color: #e74c3c;
        }
      }
    }
  }

  .placeholder {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #7f8c8d;

    span {
      font-size: 14px;
    }
  }

  .arrow-icon {
    color: #bdc3c7;
  }
}

.order-selector {
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    border-color: #3498db;
  }

  .selected-order {
    display: flex;
    align-items: center;
    gap: 12px;

    .order-info {
      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0 0 4px 0;
      }

      .order-total {
        font-size: 11px;
        font-weight: 600;
        color: #27ae60;
      }
    }
  }

  .placeholder {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #7f8c8d;

    span {
      font-size: 14px;
    }
  }

  .arrow-icon {
    color: #bdc3c7;
  }
}

.quantity-input,
.price-input {
  display: flex;
  align-items: center;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  overflow: hidden;

  .form-input {
    flex: 1;
    border: none;
    padding: 12px 15px;
    font-size: 14px;
    outline: none;

    &:focus {
      background-color: #f8f9fa;
    }
  }

  .unit-label,
  .currency-label {
    background-color: #ecf0f1;
    padding: 12px 15px;
    font-size: 14px;
    font-weight: 600;
    color: #7f8c8d;
  }
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    background-color: #f8f9fa;
  }
}

.form-textarea {
  width: 100%;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    background-color: #f8f9fa;
  }
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;

  .btn {
    flex: 1;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &.btn-secondary {
      background: #ecf0f1;
      color: #7f8c8d;
      border: none;

      &:hover {
        background: #d5dbdb;
      }
    }

    &.btn-primary {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      border: none;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.success-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #27ae60;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@media (max-width: 480px) {
  .stock-export-component {
    padding: 10px;
  }

  .export-form {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
