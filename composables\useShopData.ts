import { ref, computed, readonly } from 'vue';
import { useRoute } from 'vue-router';
import { appConst } from '~/assets/AppConst';
import { AgentService } from '~/services/agentService/agentService';
import { ShopService } from '~/services/shopService/shopService';
import { HttpStatusCode } from 'axios';

export function useShopData() {
  const route = useRoute();
  const agentService = new AgentService();
  const shopService = new ShopService();

  // Reactive state
  const shopList = ref<any[]>([]);
  const myShopData = ref<any>(null);
  const isLoading = ref(false);
  const isRefreshing = ref(false);

  /**
   * Get the current shop slug from the route
   */
  const currentShopSlug = computed(() => {
    return route.params.slug || route.params.id;
  });

  /**
   * Get the active shop data based on the current shop slug
   */
  const activeShop = computed(() => {
    if (!currentShopSlug.value || !shopList.value.length) {
      return null;
    }

    // Find shop by slug or id
    return shopList.value.find(shop => 
      shop.slug === currentShopSlug.value || 
      shop.id === currentShopSlug.value
    ) || null;
  });

  /**
   * Load shop list from localStorage
   */
  const loadFromCache = (): any[] => {
    if (process.client) {
      try {
        const cached = localStorage.getItem(appConst.storageKey.shopList);
        return cached ? JSON.parse(cached) : [];
      } catch (error) {
        console.warn('Failed to load shop list from localStorage:', error);
        return [];
      }
    }
    return [];
  };

  /**
   * Save shop list to localStorage
   */
  const saveToCache = (shops: any[]) => {
    if (process.client) {
      try {
        localStorage.setItem(appConst.storageKey.shopList, JSON.stringify(shops));
      } catch (error) {
        console.warn('Failed to save shop list to localStorage:', error);
      }
    }
  };

  /**
   * Get user's own shop data
   */
  const getMyShop = async (): Promise<any> => {
    console.log('getMyShop');
    return new Promise((resolve) => {
      if (myShopData.value?.id) {
        resolve(myShopData.value);
      } else {
        // Try to load from cache first
        if (process.client) {
          try {
            const cached = localStorage.getItem(appConst.storageKey.myShop);
            if (cached) {
              const cachedMyShop = JSON.parse(cached);
              if (cachedMyShop?.id) {
                myShopData.value = cachedMyShop;
                resolve(myShopData.value);
                return;
              }
            }
          } catch (error) {
            console.warn('Failed to load myShop data from localStorage:', error);
          }
        }
        
        // Fetch from API if not in cache
        shopService.myShop().then(res => {
          if (res.status === HttpStatusCode.Ok && res.body?.data) {
            myShopData.value = JSON.parse(JSON.stringify(res.body.data));
            // Save to cache after API request
            if (process.client) {
              try {
                localStorage.setItem(appConst.storageKey.myShop, JSON.stringify(myShopData.value));
              } catch (error) {
                console.warn('Failed to save myShop data to localStorage:', error);
              }
            }
            resolve(myShopData.value);
          } else {
            myShopData.value = null;
            resolve(null);
          }
        }).catch(() => {
          myShopData.value = null;
          resolve(null);
        });
      }
    });
  };

  /**
   * Fetch shop list from API
   */
  const fetchShopList = async (): Promise<any[]> => {
    try {
      const res = await agentService.listShopManage('', 100, 0);
      
      if (res.status === HttpStatusCode.Ok) {
        let shops = JSON.parse(JSON.stringify(res.body.data));
        
        // Get user's own shop and add it to the list if it exists
        const myShop = await getMyShop();
        if (myShop?.id) {
          // Remove myShop from the list if it already exists to avoid duplicates
          shops = shops.filter((shop: any) => shop.id !== myShop.id);
          // Add myShop at the beginning
          shops = [myShop, ...shops];
        }
        
        return shops;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch shop list:', error);
      return [];
    }
  };

  /**
   * Initialize shop data - check cache first, then fetch if needed
   */
  const initializeShopData = async () => {
    if (isLoading.value) return;
    
    isLoading.value = true;
    getMyShop();
    try {
      // First, try to load from cache
      const cachedShops = loadFromCache();
      
      if (cachedShops.length > 0) {
        shopList.value = cachedShops;
      } else {
        // Cache is empty, fetch from API
        const shops = await fetchShopList();
        shopList.value = shops;
        saveToCache(shops);
      }
    } catch (error) {
      console.error('Failed to initialize shop data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Refresh shop data - clear cache and fetch fresh data
   */
  const refreshShops = async () => {
    if (isRefreshing.value) return;
    
    isRefreshing.value = true;
    
    try {
      // Clear cache
      if (process.client) {
        myShopData.value = null;
        localStorage.removeItem(appConst.storageKey.shopList);
        localStorage.removeItem(appConst.storageKey.myShop);
      }
      
      // Fetch fresh data
      const shops = await fetchShopList();
      shopList.value = shops;
      saveToCache(shops);
      getMyShop();
    } catch (error) {
      console.error('Failed to refresh shop data:', error);
    } finally {
      isRefreshing.value = false;
    }
  };

  /**
   * Get shop by slug or id
   */
  const getShopBySlug = (slug: string) => {
    return shopList.value.find(shop => 
      shop.slug === slug || shop.id === slug
    ) || null;
  };

  /**
   * Check if shops are loaded
   */
  const isShopsLoaded = computed(() => {
    return shopList.value.length > 0;
  });

  return {
    // State
    shopList: readonly(shopList),
    myShopData: readonly(myShopData),
    isLoading: readonly(isLoading),
    isRefreshing: readonly(isRefreshing),
    
    // Computed
    currentShopSlug,
    activeShop,
    isShopsLoaded,
    
    // Methods
    initializeShopData,
    refreshShops,
    getShopBySlug,
    getMyShop
  };
}
