<template>
	<div class="public-container">
		<div class="create-private-product-container">
			<!-- <div class='title-header'>
				<div class="header-left">
				</div>
				<h3>{{ appRouteTitle.CreatePrivateProductComponent }}</h3>
				<div class="header-right"></div>
			</div> -->
			<SubHeaderV2Component :title="$t('AppRouteTitle.CreatePrivateProductComponent')">
			</SubHeaderV2Component>
			<!-- Loading state for refreshing data -->
			<div class="loading-container" v-if="isRefreshing">
				<div class="d-flex justify-center align-center" style="min-height: 100vh;">
					<v-progress-circular indeterminate size="64" color="primary"></v-progress-circular>
				</div>
			</div>
			
			<!-- Loading state for initial shop data fetch -->
			<div class="loading-container" v-else-if="!shopData && isInitialLoading">
				<div class="d-flex justify-center align-center" style="min-height: 100vh;">
					<v-progress-circular indeterminate size="64" color="primary"></v-progress-circular>
				</div>
			</div>
			
			<!-- Main content when shop data is available -->
			<div class="product-content-container" v-else-if="shopData" v-show="!isRefreshing">
				<div class="v-stack product-content avatar">
					<div class="content-header">
						<span class='label'>
							{{ $t('CreatePrivateProductComponent.anh_san_pham') }} <em>({{ listImagesEdit.length }}/{{
								appConst.productImageMaxAmount }})</em>
						</span>
					</div>

					<VueDraggableNext :multi-drag="false" handle=".handle-drag" :animation="300" ghost-class="ghost"
						opacity="1" drag-class="dragging" class="product-image-list" :list="listImagesEdit"
						v-on:change="(e: any) => { }">
						<div class="selected-image" v-for="(itemImg, index) in listImagesEdit">
							<img :src="itemImg.path" class="handle-drag" />
							<div class="action-overlay">
								<button class="delete-image" v-on:click="() => {
									listImagesEdit.splice(index, 1)
								}">
									<Icon name="bi:trash3-fill"></Icon>
								</button>
							</div>
						</div>
						<div class="select-image" v-if="listImagesEdit.length < appConst.productImageMaxAmount">
							<label>
								<Icon name="ion:plus-round"></Icon>
								<input type="file" accept='image/*' :multiple="true"
									:maxLengh="appConst.productImageMaxAmount - listImagesEdit.length" v-on:change="($event: any) => {
										fileChangeInput($event)
									}" ref="imageFilesName" />
							</label>
						</div>

					</VueDraggableNext>
				</div>
				<div class="language-options">
					<!-- <button class="lang-button" :class="{ 'active': !selectLanguage }" v-on:click="() => {
					selectLanguage = null
					}">{{ $t('CreatePrivateProductComponent.mac_dinh') }}</button> -->
					<button class="lang-button" v-for="item in shopData?.language"
						:class="{ 'active': selectLanguage == item }" v-on:click="() => {
							selectLanguage = item;
						}">
						{{ ISO6391.getNativeName(item) }}
					</button>
				</div>
				<div class="v-stack product-content">
					<span class='label required'>
						{{ $t('CreatePrivateProductComponent.ten_san_pham') }} {{ selectLanguage ?
							`(${ISO6391.getName(selectLanguage)})` :
							`` }}
					</span>
					
					<div class="text-input-container">
						<input v-for="item in shopData?.language" v-show="item == selectLanguage"
							:title="$t('CreatePrivateProductComponent.ten_san_pham')" name='product-name'
							:maxlength="appConst.max_text_short" :disabled="isSaving" class='input-custom' 
							:placeholder="`${$t('CreatePrivateProductComponent.ten_san_pham')} - ${ISO6391.getName(item)}`"
							:value="getNameTranslate(selectLanguage) || ''" 
							v-on:input="async ($event: any) => {
								await nameProductChange($event.target.value, selectLanguage);
								validateName()
							}" />
						<button
							class="speech-button"
							:class="{ recording: isRecording }"
							@click="toggleSpeechRecognition"
							:disabled="isSaving"
							:title="isRecording ? 'Stop recording' : 'Start speech to text'"
						>
							<Icon name="mdi:microphone" size="20" v-if="!isRecording" />
							<Icon name="mdi:stop" size="20" v-else />
						</button>
					</div>
					
					<div class="text-input-container">
						<input v-show="!shopData?.language?.length || !selectLanguage"
							:title="$t('CreatePrivateProductComponent.ten_san_pham')" name='product-name'
							:maxlength="appConst.max_text_short" :disabled="isSaving" class='input-custom'
							:placeholder="$t('CreatePrivateProductComponent.ten_san_pham')" 
							:value="productData.name || ''"
							v-on:input="($event: any) => {
								productData = {
									...productData,
									name: $event.currentTarget.value
								}
								validateName();
							}" v-on:blur="() => {
								validateName();
							}" />
						<button
							v-show="!shopData?.language?.length || !selectLanguage"
							class="speech-button"
							:class="{ recording: isRecording }"
							@click="toggleSpeechRecognition"
							:disabled="isSaving"
							:title="isRecording ? 'Stop recording' : 'Start speech to text'"
						>
							<Icon name="mdi:microphone" size="30" v-if="!isRecording" />
							<Icon name="mdi:stop" size="30" v-else />
						</button>
					</div>
					<span class='error-message'>{{ nameErr }}</span>
				</div>
				<div class="v-stack product-content">
					<span class='label'>
						{{ $t('CreatePrivateProductComponent.gia') }}
					</span>
					<client-only>
						<input type="text" inputmode="numeric"
							:placeholder="$t('CreatePrivateProductComponent.de_trong_neu_gia_can_lien_he')" v-on:input="($event: any) => {
								productData.price = parseDecimal2Digits(productData.price?.toString());
								validatePrice()
							}" v-model="productData.price" class="input-custom price-input" max=1000000000000>
					</client-only>

					<span class="price-text" v-if="productData.price && validDecimal2Digits(productData.price?.toString())">{{
						formatCurrency(parseDecimal2Digits(productData.price?.toString()),
							shopData.currency)
					}}</span>
					<span class='error-message' v-if="priceErr">{{ priceErr }}</span>
				</div>
				
				<!-- Global Category Selection -->
				<div class="v-stack product-content">
					<span class='label'>
						{{ $t('CreatePrivateProductComponent.category_global') }}
					</span>
					<client-only>
						<button
							class="global-category-selector mt-2"
							@click="openCategoryModal"
							:disabled="!isGlobalCategoriesLoaded">
							<div class="selection-display">
								<span v-if="selectedGlobalCategory" class="selected-text">
									{{ selectedGlobalCategory.category_path || selectedGlobalCategory.name }}
									<span v-if="!selectedGlobalCategory.public" class="private-note">
										({{ $t('CreatePrivateProductComponent.chi_hien_thi_noi_bo') }})
									</span>
								</span>
								<span v-else-if="!isGlobalCategoriesLoaded" class="placeholder">
									{{ $t('CreatePrivateProductComponent.dang_tai_danh_muc') }}
								</span>
								<span v-else class="placeholder">
									{{ $t('CreatePrivateProductComponent.chon_danh_muc_global') }}
								</span>
							</div>
							<Icon name="mdi:chevron-down" class="dropdown-icon" />
						</button>
					</client-only>
				</div>

				<!-- Toggle Button cho thông tin nâng cao -->
				<div class="advanced-toggle-section">
					<button class="toggle-advanced-btn" @click="showAdvancedInfo = !showAdvancedInfo">
						<Icon :name="showAdvancedInfo ? 'mdi:chevron-up' : 'mdi:chevron-down'"></Icon>
						{{ $t('CreatePrivateProductComponent.advanced_info')}}
					</button>
				</div>
				
				<!-- Vùng chứa thông tin nâng cao -->
				<div class="advanced-info-section" v-show="showAdvancedInfo">
					
					<div class="v-stack product-content">
						<span class='label'>
							{{ $t('CreatePrivateProductComponent.unit') }}
						</span>
						<datalist id="unit-suggestions">
							<option v-for="unit in appConst.product_unit" :key="unit" :value="unit">{{ unit }}</option>
						</datalist>
						<input v-model="productData.unit" type="text" class="input-custom" :maxlength="appConst.max_text_short"
								:placeholder="$t('CreatePrivateProductComponent.unit')" list="unit-suggestions" />
					</div>
					<div class="v-stack product-content">
						<span class='label'>
							{{ $t('CreatePrivateProductComponent.mo_ta_san_pham') }} {{ selectLanguage ?
								`(${ISO6391.getName(selectLanguage)})` :
								`` }}
						</span>
						<textarea v-for="item in shopData?.language" v-show="item == selectLanguage"
							:title="$t('CreatePrivateProductComponent.mo_ta_san_pham')" name='product-notes'
							:maxlength="appConst.max_text_long" class='text-area-custom'
							:placeholder="`${$t('CreatePrivateProductComponent.mo_ta_san_pham')} - ${ISO6391.getName(item)}`"
							v-on:input="($event: any) => {
								noteProductChange($event.target.value, selectLanguage)
							}" :value="getNoteTranslate(item) || ''"></textarea>
						<textarea v-show="!shopData?.language?.length || !selectLanguage"
							:title="$t('CreatePrivateProductComponent.mo_ta_san_pham')" name='product-notes'
							:maxlength="appConst.max_text_long" class='text-area-custom'
							:placeholder="`${$t('CreatePrivateProductComponent.mo_ta_san_pham')} (${selectLanguage ? ISO6391.getName(selectLanguage) : ``})`"
							v-model="productData.notes"></textarea>
					</div>
					

					<div class="v-stack product-content">
						<div class="content-header">
							<span class='label'>
								{{ $t('CreatePrivateProductComponent.danh_muc') }}
							</span>
							<button class="add-category" v-on:click="() => {
								showCreateCategoryModal = true;
							}">
								<Icon name="material-symbols:add-circle-outline-rounded"></Icon> {{
									$t('CreatePrivateProductComponent.tao_danh_muc') }}
							</button>
						</div>

						<client-only>
							<v-autocomplete :custom-filter="(item: any, queryText: any, itemObj: any) => {
								let name = nonAccentVietnamese(itemObj.value.name).toLocaleLowerCase();
								let query = nonAccentVietnamese(queryText).toLocaleLowerCase();
								return name.includes(query)
							}" class="custom-v-select mt-2" clearable chips label="" focused closable-chips clear-on-select
								:placeholder="$t('CreatePrivateProductComponent.chon_danh_muc_san_pham')"
								v-model="selectedCategories" v-on:update:modelValue="() => {
									productData.category_ids = selectedCategories.map(e => e.id);
								}" :menu-icon="''" return-object :items="dataShopCategories" multiple variant="plain">
								<template v-slot:item="{ props, item }">
									<v-list-item v-bind="props" :title="item.value.name"></v-list-item>
								</template>
								<template v-slot:chip="{ props, item }">
									<v-chip v-bind="props" class="chip">{{ item.value.name }}</v-chip>
								</template>
								<template v-slot:no-data>
									<v-list-item
										:title="$t('CreatePrivateProductComponent.khong_tim_thay_danh_muc')"></v-list-item>
								</template>
								<template v-slot:append-inner>
									<Icon name="mdi:chevron-down"></Icon>
								</template>
							</v-autocomplete>
						</client-only>
					</div>
					<div class='h-stack product-content'>
						<label class='checkbox-input-label' v-on:click="() => {
							productData = {
								...productData,
								is_feature: !productData.is_feature
							}
						}"><Icon name="mdi:checkbox-marked-circle-outline" v-if="productData.is_feature"></Icon>
							<Icon name="mdi:checkbox-blank-circle-outline" v-if="!productData.is_feature"></Icon>
							<span>
								{{ $t('CreatePrivateProductComponent.dac_trung') }}
							</span>
							<em>({{ $t('CreatePrivateProductComponent.uu_tien_hien_thi') }})</em>
						</label>
					</div>
					<div class="v-stack product-content">
						<span class='label'>
							{{ $t('CreatePrivateProductComponent.gia_uu_dai') }}
						</span>
						<client-only>
							<input type="text" inputmode="numeric"
								:placeholder="$t('CreatePrivateProductComponent.de_trong_neu_khong_giam_gia')"
								v-model="productData.price_off" v-on:input="($event: any) => {
									validatePriceOff()
								}" class="input-custom price-input" max=1000000000000>
						</client-only>

						<span class="price-text" v-if="productData.price_off && validDecimal2Digits(productData.price_off?.toString())">{{
							formatCurrency(parseDecimal2Digits(productData.price_off?.toString()),
								shopData.currency) }}</span>
						<span class='error-message' v-if="priceOffErr">{{ priceOffErr }}</span>
					</div>
					<!-- Mini Toggle cho Nhập kho -->
					<div class="inventory-toggle-section">
						<button class="mini-toggle-btn" @click="showInventoryInput = !showInventoryInput">
							<Icon :name="showInventoryInput ? 'mdi:minus-circle' : 'mdi:plus-circle'"></Icon>
							{{ $t('StockManagement.nhap_kho') }}
							<Icon :name="showInventoryInput ? 'mdi:chevron-up' : 'mdi:chevron-down'" class="chevron-icon"></Icon>
						</button>
					</div>

					<!-- Expanded Inventory Fields -->
					<div class="inventory-expanded-section" v-show="showInventoryInput">
						<!-- Số lượng nhập kho -->
						<div class="v-stack product-content">
							<span class='label'>
								{{ $t('StockManagement.so_luong') }}
							</span>
							<input type="text" :disabled="isSaving" v-model="productData.stock" max="1000000000000"
								inputmode="numeric"
								:placeholder="$t('StockManagement.nhap_kho')"
								class="input-custom price-input" v-on:input="($event: any) => {
									validateStock()
								}">
							<span class='error-message' v-if="stockErr">{{ stockErr }}</span>
						</div>

						<!-- Giá nhập -->
						<div class="v-stack product-content">
							<span class='label'>
								{{ $t('StockManagement.gia_nhap') }}
							</span>
							<client-only>
								<input type="text" inputmode="numeric"
									:placeholder="$t('StockManagement.gia_nhap')"
									v-model="productData.price_root" v-on:input="($event: any) => {
										validatePriceRoot()
									}" class="input-custom price-input" max=1000000000000>
							</client-only>

							<span class="price-text" v-if="productData.price_root && validDecimal2Digits(productData.price_root?.toString())">{{
								formatCurrency(parseDecimal2Digits(productData.price_root?.toString()),
									shopData.currency) }}</span>
							<span class='error-message' v-if="priceRootErr">{{ priceRootErr }}</span>
						</div>
					</div>

					<!-- Commission Percent -->
					<div class="v-stack product-content" v-if="shopData?.settings.general?.commission_percent?.enabled == true">
						<span class='label'>
							{{ $t('EditProductComponent.commission_percent') }}
						</span>
						<client-only>						
							<input type="text" inputmode="numeric"
								:placeholder="$t('EditProductComponent.de_trong_neu_dung_commission_cua_shop') + ' (' + shopData.settings.general.commission_percent.value + '%)'" 
								v-on:input="($event: any) => {
									const inputValue = $event.target.value?.trim();
									if (inputValue === '' || inputValue === null || inputValue === undefined) {
										productData.commission_percent = null;
									} else {
										const parsedString = parseDecimal2Digits(inputValue);
										productData.commission_percent = parsedString ? parseFloat(parsedString) : null;
									}
									validateCommissionPercent()
								}" v-model="productData.commission_percent" class="input-custom price-input" max="100">
						</client-only>

						<span class="price-text" v-if="productData.commission_percent && validDecimal2Digits(productData.commission_percent?.toString())">{{
							parseDecimal2Digits(productData.commission_percent?.toString())
						}}%</span>
						<span class='error-message' v-if="commissionPercentErr">{{ commissionPercentErr }}</span>
					</div>

					<!-- Product Enable -->
					<!-- <div class='h-stack product-content'>
						<label class='checkbox-input-label' v-on:click="() => {
							productData = {
								...productData,
								enable: !productData.enable
							}
						}">
							<Icon name="mdi:checkbox-marked-outline" v-if="productData.enable" />
							<Icon name="mdi:checkbox-blank-outline" v-else />
							<span>
								{{ $t('EditProductComponent.hien_thi_san_pham') }}
							</span>
						</label>
					</div> -->
				</div> <!-- Đóng advanced-info-section -->

				<div class='h-stack form-actions'>
					<button class='cancel-button' :disabled="isSaving" v-on:click="() => {
						// close(false)
						showCancelCreateModal = true;
					}">
						{{ $t('CreatePrivateProductComponent.thoat') }}
					</button>
					<button class='save-button' :disabled="isSaving" v-on:click="() => {
						if (mode != 'agent') {
							createPrivateProduct()
						}
						else {
							agentCreatePrivateProduct()
						}
					}">
						<span v-show="!isSaving">{{ $t('CreatePrivateProductComponent.tao') }}</span>
						<Icon name="eos-icons:loading" size="20" v-show="isSaving" />
					</button>
				</div>
			</div>
			<!-- No shop component when loading is complete but no shop data -->
			<NoneMyShopComponent v-else v-show="!isRefreshing" :show_header="false" :mode="props.mode">	
			</NoneMyShopComponent>
			<VueFinalModal class="my-modal-container" content-class="v-stack cancel-create-container"
				:overlay-behavior="'persist'" v-model="showCancelCreateModal" v-on:closed="() => {
					showCancelCreateModal = false
				}" contentTransition="vfm-fade">
				<div class='v-stack delete-category-content'>
					<span class='delete-category-title'>
						{{ $t('CreatePrivateProductComponent.tieu_de') }}
					</span>

					<span class='delete-category-message'>
						{{ $t('CreatePrivateProductComponent.thong_bao') }}
					</span>
				</div>
				<div class='h-stack confirm-modal-buttons'>
					<button class='reject-button' v-on:click="() => {
						showCancelCreateModal = false;
					}">
						{{ $t('CreatePrivateProductComponent.o_lai') }}
					</button>
					<button class='accept-button' v-on:click="() => {
						close();
						showCancelCreateModal = false;
					}">
						{{ $t('CreatePrivateProductComponent.thoat') }}
					</button>
				</div>
			</VueFinalModal>

			<VueFinalModal class="my-modal-container" content-class="v-stack category-content-container"
				:overlay-behavior="'persist'" v-model="showCreateCategoryModal" v-on:closed="() => {
					showCreateCategoryModal = false
				}" contentTransition="vfm-fade">
				<CreateCategoryComponent :shopId="shopData.id" :shop_data="JSON.parse(JSON.stringify(shopData))"
					:dataCategories="dataShopCategories" v-on:close="(obj: any) => {
						showCreateCategoryModal = false;
						// useNuxtApp().$emit('refresh_category_manage')
						if (obj?.id) {
							productData.category_ids.push(obj?.id);
							getListCategory();
						}
					}" :mode="props.mode" />
			</VueFinalModal>

			<!-- Global Category Selection Modal -->
			<VueFinalModal
				class="my-modal-container category-modal-container"
				content-class="category-modal-content"
				:overlay-behavior="'persist'"
				v-model="showGlobalCategoryModal"
				v-on:closed="closeCategoryModal"
				contentTransition="vfm-fade">

				<div class="category-selection-modal">
					<!-- Header with Navigation -->
					<div class="modal-header">
						<button
							v-if="categoryNavigationStack.length > 0"
							class="back-button"
							@click="navigateBack">
							<Icon name="mdi:arrow-left" />
						</button>

						<h3 class="modal-title">
							{{ currentLevelTitle }}
						</h3>

						<div class="header-actions">
							<button class="search-toggle" @click="toggleSearchMode">
								<Icon name="mdi:magnify" />
							</button>
							<button class="close-button" @click="closeCategoryModal">
								<Icon name="mdi:close" />
							</button>
						</div>
					</div>

					<!-- Search Input (Hidden by Default) -->
					<div v-if="searchMode" class="search-container">
						<input
							ref="searchInput"
							v-model="categorySearchQuery"
							:placeholder="$t('CreatePrivateProductComponent.tim_kiem_danh_muc')"
							class="search-input"
							@input="filterCategories" />
					</div>

					<!-- Categories List -->
					<div class="categories-container">
						<div
							v-for="category in displayedCategories"
							:key="category.id"
							class="category-item"
							@click="selectCategory(category)">

							<div class="category-info">
								<span class="category-name">{{ category.name }}</span>
								<span v-if="!category.public" class="private-note">
									({{ $t('CreatePrivateProductComponent.chi_hien_thi_noi_bo') }})
								</span>
							</div>

							<Icon
								v-if="category.sub_categories?.length > 0"
								name="mdi:chevron-right"
								class="nav-icon" />
						</div>

						<!-- Empty State -->
						<div v-if="displayedCategories.length === 0" class="empty-state">
							<span>{{ $t('CreatePrivateProductComponent.khong_tim_thay_danh_muc') }}</span>
						</div>
					</div>
				</div>
			</VueFinalModal>

		</div>
	</div>

</template>

<script lang="ts" setup>
import ISO6391 from 'iso-639-1';
import { toast } from 'vue3-toastify';
import { VueFinalModal } from 'vue-final-modal';
import { nextTick } from 'vue';
import { useCurrencyInput } from "vue-currency-input";
import { appConst, appDataStartup, formatCurrency, formatNumber, nonAccentVietnamese, validNumber, validDecimal2Digits, parseDecimal2Digits } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import no_image from '~/assets/image/no-image.webp'
import { AgentService } from '~/services/agentService/agentService';
import exifr from 'exifr';
import { HttpStatusCode } from 'axios';
import { VueDraggableNext } from 'vue-draggable-next';
import { ProductCreateDto, ProductUpdateDto } from '~/assets/DTOs/productDtos';

const nuxtApp = useNuxtApp();
const router = useRouter();
const route = useRoute();
const { t, locale } = useI18n();
var props = defineProps({
	shopData: {},
	mode: null
});
useSeoMeta({
	title: t('AppRouteTitle.CreatePrivateProductComponent')
})
var emit = defineEmits(['close'])

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var imageService = new ImageService();
var shopService = new ShopService();
var agentService = new AgentService();
var categoryService = new CategoryService();
var searchProductTimeout: any;
// var dataShopCategories = appDataStartup.listCategory;
var imageFilesName = ref(null as any);

var name = ref("");
var nameErr = ref("");
var priceErr = ref<any>("");
var priceOffErr = ref<any>("");
var stockErr = ref<any>("");
var commissionPercentErr = ref<any>("");
var priceRootErr = ref<any>("");
var productData = ref(new ProductCreateDto({
	name: "",
	is_main: true,
	is_feature: false,
	latitude: router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)).latitude : null,
	longitude: router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)).longitude : null,
	notes: "",
	shop_id: router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)).id : null,
	price: null as any,
	price_off: null as any,
	category_ids: [] as any[],
	translation: [] as any,
	stock: null as any,
	unit: null as any,
	commission_percent: null as any,
	price_root: null as any,
	enable: true
}));

var selectLanguage = ref(null as any);
var selectedCategories = ref([] as any[]);
var shopData = ref(router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)) : null);
var isSaving = ref(false);
var dataShopCategories = ref([] as any[]);
var isRefreshing = ref(false);
var isInitialLoading = ref(true);

var listImagesEdit = ref([] as any[]);

var showCancelCreateModal = ref(false);
var showCreateCategoryModal = ref(false);
var showAdvancedInfo = ref(false);
var showInventoryInput = ref(false);
var mode = ref(props.mode ? props.mode : null)

// Global category selection variables
var selectedGlobalCategory = ref(null as any);
var globalCategoriesFiltered = ref([] as any[]);
var isGlobalCategoriesLoaded = ref(false);
var categoryPath = ref([] as any[]);
var globalCategoryId = ref(null as any);

// New modal-based category selection variables
var showGlobalCategoryModal = ref(false);
var categoryNavigationStack = ref([] as any[]);
var currentLevelCategories = ref([] as any[]);
var currentLevelTitle = ref('');
var searchMode = ref(false);
var categorySearchQuery = ref('');
var displayedCategories = ref([] as any[]);
var searchInput = ref(null as any);

onMounted(async () => {
	// shopData.value = router.options.history.state.shopData ? JSON.parse(JSON.stringify(router.options.history.state.shopData)) : null;
	if (!shopData?.value?.id) {
		if (mode.value != 'agent') {
			await getMyShop();
		}
		else {
			await getShopDetailByAgent()
		}
	}
	else {
		getListCategory();
		isInitialLoading.value = false;
	}

	// Listen for global categories data loaded
	nuxtApp.$listen(appConst.event_key.app_data_loaded, () => {
		loadGlobalCategories();
	});

	// Load global categories if already available
	if (appDataStartup.listCategory && appDataStartup.listCategory.length > 0) {
		loadGlobalCategories();
	}

	// Initialize speech recognition
	if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
		recognition.value = new (window as any).webkitSpeechRecognition();
		recognition.value.continuous = true;
		recognition.value.interimResults = true;

		// Set language based on current selection or default
		const initialLang = selectLanguage.value || appConst.defaultLanguage;
		recognition.value.lang = getLocaleFromLanguageCode(initialLang);

		recognition.value.onresult = (event: any) => {
			let finalTranscript = '';
			for (let i = event.resultIndex; i < event.results.length; i++) {
				if (event.results[i].isFinal) {
					finalTranscript += event.results[i][0].transcript;
				}
			}
			if (finalTranscript) {
				// Update the product name with the recognized speech
				if (selectLanguage.value) {
					nameProductChange(finalTranscript, selectLanguage.value);
				} else {
					productData.value = {
						...productData.value,
						name: finalTranscript
					};
				}
				validateName();
			}
		};

		recognition.value.onend = () => {
			isRecording.value = false;
		};

		recognition.value.onerror = () => {
			isRecording.value = false;
		};
	}
});
// Add a watch on selectLanguage to update recognition language
watch(selectLanguage, (newLang) => {
  if (recognition.value && newLang) {
    // Convert ISO language code to proper locale format for speech recognition
    recognition.value.lang = getLocaleFromLanguageCode(newLang);
  }
});

onUnmounted(() => {
	nuxtApp.$unsubscribe(appConst.event_key.app_data_loaded);
});

// Helper function to convert ISO language code to locale format
function getLocaleFromLanguageCode(langCode: string): string {
  // Map of common language codes to their full locale for speech recognition
  const localeMap: {[key: string]: string} = {
    'vi': 'vi-VN',
    'en': 'en-US',
    'fr': 'fr-FR',
    'de': 'de-DE',
    'zh': 'zh-CN',
    'ja': 'ja-JP',
    'ko': 'ko-KR',
    'ru': 'ru-RU',
    'es': 'es-ES',
    'it': 'it-IT',
    'pt': 'pt-PT'
  };

  return localeMap[langCode] || `${langCode}-${langCode.toUpperCase()}`;
}

function getMyShop() {
	isRefreshing.value = true;
	shopService.myShop().then(res => {
		if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {

			shopData.value = res.body.data;
			shopData.value.language = shopData.value.language.at(0) != '[' ? shopData.value.language : JSON.parse(shopData.value.language);

			if (shopData.value.language.indexOf(appConst.defaultLanguage) == -1) {
				shopData.value.language.splice(0, 0, appConst.defaultLanguage);
			}
			if (shopData.value.language.indexOf('en') == -1) {
				shopData.value.language.splice(shopData.value.language.indexOf(appConst.defaultLanguage) + 1, 0, 'en');
			}
			selectLanguage.value = shopData.value.language[shopData.value.language.indexOf(locale.value)] ?? shopData.value.language[0] ?? appConst.defaultLanguage;

			// let indexCurrentLanguage = productData.value.translation.findIndex(function (e: any) {
			// 	return e.language_code = selectLanguage.value;
			// });
			// if (indexCurrentLanguage == -1) {
			// 	productData.value.translation.push({
			// 		language_code: selectLanguage.value,
			// 		name: productData.value.name,
			// 		description: productData.value.notes,
			// 	})
			// }
			getListCategory();
			isRefreshing.value = false;
			isInitialLoading.value = false;
		}
		else {
			isRefreshing.value = false;
			isInitialLoading.value = false;
			shopData.value = null;
		}
		return
	})
}
function getShopDetailByAgent() {
	agentService.agentShopDetail(route.params.id as any).then(res => {
		if (res.status && res.status == HttpStatusCode.Ok) {

			shopData.value = res.body.data;
			console.log(shopData.value.language)
			shopData.value.language = shopData.value.language.at(0) != '[' ? shopData.value.language : JSON.parse(shopData.value.language);

			if (shopData.value.language.indexOf(appConst.defaultLanguage) == -1) {
				shopData.value.language.splice(0, 0, appConst.defaultLanguage);
			}
			if (shopData.value.language.indexOf('en') == -1) {
				shopData.value.language.splice(shopData.value.language.indexOf(appConst.defaultLanguage) + 1, 0, 'en');
			}
			selectLanguage.value = shopData.value.language[shopData.value.language.indexOf(locale.value)] ?? shopData.value.language[0] ?? appConst.defaultLanguage;

			// selectLanguage.value = shopData.value.language[0];

			// let indexCurrentLanguage = productData.value.translation.findIndex(function (e: any) {
			// 	return e.language_code = selectLanguage.value;
			// });
			// if (indexCurrentLanguage == -1) {
			// 	productData.value.translation.push({
			// 		language_code: selectLanguage.value,
			// 		name: productData.value.name,
			// 		description: productData.value.notes,
			// 	})
			// }

			getListCategory();
			isRefreshing.value = false;
			isInitialLoading.value = false;
		}
		else {

			isRefreshing.value = false;
			isInitialLoading.value = false;
			shopData.value = null;
		}
		return
	})
}
function close(updated = false) {
	setTimeout(() => {
		router.options.history.state.back ? router.back() : router.push(appRoute.MyShopComponent)
	}, 1500);
	// if (updated == true) {
	// 	nuxtApp.$emit('refresh_product_manage', true)
	// }
}

function getListCategory() {
	categoryService.getCategoryByShopId(shopData.value.id).then(res => {
		dataShopCategories.value = JSON.parse(JSON.stringify(res.body.data))
		setSelectedCategories();
	})
}

function setSelectedCategories() {
	let arr = dataShopCategories.value.filter(el => {

		return productData.value.category_ids.indexOf(el.id) != -1;
	})
	selectedCategories.value = JSON.parse(JSON.stringify(arr));
}

async function fileChangeInput(fileInput: any) {
	if (fileInput.target.files.length) {
		if (fileInput.target.files.length > appConst.productImageMaxAmount - listImagesEdit.value.length) {
			toast.warn(t('CreatePrivateProductComponent.so_luong_anh_toi_da', { amount: appConst.productImageMaxAmount }))
		}
		else {
			imageFilesName.value = fileInput.target.files;
			for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
				if (fileInput.target.files[i].size > appConst.image_size.max) {
					let imgErr = t('CreatePrivateProductComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
					toast.error(imgErr);
				}
				else {
					const reader = new FileReader();
					reader.onload = async (e: any) => {
						const image = new Image();
						image.src = e.target.result;

						let orientationExif;
						if (fileInput.target.files[i].type != 'image/webp') {
							orientationExif = await exifr.orientation(image) || 0;
						}
						else orientationExif = 0;
						let newImg = {
							isEdit: true,
							object_type: appConst.object_type.product,
							orientation: orientationExif,
							description: null,
							enable: true,
							path: image.src,
							title: productData.value.name
						}
						listImagesEdit.value.push(newImg);
					}
					await reader.readAsDataURL(fileInput.target.files[i]);
				}
			}
		}


	}
}
async function createPrivateProduct() {
	isSaving.value = true;
	let checkName = await validateName();
	validateNote();
	let checkPrice = validatePrice();
	let checkPriceOff = validatePriceOff();
	let checkStock = validateStock();
	let checkCommissionPercent = validateCommissionPercent();	if (checkName && checkPrice && checkPriceOff && checkStock && checkCommissionPercent) {
		let createObj = new ProductCreateDto(productData.value);
		createObj.latitude = shopData.value.latitude;
		createObj.longitude = shopData.value.longitude;
		createObj.shop_id = shopData.value.id;

		productService.createPrivateProduct(createObj).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				await handleSaveNewImage(res.body.data.id).then(() => {
					// isSaving.value = false;
					toast.success(t('CreatePrivateProductComponent.them_thanh_cong'));
					close(true);
				});
			}			else {
				// toast.error('Thêm thất bại.\nKiểm tra lại thông tin ');

				isSaving.value = false;
				await validateName();
				await validateNote();
				await validatePrice();
				await validatePriceOff();
				await validateStock();
				await validateCommissionPercent();
				hightlightError();
			}		}).catch(async err => {
			await validateName();
			await validateNote();
			await validatePrice();
			await validatePriceOff();
			await validateStock();
			await validateCommissionPercent();
			// toast.error('Thêm thất bại.\nKiểm tra lại thông tin ');
			hightlightError();
			isSaving.value = false;
		})
	}
	else {

		await validateName();
		await validateNote();
		await validatePrice();
		await validatePriceOff();
		await validateStock();
		await validateCommissionPercent();
		hightlightError();
		isSaving.value = false;
	}
}
async function agentCreatePrivateProduct() {
	isSaving.value = true;
	let checkName = await validateName();
	validateNote();
	let checkPrice = validatePrice();
	let checkPriceOff = validatePriceOff();
	let checkStock = validateStock();
	let checkCommissionPercent = validateCommissionPercent();
	if (checkName && checkPrice && checkPriceOff && checkStock && checkCommissionPercent) {
		let createObj = new ProductCreateDto(productData.value);
		createObj.latitude = shopData.value.latitude;
		createObj.longitude = shopData.value.longitude;
		createObj.shop_id = shopData.value.id;

		agentService.createPrivateProduct(createObj).then(async res => {
			if (res.status == HttpStatusCode.Ok) {
				await handleSaveNewImage(res.body.data.id).then(() => {
					// isSaving.value = false;
					toast.success(t('CreatePrivateProductComponent.them_thanh_cong'));
					close(true);
				});

			}
			else {
				isSaving.value = false;
				await validateName();
				await validateNote();
				await validatePrice();
				await validatePriceOff();
				await validateStock();
				hightlightError();
			}
		}).catch(async err => {
			// toast.error('Thêm thất bại.\nKiểm tra lại thông tin ');
			await validateName();
			await validateNote();
			await validatePrice();
			await validatePriceOff();
			await validateStock();
			hightlightError();
			isSaving.value = false;
		})
	}
	else {
		await validateName();
		await validateNote();
		await validatePrice();
		await validatePriceOff();
		await validateStock();
		hightlightError();
		isSaving.value = false;
	}
}
function hightlightError() {
	let els = document.getElementsByClassName("error-message");
	Array.prototype.forEach.call(els, function (el) {
		// Do stuff here
		el.classList.add("hight-light");
		setTimeout(() => {
			el.classList.remove("hight-light");
		}, 1000);
	});
}

function nameProductChange(text: string, translation: string) {
	// if (translation == 'vi') {
	// 	productData.value.name = text;
	// 	if (!productData.value.name || !productData.value.name.length) {
	// 		nameErr.value = 'Vui lòng nhập tên Tiếng Việt của sản phẩm';
	// 	} else {
	// 		nameErr.value = '';
	// 	}
	// }

	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == translation;
	})
	if (indexTranslate != -1) {
		productData.value.translation[indexTranslate].name = text;
	}
	else {
		productData.value.translation.push({
			language_code: translation,
			name: text
		})
	}
}
function validateName() {
	let indexVietnamese = productData.value.translation.findIndex(function (el: any) {
		return el.language_code == appConst.defaultLanguage
	})

	if (indexVietnamese != -1 && productData.value.translation[indexVietnamese]?.name?.length > 0) {
		nameErr.value = "";
		productData.value.name = productData.value.translation[indexVietnamese].name;
		return true
	}

	let indexFirstName = productData.value.translation.findIndex(function (el: any) {
		return el.name?.length > 0
	})
	if (indexFirstName != -1) {
		nameErr.value = "";
		productData.value.name = productData.value.translation[indexFirstName].name;
		return true
	}
	// if (productData?.value?.name?.length) {
	// 	nameErr.value = "";
	// 	return true
	// }
	nameErr.value = t('CreatePrivateProductComponent.vui_long_nhap_ten_san_pham');
	return false;
}
function validateNote() {
	let indexVietnamese = productData.value.translation.findIndex(function (el: any) {
		return el.language_code == appConst.defaultLanguage
	})

	if (indexVietnamese != -1 && productData.value.translation[indexVietnamese]?.description?.length > 0) {
		productData.value.notes = productData.value.translation[indexVietnamese].description;
		return true
	}

	let indexFirstDescription = productData.value.translation.findIndex(function (el: any) {
		return el.description?.length > 0
	})
	if (indexFirstDescription != -1) {
		productData.value.notes = productData.value.translation[indexFirstDescription].description;
		return true
	}
	productData.value.notes = "";
	return false;
}

function validatePrice() {
	if (productData.value?.price) {
		priceErr.value = !validDecimal2Digits(productData.value?.price?.toString()) ? t('CreatePrivateProductComponent.gia_phai_la_kieu_so_2_thap_phan') : null;
	}
	else {
		priceErr.value = null
	}

	return !priceErr.value;
}

function validatePriceOff() {
	if (productData.value?.price_off) {
		priceOffErr.value = !validDecimal2Digits(productData.value?.price_off?.toString()) ? t('CreatePrivateProductComponent.gia_phai_la_kieu_so_2_thap_phan') : null;
	}
	else {
		priceOffErr.value = null
	}

	return !priceOffErr.value
}
function validateStock() {
	if (productData.value?.stock) {
		stockErr.value = !validNumber(productData.value?.stock) ? t('CreatePrivateProductComponent.ton_kho_phai_la_kieu_so') : null;
	}
	else {
		stockErr.value = null
	}

	return !stockErr.value
}

function validateCommissionPercent() {
	if (productData.value?.commission_percent?.toString()) {
		const value = parseFloat(productData.value.commission_percent.toString());
		if (!validDecimal2Digits(productData.value?.commission_percent?.toString())) {
			commissionPercentErr.value = t('EditProductComponent.commission_percent_phai_la_kieu_so_2_thap_phan');
		} else if (value < 0) {
			commissionPercentErr.value = t('EditProductComponent.commission_percent_phai_lon_hon_0');
		} else if (value > 100) {
			commissionPercentErr.value = t('EditProductComponent.commission_percent_khong_vuot_qua_100');
		} else {
			commissionPercentErr.value = null;
		}
	}
	else {
		commissionPercentErr.value = null
	}

	return !commissionPercentErr.value
}

function validatePriceRoot() {
	if (productData.value?.price_root) {
		priceRootErr.value = !validDecimal2Digits(productData.value?.price_root?.toString()) ? t('CreatePrivateProductComponent.gia_phai_la_kieu_so_2_thap_phan') : null;
	}
	else {
		priceRootErr.value = null
	}

	return !priceRootErr.value
}

function noteProductChange(text: string, translation: string) {
	// if (translation == 'vi') {
	// 	productData.value.notes = text;
	// }

	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == translation;
	})
	if (indexTranslate != -1) {
		productData.value.translation[indexTranslate].description = text;
	}
	else {
		productData.value.translation.push({
			language_code: translation,
			description: text
		})
	}
}
function getNameTranslate(language: string) {
	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == language;
	});
	return indexTranslate != -1 ? productData.value.translation[indexTranslate].name : '';
}
function getNoteTranslate(language: string) {
	let indexTranslate = productData.value.translation.findIndex(function (e: any) {
		return e.language_code == language;
	});
	return indexTranslate != -1 ? productData.value.translation[indexTranslate].description : '';
}

async function handleSaveNewImage(product_id: string) {
	await Promise.all(
		listImagesEdit.value.map(async (img, index) => {
			img.index = index;
			img.title = productData.value.name;
			img.parent_id = product_id;
			img.is_profile_picture = index == 0 ? true : false;
			await imageService.insertImage(img);
		})
	)

}

// Add these state variables
var isRecording = ref(false);
var recognition = ref(null as any);

function toggleSpeechRecognition() {
	if (!recognition.value) {
		toast.error('Speech recognition not supported in this browser');
		return;
	}

	if (isRecording.value) {
		recognition.value.stop();
		isRecording.value = false;
	} else {
		recognition.value.start();
		isRecording.value = true;
	}
}

// Global category functions
function loadGlobalCategories() {
	if (appDataStartup.listCategory && appDataStartup.listCategory.length > 0) {
		globalCategoriesFiltered.value = JSON.parse(JSON.stringify(appDataStartup.listCategory));
		isGlobalCategoriesLoaded.value = true;
	}
}

// New modal-based category selection functions
function openCategoryModal() {
	showGlobalCategoryModal.value = true;
	resetToRootLevel();
}

function resetToRootLevel() {
	categoryNavigationStack.value = [];
	currentLevelCategories.value = [...globalCategoriesFiltered.value];
	currentLevelTitle.value = t('CreatePrivateProductComponent.chon_danh_muc_global');
	displayedCategories.value = [...currentLevelCategories.value];
	searchMode.value = false;
	categorySearchQuery.value = '';
}

function selectCategory(category: any) {
	if (category.sub_categories && category.sub_categories.length > 0) {
		// Navigate to sub-categories
		categoryNavigationStack.value.push({
			categories: [...currentLevelCategories.value],
			title: currentLevelTitle.value,
			searchMode: searchMode.value,
			searchQuery: categorySearchQuery.value
		});

		currentLevelCategories.value = [...category.sub_categories];
		currentLevelTitle.value = category.name;
		displayedCategories.value = [...currentLevelCategories.value];
		searchMode.value = false;
		categorySearchQuery.value = '';
	} else {
		// Final selection
		selectedGlobalCategory.value = category;
		setGlobalCategory(category);
		buildCategoryPath(category);
		closeCategoryModal();
		// Focus next field after selection
		focusNextField();
	}
}

function navigateBack() {
	if (categoryNavigationStack.value.length > 0) {
		const previousLevel = categoryNavigationStack.value.pop();
		currentLevelCategories.value = previousLevel.categories;
		currentLevelTitle.value = previousLevel.title;
		searchMode.value = previousLevel.searchMode;
		categorySearchQuery.value = previousLevel.searchQuery;

		if (searchMode.value && categorySearchQuery.value) {
			filterCategories();
		} else {
			displayedCategories.value = [...currentLevelCategories.value];
		}
	}
}

function toggleSearchMode() {
	searchMode.value = !searchMode.value;

	if (searchMode.value) {
		nextTick(() => {
			searchInput.value?.focus();
		});
	} else {
		categorySearchQuery.value = '';
		displayedCategories.value = [...currentLevelCategories.value];
	}
}

function filterCategories() {
	if (!categorySearchQuery.value.trim()) {
		displayedCategories.value = [...currentLevelCategories.value];
		return;
	}

	const query = nonAccentVietnamese(categorySearchQuery.value).toLowerCase();
	displayedCategories.value = currentLevelCategories.value.filter((category: any) => {
		const name = nonAccentVietnamese(category.name).toLowerCase();
		return name.includes(query);
	});
}

function closeCategoryModal() {
	showGlobalCategoryModal.value = false;
	resetToRootLevel();
}

function setGlobalCategory(category: any) {
	// Remove previous global category if exists
	if (globalCategoryId.value) {
		productData.value.category_ids = productData.value.category_ids.filter((id: string) => id !== globalCategoryId.value);
	}

	// Add new global category
	globalCategoryId.value = category.id;
	if (!productData.value.category_ids.includes(category.id)) {
		productData.value.category_ids.push(category.id);
	}
}

function buildCategoryPath(category: any) {
	categoryPath.value = [];

	// Build path from category hierarchy
	let currentCategory = category;
	const path = [];

	while (currentCategory) {
		path.unshift({
			id: currentCategory.id,
			name: currentCategory.name
		});

		// Find parent category if exists
		if (currentCategory.parent_id) {
			currentCategory = findCategoryById(currentCategory.parent_id, appDataStartup.listCategory);
		} else {
			currentCategory = null;
		}
	}

	categoryPath.value = path;
}

function findCategoryById(id: string, categories: any[]): any {
	for (const category of categories) {
		if (category.id === id) {
			return category;
		}
		if (category.sub_categories && category.sub_categories.length > 0) {
			const found = findCategoryById(id, category.sub_categories);
			if (found) return found;
		}
	}
	return null;
}



function focusNextField() {
	// Focus the next field after category selection
	nextTick(() => {
		// Try to focus the advanced info toggle or the first input in advanced section
		const advancedToggle = document.querySelector('.toggle-advanced-btn') as HTMLElement;
		if (advancedToggle) {
			advancedToggle.focus();
		}
	});
}
</script>

<style lang="scss" src="./CreatePrivateProductStyles.scss"></style>