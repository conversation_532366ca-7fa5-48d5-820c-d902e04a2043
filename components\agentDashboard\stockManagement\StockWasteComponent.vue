<template>
  <div class="stock-waste-component">
    <!-- Sub Header -->
    <SubHeaderV2Component
      :title="$t('StockManagement.hang_huy')"
      :show-back="true"
      @back="$router.back()"
    />

    <!-- Header -->
    <div class="component-header">
      <p>{{ $t('StockManagement.ghi_nhan_hang_huy') }}</p>
    </div>

    <!-- Waste Form -->
    <form @submit.prevent="submitWaste" class="waste-form">
      <!-- Product Selection -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.chon_san_pham') }} *</label>
        <div class="product-selector" @click="showProductPicker = true">
          <div v-if="selectedProduct" class="selected-product">
            <img :src="selectedProduct.image || '/default-product.png'" :alt="selectedProduct.name" class="product-image">
            <div class="product-info">
              <h4>{{ selectedProduct.name }}</h4>
              <p>{{ selectedProduct.category }}</p>
              <span class="stock-level" :class="getStockLevelClass(selectedProduct.current_stock)">
                {{ $t('StockManagement.ton_kho') }}: {{ selectedProduct.current_stock || 0 }} {{ selectedProduct.unit }}
              </span>
            </div>
          </div>
          <div v-else class="placeholder">
            <Icon name="solar:box-bold" size="24" />
            <span>{{ $t('StockManagement.chon_san_pham') }}</span>
          </div>
          <Icon name="solar:arrow-right-bold" size="20" class="arrow-icon" />
        </div>
      </div>

      <!-- Quantity -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.so_luong_huy') }} *</label>
        <div class="quantity-input">
          <input
            v-model.number="formData.quantity"
            type="number"
            :placeholder="$t('StockManagement.nhap_so_luong')"
            :max="selectedProduct?.current_stock || 999999"
            min="1"
            step="1"
            required
            class="form-input"
            @input="validateQuantity"
          />
          <span class="unit-label">{{ selectedProduct?.unit || $t('StockManagement.don_vi') }}</span>
        </div>
        <div v-if="quantityError" class="error-message">
          {{ quantityError }}
        </div>
      </div>

      <!-- Waste Reason -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.ly_do_huy') }} *</label>
        <select v-model="formData.reason" required class="form-select">
          <option value="">{{ $t('StockManagement.chon_ly_do') }}</option>
          <option value="expired">{{ $t('StockManagement.het_han') }}</option>
          <option value="damaged">{{ $t('StockManagement.hong_hoc') }}</option>
          <option value="spoiled">{{ $t('StockManagement.bi_hong') }}</option>
          <option value="contaminated">{{ $t('StockManagement.bi_nhiem') }}</option>
          <option value="quality_issue">{{ $t('StockManagement.chat_luong_kem') }}</option>
          <option value="customer_return">{{ $t('StockManagement.khach_tra_lai') }}</option>
          <option value="other">{{ $t('StockManagement.ly_do_khac') }}</option>
        </select>
      </div>

      <!-- Photo Documentation -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.hinh_anh_minh_chung') }}</label>
        <div class="image-upload" @click="triggerImageUpload">
          <input
            ref="imageInput"
            type="file"
            accept="image/*"
            @change="handleImageUpload"
            style="display: none"
          />
          <div v-if="formData.photo" class="uploaded-image">
            <img :src="formData.photo" alt="Waste Photo" />
            <button type="button" @click.stop="removeImage" class="remove-image-btn">
              <Icon name="solar:trash-bin-trash-bold" size="16" />
            </button>
          </div>
          <div v-else class="upload-placeholder">
            <Icon name="solar:camera-bold" size="32" />
            <p>{{ $t('StockManagement.tai_len_hinh') }}</p>
            <span>{{ $t('StockManagement.chup_hinh') }} / {{ $t('StockManagement.chon_tu_thu_vien') }}</span>
          </div>
        </div>
      </div>

      <!-- Notes -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.ghi_chu_chi_tiet') }}</label>
        <textarea
          v-model="formData.notes"
          :placeholder="$t('StockManagement.mo_ta_chi_tiet')"
          rows="4"
          class="form-textarea"
        ></textarea>
      </div>

      <!-- Action Buttons -->
      <div class="form-actions">
        <button type="button" @click="$router.back()" class="btn btn-secondary">
          {{ $t('StockManagement.huy_bo') }}
        </button>
        <button type="submit" :disabled="!isFormValid || loading" class="btn btn-primary">
          <Icon v-if="loading" name="solar:refresh-bold" size="20" class="loading-icon" />
          {{ $t('StockManagement.luu_thong_tin') }}
        </button>
      </div>
    </form>

    <!-- Product Picker Modal -->
    <ProductPickerModal
      v-if="showProductPicker"
      :shop-id="shopId"
      @close="showProductPicker = false"
      @select="selectProduct"
    />

    <!-- Success Toast -->
    <div v-if="showSuccessToast" class="success-toast">
      <Icon name="solar:check-circle-bold" size="24" />
      <span>{{ $t('StockManagement.cap_nhat_thanh_cong') }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import { MqttService } from '~/services/mqttService/mqttService'
import ProductPickerModal from './ProductPickerModal.vue'
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue'
import { HttpStatusCode } from "axios";

// Props
const props = defineProps<{
  mode?: string
}>()

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Use the centralized shop data composable
const {
  activeShop,
  isLoading: shopDataLoading,
  initializeShopData,
  getShopBySlug
} = useShopData()

// Services
const stockService = new StockService()
const mqttService = new MqttService()

// Reactive data
const loading = ref(false)
const showProductPicker = ref(false)
const showSuccessToast = ref(false)
const selectedProduct = ref(null)
const quantityError = ref('')
const imageInput = ref(null)

const formData = ref({
  product_id: '',
  quantity: null,
  reason: '',
  photo: '',
  notes: ''
})

// Computed properties
const isFormValid = computed(() => {
  return selectedProduct.value && 
         formData.value.quantity > 0 && 
         formData.value.reason &&
         !quantityError.value
})

const shopSlug = route.params.id as string
const shopId = computed(() => {
    const shop = getShopBySlug(shopSlug)
    return shop?.id || shopSlug
})

// Methods
const selectProduct = (product: any) => {
  selectedProduct.value = product
  formData.value.product_id = product.id
  showProductPicker.value = false
  validateQuantity() // Revalidate quantity when product changes
}

const getStockLevelClass = (stock: number) => {
  if (stock <= 0) return 'stock-empty'
  if (stock <= 10) return 'stock-low'
  return 'stock-normal'
}

const validateQuantity = () => {
  quantityError.value = ''
  
  if (!selectedProduct.value || !formData.value.quantity) return
  
  const currentStock = selectedProduct.value.current_stock || 0
  const requestedQuantity = formData.value.quantity
  
  if (requestedQuantity > currentStock) {
    quantityError.value = t('StockManagement.so_luong_vuot_qua_ton_kho', { 
      available: currentStock,
      unit: selectedProduct.value.unit 
    })
  }
}

const triggerImageUpload = () => {
  imageInput.value?.click()
}

const handleImageUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert(t('StockManagement.dung_luong_anh_toi_da', { size: '5' }))
      return
    }

    // Convert to base64 for preview
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.value.photo = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const removeImage = () => {
  formData.value.photo = ''
  if (imageInput.value) {
    imageInput.value.value = ''
  }
}

const submitWaste = async () => {
  if (!isFormValid.value) return

  loading.value = true
  try {
    const wasteData = {
      ...formData.value,
      shop_id: shopId.value
    }

    const response = await stockService.stockWaste(wasteData)
    if (response.status == HttpStatusCode.Ok) {
      // Show success message
      showSuccessToast.value = true
      setTimeout(() => {
        showSuccessToast.value = false
      }, 3000)

      // Publish MQTT update
      mqttService.publish(`stock_updates_${shopId.value}`, JSON.stringify({
        type: 'stock_waste_complete',
        data: {
          product_id: formData.value.product_id,
          quantity: formData.value.quantity,
          reason: formData.value.reason,
          timestamp: new Date().toISOString()
        }
      }))

      // Reset form
      resetForm()
    } else {
      throw new Error(response.message || 'Waste recording failed')
    }
  } catch (error) {
    console.error('Stock waste error:', error)
    // alert(t('StockManagement.loi_cap_nhat'))
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  selectedProduct.value = null
  quantityError.value = ''
  formData.value = {
    product_id: '',
    quantity: null,
    reason: '',
    photo: '',
    notes: ''
  }
  if (imageInput.value) {
    imageInput.value.value = ''
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize shop data first
  await initializeShopData()
})
</script>

<style scoped>
.stock-waste-component {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750px;
}

.component-header {
  margin-bottom: 20px;
  text-align: center;

  h1 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
  }

  p {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.waste-form {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.product-selector {
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    border-color: #3498db;
  }

  .selected-product {
    display: flex;
    align-items: center;
    gap: 12px;

    .product-image {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      object-fit: cover;
    }

    .product-info {
      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0 0 4px 0;
      }

      .stock-level {
        font-size: 11px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;

        &.stock-normal {
          background: #d5f4e6;
          color: #27ae60;
        }

        &.stock-low {
          background: #fef9e7;
          color: #f39c12;
        }

        &.stock-empty {
          background: #fadbd8;
          color: #e74c3c;
        }
      }
    }
  }

  .placeholder {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #7f8c8d;

    span {
      font-size: 14px;
    }
  }

  .arrow-icon {
    color: #bdc3c7;
  }
}

.quantity-input {
  display: flex;
  align-items: center;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  overflow: hidden;

  .form-input {
    flex: 1;
    border: none;
    padding: 12px 15px;
    font-size: 14px;
    outline: none;

    &:focus {
      background-color: #f8f9fa;
    }
  }

  .unit-label {
    background-color: #ecf0f1;
    padding: 12px 15px;
    font-size: 14px;
    font-weight: 600;
    color: #7f8c8d;
  }
}

.form-select {
  width: 100%;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    background-color: #f8f9fa;
  }
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    background-color: #f8f9fa;
  }
}

.form-textarea {
  width: 100%;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    background-color: #f8f9fa;
  }
}

.image-upload {
  border: 2px dashed #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #3498db;
  }

  .uploaded-image {
    position: relative;
    display: inline-block;

    img {
      max-width: 200px;
      max-height: 150px;
      border-radius: 6px;
    }

    .remove-image-btn {
      position: absolute;
      top: -8px;
      right: -8px;
      background: #e74c3c;
      color: white;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }

  .upload-placeholder {
    color: #7f8c8d;

    p {
      font-size: 16px;
      font-weight: 600;
      margin: 10px 0 5px 0;
    }

    span {
      font-size: 12px;
    }
  }
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;

  .btn {
    flex: 1;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &.btn-secondary {
      background: #ecf0f1;
      color: #7f8c8d;
      border: none;

      &:hover {
        background: #d5dbdb;
      }
    }

    &.btn-primary {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      border: none;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.success-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #27ae60;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@media (max-width: 480px) {
  .stock-waste-component {
    padding: 10px;
  }

  .waste-form {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
