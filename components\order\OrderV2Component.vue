<template>
	<div class="public-container">
		<div class='order-v2-container'>
			<client-only hidden>
				<LMap id="temp_leaflet_map" hidden class="leaflet-map-order-container " v-on:ready="(e: any) => {
					tempLeafletMap = e;
				}"></LMap>
			</client-only>
			<!-- <HeaderComponent :title="$t('AppRouteTitle.OrderComponent')">
			</HeaderComponent> -->
			<SubHeaderV2Component class="order-header">
				<template v-slot:header_middle>
					<h3>{{ $t('AppRouteTitle.OrderComponent') }}</h3>
					<em>{{ $t('OrderComponent.thong_tin_se_duoc_bao_mat') }}</em>
				</template>
			</SubHeaderV2Component>
			<div class='v-stack order-content-container'
				v-if="!refreshing && (selectedItemsCart && selectedItemsCart.length)">

				<div class="receiver-info" v-on:click="(e) => {
					if (checkCanOrder() == true) {
						if (userInfo) {
							showSelectUser = true;
							isEditingCustomerInfo = false;
						}
						else {
							isEditingCustomerInfo = true;
							showSelectUser = false;
						}
					}
					else e.preventDefault()
				}">
					<Icon name="solar:map-point-linear"></Icon>
					<div class="user-info">
						<span class="name-phone">
							<span :class="{ 'error': !(orderData.customer_name && orderData.customer_name.length) }">
								{{ orderData.customer_name && orderData.customer_name.length
									? orderData.customer_name
									: $t('OrderComponent.chua_co_ten')
								}}
							</span>

							{{ " | " }}

							<span
								:class="{ 'error': !(validPhone(orderData.customer_phone) && validPhone(orderData.customer_phone).length) }">
								{{ orderData.customer_phone && orderData.customer_phone.length
									? orderData.customer_phone
									: $t('OrderComponent.chua_co_sdt')
								}}
							</span>
						</span>

						<span class="address" :class="{ 'error': !(orderData.address && orderData.address.length) }">
							{{ orderData.address && orderData.address.length
								? orderData.address
								: $t('OrderComponent.chua_co_dia_chi')
							}}
						</span>
					</div>
					<button class="select-receiver" v-if="checkCanOrder() == true">
						<Icon name="material-symbols:arrow-forward-ios-rounded"></Icon>
					</button>
				</div>

				<div class="cart-selected-items">
					<div class="cart-item-shop">
						<div class="shop-info">
							<Icon class="icon-label" name="solar:shop-2-linear"></Icon>
							<nuxt-link
								:to="appRoute.DetailShopComponent + '/' + (shopData?.slug ? shopData?.slug : shopData?.id)"
								class="name">
								{{ shopData?.name }}
							</nuxt-link>
							<v-btn class="chat-to-shop" variant="tonal" v-on:click="() => {
								chatToShop()
							}">
								<Icon name="solar:chat-round-dots-bold"></Icon>
								<span>{{ $t('OrderComponent.nhan_tin') }}</span>
							</v-btn>
						</div>

						<div class="closed" v-if="checkCanOrder() == false">
							<span>{{ $t('OrderComponent.cua_hang_dang_dong_cua') }}</span>
						</div>
					</div>

					<div class="cart-item-container" v-for="(itemCart, index) in selectedItemsCart">
						<img loading="lazy" :src="(itemCart && itemCart.product?.profile_picture)
							? (domainImage + itemCart.product?.profile_picture)
							: icon_for_product" :placeholder="icon_for_product" :alt="showTranslateProductName(itemCart.product)" />
						<div class='v-stack item-cart-detail'>
							<div class='h-stack item-cart-product-name'
								:title="showTranslateProductName(itemCart.product)">
								<span>
									{{ showTranslateProductName(itemCart.product) }}
								</span>
							</div>
							<span class='product-price'>
								{{
									(itemCart.product.price_off != null && itemCart.product.price_off <
										itemCart.product.price) ? formatCurrency(parseFloat(itemCart.product.price_off),
											itemCart.product?.shop?.currency) : (parseFloat(itemCart.product.price) == 0 ||
												itemCart.product.price == null) ? $t('CartComponent.gia_lien_he') :
										formatCurrency(parseFloat(itemCart.product.price), itemCart.product?.shop?.currency)
								}} <em class="off"
									:class="{ 'show': (itemCart.product.price_off != null && itemCart.product.price_off < itemCart.product.price) }">
									{{
										formatCurrency(itemCart.product.price ? parseFloat(itemCart.product.price) : 0,
											itemCart.product?.shop?.currency)
									}}
									</em>
							</span>
							<div class='h-stack item-cart-quantity' v-if="checkCanOrder() == true">
								<button class='quantity-button left' title="-1" v-if="checkCanOrder() == true"
									:disabled="(itemCart.quantity <= 1 || undefined)" v-on:click="(e) => {
										e.stopPropagation();
										if (parseFloat((itemCart.quantity - 1).toString()) > 0) {
											let itemCartTemp = JSON.parse(JSON.stringify(itemCart));
											itemCartTemp.quantity = itemCartTemp.quantity - 1 > 0 ? (parseFloat(itemCartTemp.quantity) - 1) : 0;
											itemCartTemp.price = parseFloat(itemCartTemp.quantity) * ((itemCartTemp.product.price_off != null && itemCartTemp.product.price_off < itemCartTemp.product.price) ? itemCartTemp.product.price_off : itemCartTemp.product.price);
											changeCartItem(itemCartTemp, index).then(() => { checkDeliveryPrice(); });

										}
									}">
									-
								</button>
								<input v-if="checkCanOrder() == true" type="number" v-model="itemCart.quantity" min="1"
									class="price-input" max=1000000000000 v-on:input="($event: any) => {

										if (itemCart.quantity < 1) {
											itemCart.quantity = 1
										}
										let itemCartTemp = JSON.parse(JSON.stringify(itemCart));
										itemCartTemp.price = parseFloat(itemCartTemp.quantity) * ((itemCartTemp.product.price_off != null && itemCartTemp.product.price_off < itemCartTemp.product.price) ? itemCartTemp.product.price_off : itemCartTemp.product.price);
										changeCartItem(itemCartTemp, index);
									}" v-on:change="() => {
										checkDeliveryPrice();
									}" :disabled="checkCanOrder() == false">
								<button class='quantity-button right' v-if="checkCanOrder() == true" title="+1"
									v-on:click="(e) => {
										e.stopPropagation();
										let itemCartTemp = JSON.parse(JSON.stringify(itemCart));
										itemCartTemp.quantity = itemCartTemp.quantity ? (parseFloat(itemCartTemp.quantity) + 1) : 1;
										itemCartTemp.price = parseFloat(itemCartTemp.quantity) * ((itemCartTemp.product.price_off != null && itemCartTemp.product.price_off < itemCartTemp.product.price) ? itemCartTemp.product.price_off : itemCartTemp.product.price);
										changeCartItem(itemCartTemp, index).then(() => {
											checkDeliveryPrice();
										});
									}">
									+
								</button>
							</div>
							<div class='h-stack item-cart-quantity' v-else>
								x{{ itemCart.quantity }}
							</div>
							<div class="note-input" v-if="itemCart.notes?.length">
								<Icon name="solar:chat-line-linear"></Icon>
								<p>{{ itemCart.notes }}</p>
							</div>

						</div>
					</div>
					<span class="selected-total" v-html="$t('OrderComponent.x_mat_hang_tong_cong_y', {
						x: selectedItemsCart.length,
						y: ':total_off'
					}).replaceAll(':total_off', `<em>
						${getCartTotalPriceOff()
							? formatCurrency((getCartTotalPriceOff() || 0), shopData?.currency)
							: $t('OrderComponent.gia_lien_he')}</em>`
					)"></span>
				</div>

				<div class="payment-method" v-if="checkCanOrder() == true">
					<Icon class="icon-label" name="solar:wallet-money-linear"></Icon>
					<div class="content-container">
						<span class="label">
							{{ $t('OrderComponent.hinh_thuc_thanh_toan') }}
						</span>
						<span class="content">
							{{ $t('OrderComponent.thanh_toan_khi_nhan_hang') }}
						</span>
					</div>
				</div>

				<div class="payment-method" v-if="checkCanOrder() == true">
					<Icon class="icon-label" name="solar:delivery-linear"></Icon>
					<div class="content-container">
						<span class="label">
							{{ $t('OrderComponent.phuong_thuc_nhan_hang') }}
							<span class="delivery-price"
								v-if="deliveryPrice != null && !delivery_price_checking && listOptionsDeliveryPrice?.length">
								({{ $t('OrderComponent.phi_van_chuyen') }}: {{ deliveryPrice != null &&
									listPartners?.length ?
									formatCurrency(deliveryPrice
										|| 0) : $t('OrderComponent.doi_bao_gia') }})
							</span>
						</span>
						<span class="h-stack content">
							<div class="content-item" :class="{ 'active': !deliveryType }" v-on:click="() => {
								deliveryType = false;
								checkDeliveryPrice();
							}">
								<Icon name="ion:radio-button-on" v-if="!deliveryType"></Icon>
								<Icon name="material-symbols:radio-button-unchecked" v-else></Icon>
								<span>{{ $t('OrderComponent.giao_tan_noi') }}</span>
							</div>
							<div class="content-item" :class="{ 'active': deliveryType }" v-on:click="() => {
								deliveryType = true;
								deliveryPrice = 0;
								discountDeliveryPrice = null;
							}">
								<Icon name="ion:radio-button-on" v-if="deliveryType"></Icon>
								<Icon name="material-symbols:radio-button-unchecked" v-else></Icon>
								<span>{{ $t('OrderComponent.tu_lay_hang') }}</span>
							</div>

						</span>

						<div class="delivery-partners-content-container"
							v-if="!deliveryType && listPartners?.length && listOptionsDeliveryPrice?.length">
							<span class="h-stack delivery-partners-content">
								<img class="partner-logo" :src="selectedPartner?.information?.logo"
									v-if="selectedPartner?.information?.logo" :alt="`logo ${selectedPartner?.name}`">
								<span class="partner-name" v-else>
									{{ selectedPartner?.name }}
								</span>
							</span>

							<span class="label service-type">
								{{ $t('OrderComponent.goi_dich_vu') }} <em v-if="delivery_price_checking">{{
									$t('OrderComponent.dang_kiem_tra') }}</em>
							</span>
							<div class="list-options-delivery-price">
								<button class="delivery-price-option" :disabled="delivery_price_checking" 
								v-on:click="() => {
									selectedDeliveryPrice = itemPrice;
									deliveryPrice = itemPrice.price;
									discountDeliveryPrice = itemPrice.discount;
								}" 
								:class="{ 'selected': itemPrice?.service_id == selectedDeliveryPrice?.service_id }"
									v-for="itemPrice of listOptionsDeliveryPrice">
									<Icon name="ion:radio-button-on"
										v-if="itemPrice?.service_id == selectedDeliveryPrice?.service_id"></Icon>
									<Icon name="material-symbols:radio-button-unchecked" v-else></Icon>
									<span>{{ itemPrice[(localeMap as any)[locale]] ?? itemPrice.name }} <em>({{ itemPrice.distance ?? driving_distance }} km - {{
										itemPrice.price != null ? formatCurrency(itemPrice.price) :
											(itemPrice.error?.message?.[locale] ?? itemPrice.error?.message?.vi)
											}})</em></span>
								</button>

							</div>
						</div>
					</div>
				</div>

				<div class="receiver-info" v-if="checkCanOrder() == true" v-on:click="() => {
					showDateTimePickerModal = true;

				}">
					<Icon name="solar:sort-by-time-linear"></Icon>
					<div class="user-info">
						<span class="name-phone">
							<span>
								{{ $t('OrderComponent.thoi_gian_nhan_hang') }}
							</span>
						</span>

						<span class="address">
							{{ orderData.delivery_time?.length
								? orderData.delivery_time
								: $t('OrderComponent.chua_chon')
							}}
						</span>
					</div>
					<button class="select-receiver">
						<Icon name="material-symbols:arrow-forward-ios-rounded"></Icon>
					</button>
				</div>


				<div class='note-container' v-if="checkCanOrder() == true">
					<Icon class="icon-label" name="solar:chat-line-linear"></Icon>
					<div class="content-container">
						<span class='label'>
							{{ $t('OrderComponent.ghi_chu_cho_cua_hang') }}
							<em class="text-length">({{ orderData.notes?.length ?? 0 }}/{{ appConst.max_text_short
							}})</em>
						</span>
						<div class='h-stack note-order-input-container'>
							<textarea :placeholder="$t('OrderComponent.ghi_chu_cho_cua_hang_placeholder')"
								:maxlength="appConst.max_text_long" v-model="orderData.notes" name="note-cart"
								id="note-cart" rows={5} class='note-order-input'></textarea>
						</div>
					</div>


				</div>

				<div class="payment-info-container">
					<span class='label'>
						{{ $t('OrderComponent.chi_tiet_thanh_toan') }}
					</span>
					<div class="payment-info-content">
						<span class="title">
							{{ $t('OrderComponent.tong_tien_hang') }}
						</span>
						<span class="value">
							{{
								getCartTotalPrice() ?
									formatCurrency(getCartTotalPrice() || 0, shopData?.currency)
									: $t('OrderComponent.gia_lien_he')
							}}
						</span>
					</div>
					<div class="payment-info-content off">
						<span class="title">
							{{ $t('OrderComponent.giam_gia') }}
						</span>
						<span class="value">
							{{ formatCurrency(((getCartTotalPriceOff() || 0) - (getCartTotalPrice() || 0)) || 0,
								shopData?.currency) }}
						</span>
					</div>
					<div class="payment-info-content">
						<span class="title">
							{{ $t('OrderComponent.phi_van_chuyen') }}
							<span v-if="driving_distance" class="distance">
								({{ driving_distance }} km)
							</span>

						</span>
						<span class="value" v-if="!delivery_price_checking">
							{{ deliveryPrice != null && listPartners?.length ? formatCurrency(deliveryPrice || 0) :
								`--` }}
						</span>
						<span class="value" v-else>
							{{ $t('OrderComponent.dang_kiem_tra') }}
						</span>
					</div>

					<div class="payment-info-content" v-if="Math.abs(discountDeliveryPrice ?? 0)">
						<span class="title">
							{{ $t('OrderComponent.giam_phi_van_chuyen') }}
						</span>
						<span class="value" v-if="!delivery_price_checking">
							{{ discountDeliveryPrice != null ? formatCurrency(discountDeliveryPrice || 0) :
								`--` }}
						</span>
						<span class="value" v-else>
							{{ $t('OrderComponent.dang_kiem_tra') }}
						</span>
					</div>

					<div class="payment-info-content total-left">
						<span class="title">
							{{ $t('OrderComponent.tong_thanh_toan') }}
						</span>
						<span class="value">
							{{
								getCartTotalPriceOff() ?
									formatCurrency((getCartTotalPriceOff() || 0) + parseFloat((deliveryPrice || 0).toString()) +
										parseFloat((discountDeliveryPrice || 0).toString()),
										shopData?.currency)
									: $t('OrderComponent.gia_lien_he')
							}}
							<!-- <span class="note" v-if="!driving_distance">
								{{ $t('OrderComponent.chua_bao_gom_phi_van_chuyen') }}
							</span> -->
						</span>
					</div>

				</div>

			</div>
			<div class="order-privacy"
				v-if="!refreshing && (selectedItemsCart && selectedItemsCart.length) && checkCanOrder() == true">
				<span>{{ $t('OrderComponent.dieu_khoan_dat_hang', { action: `"${t('OrderComponent.dat_hang')}"` })
				}}</span>
				<nuxt-link :to="appRoute.PolicyComponent">{{ $t('OrderComponent.dieu_khoan_remagan') }}</nuxt-link>
			</div>
			<div class="order-footer"
				v-if="!refreshing && (selectedItemsCart && selectedItemsCart.length) && checkCanOrder() == true">
				<div class="order-footer-content">
					<div class="total">
						<span>{{ $t('OrderComponent.tong') }} <em>({{ $t('OrderComponent.x_mat_hang', {
							x:
								selectedItemsCart.length
								?? 0
						}) }})</em></span>
						<span class="price">{{
							getCartTotalPriceOff() ?
								formatCurrency((getCartTotalPriceOff() || 0) + parseFloat((deliveryPrice || 0).toString()) +
									parseFloat((discountDeliveryPrice || 0).toString()),
									shopData?.currency)
								: $t('OrderComponent.lien_he_de_biet_gia')
						}}
							<span class="discount" v-if="Math.abs((getCartTotalPriceOff() || 0) - (getCartTotalPrice() ||
								0) - parseFloat((discountDeliveryPrice || 0).toString()))">
								{{ $t('OrderComponent.ban_da_tiet_kiem_duoc') }}
								<em>{{ formatCurrency((Math.abs((getCartTotalPriceOff() || 0) - (getCartTotalPrice() ||
									0) - parseFloat((discountDeliveryPrice || 0).toString()))) || 0,
									shopData?.currency) }}</em>
							</span>
						</span>
					</div>
					<button :disabled=isSubmiting v-on:click="() => {
						submitOrder()
					}">
						<Icon name="eos-icons:loading" size="20" v-if="isSubmiting" />{{
							$t('OrderComponent.dat_hang')
						}}
					</button>
				</div>

			</div>
			<div class="v-stack empty-cart" v-if="!refreshing && (!selectedItemsCart || !selectedItemsCart.length)">
				<img loading="lazy" :src='none_cart' :placeholder="none_cart" class="empty-cart-avt" />

				<span class="text">
					{{ $t('OrderComponent.gio_hang_trong') }}
				</span>
				<button class="action" v-on:click="() => {
					router.push({
						path: appRoute.AroundComponent,
						query: {
							isShowList: 'true'
						}
					})
				}">
					{{ $t('OrderComponent.hay_them_san_pham_vao_gio_hang') }}
				</button>

			</div>

			<client-only>
				<!-- <VueFinalModal class="my-modal-container" :click-to-close="false"
					content-class="my-modal-content-container edit-customer-info-modal" v-model="isEditingCustomerInfo"
					v-on:closed="() => {
						if (latitude && longitude) {
							getDistance()
						}
						isEditingCustomerInfo = false;

					}" contentTransition="vfm-slide-up">
					<div class='v-stack edit-customer-info-container'>
						<span class='title'>
							{{ $t('OrderComponent.thong_tin_nguoi_nhan') }}
						</span>

						<button class="select-user-saved" v-if="userInfo" v-on:click="() => {
							showSelectUser = true;
							isEditingCustomerInfo = false;
						}">
							{{ $t('OrderComponent.chon_tu_so_dia_chi') }}
						</button>
						<div class='v-stack'>
							<span class='label required'>
								{{ $t('OrderComponent.ten') }}
							</span>
							<input :title="$t(' OrderComponent.ten')" name='customer-name' maxLength=255
								autoComplete="off" class='input-order'
								:placeholder="$t('OrderComponent.ten_placeholder')" :value="name || null" v-on:input="($event: any) => {
									name = $event.target.value;
									nameValidation()
								}" v-on:blur="() => {
									nameValidation()
								}" />
							<span class='error-message'>{{ nameErr }}</span>
						</div>
						<div class='v-stack'>
							<span class='label required'>
								{{ $t('OrderComponent.so_dien_thoai') }}
							</span>
							<input :title="$t(' OrderComponent.so_dien_thoai')" name='customer-phone' maxLength=255
								type="phone" autoComplete="off" class='input-order'
								:placeholder="$t('OrderComponent.so_dien_thoai_placeholder')" :value="phone || null"
								v-on:input="($event: any) => {
									phone = validPhone($event.target.value);
									phoneValidation();
								}" v-on:blur="() => {
									phoneValidation();
								}" />
							<span class='error-message'>{{ phoneErr }}</span>
						</div>
						<div class='v-stack'>
							<span class='label required'>
								{{ $t('OrderComponent.dia_chi') }}
							</span>
							<input :title="$t('OrderComponent.dia_chi')" name='customer-address' maxLength=255
								autoComplete="off" class='input-order'
								:placeholder="$t('OrderComponent.dia_chi_placeholder')" :value="address || null"
								v-on:input="($event: any) => {
									address = $event.target.value;
									addressValidation()
								}" v-on:blur="() => {
									addressValidation()
								}" />
							<span class='error-message'>{{ addressErr }}</span>
						</div>
						<div class="map-container">
							<client-only>
								<LMap id="leaflet_map_order" height="200" v-on:ready="(e: any) => {
									leafletMap = e;
									leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);

									initLeafletMap();
								}" :max-zoom="appConst.leafletMapTileOption.maxZoom" v-on:update:center="async (bounds: any) => {
									console.log('center change');
									latitude = leafletMap.getCenter().lat;
									longitude = leafletMap.getCenter().lng;
									await getUserAddress();

								}" :options="{ zoomControl: false, zIndex: 1 }" :world-copy-jump="true" :use-global-leaflet="true">
									<LControlZoom position="bottomright"></LControlZoom>
									<span class="current-location-leaflet" :title="$t('OrderComponent.vi_tri_cua_ban')"
										v-on:click="() => {
											gotoCurrentLocationLeaflet();
										}
											">
										<Icon name="line-md:my-location-loop" class="my-location-icon" />
									</span>
									<div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
										data-placement="right"
										title="{{ $t('OrderComponent.nhan_de_chuyen_loai_map') }}" v-bind:style="{
											backgroundImage: `url(` + buttonMapTileBackgound + `)`,
										}" v-on:click="(event: any) => {
											if (event.isTrusted) {
												if (
													leafletMapTileUrl ==
													appConst.leafletMapTileUrl.roadmap
												) {
													leafletMapTileUrl =
														appConst.leafletMapTileUrl.hyprid;
													mapTypeTitle = $t('OrderComponent.ve_tinh');
													mapType = 'hyprid';
													buttonMapTileBackgound = map_sateline;
												} else if (
													leafletMapTileUrl ==
													appConst.leafletMapTileUrl.hyprid
												) {
													leafletMapTileUrl =
														appConst.leafletMapTileUrl.streetmap;
													mapTypeTitle = $t('OrderComponent.co_dien');
													mapType = 'hyprid';
													buttonMapTileBackgound = map_streetmap;
												} else if (
													leafletMapTileUrl ==
													appConst.leafletMapTileUrl.streetmap
												) {
													leafletMapTileUrl =
														appConst.leafletMapTileUrl.roadmap;
													mapTypeTitle = $t('OrderComponent.ve_tinh_nhan');
													mapType = 'roadmap';
													buttonMapTileBackgound = map_sateline;
												}
											} else event.preventDefault();
										}
											">
										<span>{{ mapTypeTitle }}</span>
									</div>
									<LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
										:max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5"
										layer-type="base" name="GoogleMap">
									</LTileLayer>
									<div class="marker-location">
										<img loading="lazy" :src="marker_location_icon"
											:placeholder="marker_location_icon" alt="" />
									</div>
								</LMap>
							</client-only>
						</div>
						<div class='h-stack save-info-container' v-if="false">
							<label :disabled="isSubmiting">
								<input type="radio" class='save-user-info-icon' :disabled="isSubmiting"
									:checked=saveUserInfo v-on:click="() => { saveUserInfo = !saveUserInfo }"
									:value=saveUserInfo />
								{{ " " }}{{ $t('OrderComponent.luu_thong_tin') }}
							</label>
						</div>

						<div class='h-stack edit-customer-info-actions'>
							<button class='reject-button' :disabled=isSavingUserInfo v-on:click="() => {
								name = orderData.customer_name;
								phone = validPhone(orderData.customer_phone);
								address = orderData.address;
								latitude = orderData.latitude;
								longitude = orderData.longitude;
								isEditingCustomerInfo = false;
								phoneValidation();

							}">
								{{ $t('OrderComponent.huy') }}
							</button>
							<button class='accept-button' :disabled="isSavingUserInfo == true
								|| !name?.length
								|| !phone?.length
								|| !address?.length
								|| nameErr.length > 0
								|| phoneErr.length > 0
								|| addressSeacrhing
								" v-on:click="() => {
									updateUserInfo()
								}">
								{{ $t('OrderComponent.xac_nhan') }}
							</button>
						</div>
					</div>
				</VueFinalModal> -->
				<EditReceiverInfoComponent :init_data="{
					name: name,
					phone: phone,
					address: address,
					latitude: latitude,
					longitude: longitude,
					province_id: province_id,
					district_id: district_id,
					ward_id: ward_id,
					note: address_note,
					images: JSON.parse(JSON.stringify(address_images))
				}" :user_info="JSON.parse(JSON.stringify(userInfo))" v-if="isEditingCustomerInfo" v-on:close="() => {
					if (latitude && longitude) {
						getDistance()
					}
					isEditingCustomerInfo = false;
					phoneValidation();
				}" v-on:show_select_user="() => {
					showSelectUser = true;
					isEditingCustomerInfo = false;
				}" v-on:submit_user="(data) => {
					console.log(data);
					name = data.name;
					phone = data.phone;
					address = data.address;
					latitude = data.latitude;
					longitude = data.longitude;
					province_id = data.province_id;
					district_id = data.district_id;
					ward_id = data.ward_id;
					address_images = data.images?.length ? JSON.parse(JSON.stringify(data.images)) : [];
					address_note = data.note;
					updateUserInfo();
					checkDeliveryPrice();
				}">

				</EditReceiverInfoComponent>
				<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
					content-class="my-modal-content-container form-modal saved-address-modal-container"
					:click-to-close="false" v-model="showSelectUser" v-on:closed="() => {
						showSelectUser = false
					}" contentTransition="vfm-slide-down">
					<SavedAddressComponent :mode="'order'" :selectedObject="{
						name: orderData.customer_name,
						phone: orderData.customer_phone,
						address: orderData.address,
						latitude: orderData.latitude,
						longitude: orderData.longitude,
						note: orderData.extra_data?.location?.note,
						images: orderData.extra_data?.location?.images,
					}" v-on:close="() => {
						showSelectUser = false;
					}" v-on:enterTempAddres="() => {
						showSelectUser = false;
						isEditingCustomerInfo = true;
					}" v-on:selectedUser="(selectedObj: any) => {
						showSelectUser = false;
						setDataOnSelected(selectedObj);
					}"></SavedAddressComponent>
				</VueFinalModal>
				<VueFinalModal class="my-modal-container" :overlay-behavior="'persist'"
					content-class="complete-order-container" :click-to-close="false" v-model="orderComplete"
					v-on:closed="() => {
						orderComplete = false
					}" contentTransition="vfm-slide-up">
					<div class='v-stack complete-order-container'>
						<img loading="lazy" :src='order_complete' :placeholder="order_complete"
							:alt="$t('OrderComponent.don_hang_da_duoc_gui')" class='complete-order-image' />
						<span class='complete-order-message'>
							{{ $t('OrderComponent.don_cua_ban_da_duoc_gui') }}
						</span>
						<button class='complete-order-action' v-on:click="() => {
							router.push(appRoute.HomeComponent)
						}">
							{{ $t('OrderComponent.dat_don_khac') }}
						</button>
					</div>

				</VueFinalModal>
			</client-only>

		</div>
		<DateTimePickerComponent v-if="showDateTimePickerModal" :title="t('OrderComponent.thoi_gian_nhan_hang')"
			:startDate="moment()" :endDate="moment().add(30, 'days')"
			:startTime="moment().hour(7).minutes(0).seconds(0).milliseconds(0)"
			:endTime="moment().hour(23).minutes(59).seconds(0).milliseconds(0)"
			:initialDate="orderData.delivery_time && moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').isValid() ? moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY') : null"
			:initialTime="orderData.delivery_time && moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').isValid() ? moment(orderData.delivery_time, 'DD/MM/YYYY HH:mm').format('HH:mm') : null"
			:firstItemTimeTitle="$t('OrderComponent.cang_nhanh_cang_tot')" :stepMinute="5" :firstItemTimeIsNull="false"
			:firstStepMinute="shopData.min_time_to_delivery ?? 30" v-on:close="() => {
				showDateTimePickerModal = false
			}" v-on:submit="(e: any) => {
				showDateTimePickerModal = false
				orderData.delivery_time = e;
				checkDeliveryPrice()
			}"></DateTimePickerComponent>

		<CustomSelectComponent v-if="showSelectDeliveryPartner" :_key="'select_delivery_partner'"
			:list_item="listPartners" :field_value="'id'" :multiple="false"
			:title="`${t('OrderComponent.don_vi_van_chuyen')}`"
			:class="'my-custom-select delivery-partner-custom-select'" :searchable="false"
			:model_value="selectedPartner.id" v-on:close="() => {
				showSelectDeliveryPartner = false;
			}" v-on:model:update="(e: any) => {
				let indexSelected = listPartners.findIndex(function (itemPartner: any) {
					return e == itemPartner.id
				})
				selectedPartner = indexSelected != -1 ? listPartners[indexSelected] : selectedPartner;
				// checkDeliveryPrice()
				getDistance();
			}">
			<template v-slot:title_icon_left>
				<Icon name="carbon:delivery"></Icon>
			</template>
			<template v-slot:render_item_option="{ item }">
				<div class="delivery-partner-item" :class="{
					'selected': item.id == selectedPartner.id
				}">
					<img class="partner-logo" :src="item?.information?.logo" v-if="item?.information?.logo"
						:alt="`logo ${item?.name}`">
					<span class="partner-name" v-else>
						{{ item?.name }}
					</span>
				</div>

			</template>
		</CustomSelectComponent>

		<v-overlay v-model="showChatToShop" :z-index="100" :absolute="false" :close-on-back="true" contained
			key="show_chat_detail" class="chat-detail-overlay-container" content-class='chat-detail-modal-container'
			no-click-animation v-on:click:outside="() => {
				showChatToShop = false;
			}">
			<ChatDetailComponent v-if="showChatToShop" :mode="member_type.user" :receiver_id="shopData?.id"
				:chat_info="chatToShopInfo" :receiver_type="true" v-on:close="() => {
					showChatToShop = false;
				}"></ChatDetailComponent>
		</v-overlay>
	</div>

</template>

<script lang="ts" setup>

import type { Icon } from 'leaflet';
import { toast } from 'vue3-toastify';
import { appConst, appDataStartup, domainImage, formatCurrency, showTranslateProductName, showTranslateProductDescription, validPhone } from "~/assets/AppConst";
import type { CartDto, OrderDto } from '~/assets/appDTO';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { CategoryService } from '~/services/categoryService/categoryService';
import { ImageService } from '~/services/imageService/imageService';
import { OrderService } from '~/services/orderService/orderService';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import { VueFinalModal } from 'vue-final-modal';
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import none_cart from "~/assets/image_08_05_2024/none-cart.png";
import moment from 'moment'
import { PlaceService } from '~/services/placeService/placeService';
import icon_for_product from '~/assets/image/icon-for-product.png';
import none_save_user from "~/assets/image_08_05_2024/none-save-user.jpg";
import order_complete from "~/assets/image/order-complete.png"
import { DeliveryService } from '~/services/orderService/deliveryService';
import { UserAddressService } from '~/services/userAddressService/userAddressService';
import SavedAddressComponent from '../savedAddress/SavedAddressComponent.vue';
import { HttpStatusCode } from 'axios';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import { DeliveryPartnerService } from '~/services/deliveryPartnerService/deliveryPartnerService';
import { channel_type, member_type, type ChannelDTO } from '../chatManage/ChatDTO';

const nuxtApp = useNuxtApp();

var router = useRouter();
var route = useRoute();
const { t, locale } = useI18n();
useSeoMeta({
	title: t('AppRouteTitle.OrderComponent')
})

const localeMap = {
    vi: "name_vi_vn",
    en: "name_en_us",
    ru: "name_ru_ru",
    ko: "name_ko_kr"
};

var authService = new AuthService();
var userService = new UserService();
var productService = new ProductService();
var shopService = new ShopService();
var categoryService = new CategoryService();
var imageService = new ImageService();
var orderService = new OrderService();
var placeService = new PlaceService();
var deliveryService = new DeliveryService();
var userAddressService = new UserAddressService();
var deliverPartnerService = new DeliveryPartnerService();

var webInApp = ref(null as any);

var leafletMap: L.Map;
var tempLeafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var markersCluster: any;

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('OrderComponent.ve_tinh_nhan'));
var mapType = ref("roadmap");
var zoom = ref(13);
var latitude = ref(null as any);
var longitude = ref(null as any);
var disabledDragTab = ref(false);
var addressSeacrhing = ref(false);
var buttonMapTileBackgound = ref(map_sateline);

var cartData = ref([] as CartDto[]);

var userInfo = ref(null as any);
var orderData = ref({} as OrderDto);
var deliveryType = ref(false);
var deliveryPrice = ref<number | null>(null);
var discountDeliveryPrice = ref<number | null>(null);
var paymentMethod = ref(1);
var name = ref("");
var nameErr = ref("");
var phone = ref("");
var phoneErr = ref("");
var address = ref("");
var addressErr = ref("");
var province_id = ref(null as any);
var district_id = ref(null as any);
var ward_id = ref(null as any);
var address_note = ref<any>("");
var address_images = ref<any>([]);

var isSubmiting = ref(false);
var orderComplete = ref(false);
var dataSavedUsers = ref([] as any);
var refreshing = ref(true);
var editing = ref(false);
var isEditingCustomerInfo = ref(false);
var isSavingUserInfo = ref(false);
var saveUserInfo = ref(false);
var showSelectUser = ref(false);
var showDateTimePickerModal = ref(false);

var searchProductTimeout: any;
var searchAddressTimeout: any;

var shopData = ref<any>();

var driving_distance = ref(0);
var selectedItemsCart = ref(router.options.history.state.selectedItemsCart ? JSON.parse(router.options.history.state.selectedItemsCart as string) : []);
var control: any;

var listPartners = ref<any>([]);
var selectedPartner = ref<any>(null);
var showSelectDeliveryPartner = ref(false);
var delivery_price_checking = ref(false);
var listOptionsDeliveryPrice = ref<any>(null);
var selectedDeliveryPrice = ref<any>();

var user_latitude = useState<any>('user_latitude', () => { return });
var user_longitude = useState<any>('user_longitude', () => { return });
// watch(() => [user_latitude.value, user_longitude?.value], () => {
// 	latitude.value = user_latitude?.value;
// 	longitude.value = user_longitude?.value;
// });
var checkDeliveryPriceTimeout: any;

var showChatToShop = ref(false);
var chatToShopInfo = ref<ChannelDTO>();

onUnmounted(() => {
	nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})

onBeforeMount(async () => {

	let webInAppRef = await localStorage.getItem(appConst.storageKey.webInApp);
	webInApp.value = webInAppRef ? JSON.parse(webInAppRef) : webInApp.value;

	refreshing.value = true;

	nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
		console.log('moving', coor);
		user_latitude.value = coor.latitude;
		user_longitude.value = coor.longitude;
	});
	// let dataSavedUsersTemp = await localStorage.getItem(appConst.storageKey.savedInfo);

	// dataSavedUsers.value = dataSavedUsersTemp ? JSON.parse(dataSavedUsersTemp as string) : [];
})
onMounted(async () => {

	let cartDataCurrent = await localStorage.getItem(appConst.storageKey.cart);
	cartData.value = cartDataCurrent ? JSON.parse(cartDataCurrent as string) : [];

	selectedItemsCart.value.forEach((element: any) => {
		let indexInCart = cartData.value.findIndex(function (e) {
			return e.product_id == element.product_id;
		})
		if (indexInCart != -1) element = JSON.parse(JSON.stringify(cartData.value[indexInCart]));
	});

	selectedItemsCart.value = selectedItemsCart.value?.length ? selectedItemsCart.value : JSON.parse(cartDataCurrent as string);
	await getShopInfo().then(async () => {
		await getShopDeliveryParters().then(res => {
			if (!listPartners.value.length) {
				// deliveryType.value = true;
			}
		});
	});

	let userInfoCurrent = await authService.checkAuth();
	userInfo.value = JSON.parse(JSON.stringify(userInfoCurrent));

	if (userInfo.value) {
		orderData.value = {
			...orderData.value,
			customer_id: userInfo.value ? userInfo.value.id : null,
		};
		await getMyListAddress();
		// setDataOnSelected(userInfo.value);
	}


	if (!orderData.value.latitude || orderData.value.longitude || !orderData.value.address?.length) {
		// isEditingCustomerInfo.value = true;
		getCurrentLocation();
	}

	// setFirstDeliveryTime();

	refreshing.value = false;

	setTimeout(() => {
		nameValidation();
		phoneValidation();
		addressValidation();
	}, 500);

})
onBeforeRouteUpdate(() => {

})
function getShopInfo() {
	return new Promise((resolve) => {
		shopService.detailShop(selectedItemsCart.value[0]?.shop?.id).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				shopData.value = JSON.parse(JSON.stringify(res.body.data));
			}
			else {
				shopData.value = null;
			}
			resolve(shopData.value);
		})
	})
}
async function getMyListAddress() {
	let res = await userAddressService.myListAddress();
	if (res.status == HttpStatusCode.Ok) {
		dataSavedUsers.value = res.body.data;
		let indexDefault = dataSavedUsers.value.findIndex(function (e: any) {
			return e.is_default == true;
		})

		if (indexDefault != -1) {
			setDataOnSelected(dataSavedUsers.value[indexDefault]);
		}
		else {
			setDataOnSelected(dataSavedUsers.value[0]);

		}
		// setTimeout(() => {
		// 	getDistance();
		// }, 1000);
	}
	else {
		dataSavedUsers.value = [];
	}
}
function getCartTotalPrice() {
	if (!selectedItemsCart.value.length) return 0;
	let total = 0;
	let indexPriceNull = selectedItemsCart.value.findIndex((e: any) => {
		return (e.product.price == 0 || e.product.price == null)
	})
	if (indexPriceNull != -1) return null;
	return selectedItemsCart.value
		.reduce((total: number, current: any) =>
			total + (parseFloat(current.product.price.toString()) ? parseFloat(current.product.price.toString()) * current.quantity : 0), 0
		)
}
function getCartTotalPriceOff() {
	if (!selectedItemsCart.value.length) return 0;
	let total = 0;
	let indexPriceNull = selectedItemsCart.value.findIndex((e: any) => {
		return (e.product.price_off != null && e.product.price_off < e.product.price) ? (e.product.price_off == 0 || e.product.price_off == null) : (e.product.price == 0 || e.product.price == null)
	})
	if (indexPriceNull != -1) return null;
	return selectedItemsCart.value
		.reduce((total: number, current: any) =>
			total + (
				(current.product.price_off)
					? parseFloat(current.product.price_off.toString()) * current.quantity
					: parseFloat(current.product.price.toString()) * current.quantity
			), 0)
}

function getCartTotalProduct() {
	if (!selectedItemsCart.value.length) return 0;
	return selectedItemsCart.value
		.reduce((total: number, current: any) =>
			total + (current.quantity ? current.quantity : 0), 0
		)
}

function nameValidation() {
	if (!name.value || !name.value.length) {
		nameErr.value = t('OrderComponent.vui_long_nhap_ten_nguoi_nhan')
	}
	else {
		nameErr.value = '';
	}
}

function phoneValidation() {
	let re = appConst.validateValue.phone;
	if (!validPhone(phone.value) || !validPhone(phone.value).length) {
		phoneErr.value = t('OrderComponent.vui_long_nhap_sdt')
		return;
	}
	if (!re.test(validPhone(phone.value))) {
		phoneErr.value = t('OrderComponent.sdt_khong_dung');
		return;
	}
	else {
		phoneErr.value = '';
	}
}

function addressValidation() {
	if (address.value?.length) {
		addressErr.value = "";
	}
	else addressErr.value = t('OrderComponent.vui_long_nhap_dia_chi');
}

async function submitOrder() {
	if (checkCanOrder() == false) {
		toast.warning(t('OrderComponent.cua_hang_dang_dong_cua'))
		return;
	}
	if (!latitude.value || !longitude.value || !name.value || !address.value || !phone.value) {
		isEditingCustomerInfo.value = true;
		return;
	}
	if (!orderData.value.delivery_time?.length) {
		showDateTimePickerModal.value = true;
		return;
	}
	isSubmiting.value = true;
	let orderTemp = {
		...orderData.value,
		// price: getCartTotalPrice(),
		// price_off: getCartTotalPriceOff(),
		delivery_type: deliveryType.value,
		delivery_price: deliveryPrice.value ?? null,
		delivery_price_estimate: deliveryPrice.value ?? null,
		delivery_time: orderData.value?.delivery_time?.length && moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").isValid() ? moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").format("YYYY-MM-DD HH:mm") : null,
		delivery_partner: selectedPartner.value?.name?.includes('remagan') ? 'remagan' : selectedPartner.value?.name?.toLowerCase(),
		delivery_partner_id: selectedPartner.value?.id ?? '',
		// 	delivery_price: 0,
		payment_method: paymentMethod.value,
		items: selectedItemsCart.value,
		shop_id: shopData.value?.id,
		delivery_distance: driving_distance.value,
		province_id: province_id.value,
		district_id: district_id.value,
		ward_id: ward_id.value,
		notes: orderData.value.notes ? orderData.value.notes : '',
		extra_data: {
			location: {
				note: address_note.value,
				images: address_images.value?.length ? JSON.parse(JSON.stringify(address_images.value)) : []
			}
		}
		// delivery: {
		// 	latitude_from: selectedItemsCart.value[0]?.shop?.latitude,
		// 	longitude_from: selectedItemsCart.value[0]?.shop?.longitude,
		// 	latitude_to: latitude.value,
		// 	longitude_to: longitude.value,
		// 	distance: driving_distance.value,
		// 	driver_id: null,
		// }
	}
	orderService.create(orderTemp).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			// localStorage.removeItem(appConst.storageKey.cart);
			clearNuxtState('cart_selected');
			selectedItemsCart.value.forEach((e: any) => {
				let indexInCart = cartData.value.findIndex(function (eCart) {
					return eCart.product_id == e.product_id
				})

				if (indexInCart != -1) {
					if (e.quantity >= cartData.value[indexInCart].quantity) {
						cartData.value.splice(indexInCart, 1);
					}
					else {
						cartData.value[indexInCart].quantity = cartData.value[indexInCart].quantity - e.quantity;
					}
				}
			});

			// router.options.history.state.selectedItemsCart = null;
			// router.currentRoute.value.params.selectedItemsCart = [];
			orderComplete.value = true;
			localStorage.setItem(appConst.storageKey.cart, JSON.stringify(cartData.value));
			selectedItemsCart.value = [];
			isSubmiting.value = false;
			router.push({
				path: appRoute.OrderComponent,
				force: true,
				state: {
					selectedItemsCart: null
				},
				replace: true
			})

			nuxtApp.$emit('cart_change');
		}
		else {
			isSubmiting.value = false;
			toast.error(res.body?.message ?? t('OrderComponent.dat_hang_that_bai'), {
				autoClose: 1000
			});
		}
	}).catch(err => {
		console.log(err);
		toast.error(t('OrderComponent.dat_hang_that_bai'), {
			autoClose: 1000,
		});
		isSubmiting.value = false;
	})
}

function updateUserInfo() {
	if (saveUserInfo.value) {
		let arr = JSON.parse(JSON.stringify(dataSavedUsers.value));
		let index = arr.findIndex((e: any) => {
			return e.name == name.value
				&& e.phone == phone.value
				&& e.address == address.value
				&& e.latitude == latitude.value
				&& e.longitude == longitude.value
		})

		if (index == -1 && userInfo?.value?.id) {
			let info = {
				id: null,
				name: name.value,
				address: address.value,
				phone: validPhone(phone.value),
				latitude: latitude.value,
				longitude: longitude.value,
				province_id: province_id.value,
				district_id: district_id.value,
				ward_id: ward_id.value,
				title: "new",
				is_default: false
			}
			// arr.push({
			// 	customer_name: name.value,
			// 	customer_phone: phone.value,
			// 	address: address.value,
			// 	latitude: latitude.value,
			// 	longitude: longitude.value,

			// });
			// localStorage.setItem(appConst.storageKey.savedInfo, JSON.stringify(arr));
			// dataSavedUsers.value = arr;
			userAddressService.create(info).then((res) => {
				if (res.status == HttpStatusCode.Ok) {
					getMyListAddress();
				}
				else {
					toast.error(res.body?.message ?? t('SavedAddressComponent.luu_dia_chi_that_bai'))
				}
			})
		}

	}
	orderData.value = {
		...orderData.value,
		customer_name: name.value,
		customer_phone: validPhone(phone.value),
		address: address.value,
		latitude: latitude.value,
		longitude: longitude.value,
		extra_data: {
			location: {
				note: address_note.value,
				images: address_images.value?.length ? JSON.parse(JSON.stringify(address_images.value)) : []
			}
		}
	};
	isEditingCustomerInfo.value = false;
}
async function getDistance() {
	// if (control) {
	// 	control.remove();
	// }
	console.log(latitude.value, longitude.value)
	control = nuxtApp.$L.Routing.control({
		waypointMode: 'connect',
		router: nuxtApp.$L.Routing.osrmv1({
			serviceUrl: appConst.urlOSRMv1,
			requestParameters: {
				overview: 'full',
				annotations: true,
				steps: true,
				alternatives: 2,

			},
		}),
		autoRoute: false,
		plan: new nuxtApp.$L.Routing.Plan([
			nuxtApp.$L.latLng(shopData.value?.latitude, shopData.value?.longitude),
			nuxtApp.$L.latLng(latitude.value, longitude.value)
		], {
			createMarker: () => (false),
		}),
	})
	if (tempLeafletMap) {
		control.route();
		control.addTo(tempLeafletMap).on('routesfound', (e: any) => {
			driving_distance.value = parseFloat((e.routes[0].summary.totalDistance / 1000).toFixed(1));
			checkDeliveryPrice();

		});
	}

}
function checkDeliveryPrice() {
	clearTimeout(checkDeliveryPriceTimeout);
	console.log(deliveryType.value, listPartners.value)
	checkDeliveryPriceTimeout = setTimeout(() => {
		if (!deliveryType.value && listPartners.value.length) {
			delivery_price_checking.value = true;
			let duration =
				(!orderData.value.delivery_time || moment(orderData.value.delivery_time, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes') < 30)
					? 30
					: moment(orderData.value.delivery_time, 'DD/MM/YYYY HH:mm').diff(moment(), 'minutes');

			let bodyRef = {};

			bodyRef = {
				partner: selectedPartner.value.name.toLowerCase().includes('remagan') ? 'remagan' : selectedPartner.value.name.toLowerCase(),
				shop_id: shopData.value?.id,
				distance: driving_distance.value,
				duration: duration,
				path: [
					{
						lat: shopData.value?.latitude,
						lng: shopData.value?.longitude,
						address: shopData.value?.address,
						name: shopData.value?.name,
						phone: shopData.value?.phone
					},
					{
						lat: latitude.value,
						lng: longitude.value,
						address: address.value,
						name: name.value,
						phone: phone.value,
					}
				],
				order: {
					...orderData.value,
					price: getCartTotalPrice(),
					price_off: getCartTotalPriceOff(),
					delivery_type: deliveryType.value,
					delivery_price: deliveryPrice.value ?? null,
					delivery_time: orderData.value?.delivery_time?.length && moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").isValid() ? moment(orderData.value.delivery_time, "DD/MM/YYYY HH:mm").format("YYYY-MM-DD HH:mm") : null,
					delivery_partner: selectedPartner.value?.name?.includes('remagan') ? 'remagan' : selectedPartner.value?.name?.toLowerCase(),
					delivery_partner_id: selectedPartner.value?.id ?? '',
					// 	delivery_price: 0,
					payment_method: paymentMethod.value,
					items: selectedItemsCart.value,
					shop_id: shopData.value?.id,
					delivery_distance: driving_distance.value,
					province_id: province_id.value,
					district_id: district_id.value,
					ward_id: ward_id.value,
					notes: orderData.value.notes ? orderData.value.notes : ''
				}
			}

			deliveryService.checkDeliveryPriceFromPartner(bodyRef).then((res) => {
				if (res.status == HttpStatusCode.Ok) {
					if (res?.body?.data) {
						listOptionsDeliveryPrice.value = isArray(res.body.data) ? res.body.data : [res.body.data];
						selectedDeliveryPrice.value = listOptionsDeliveryPrice.value[0];
						deliveryPrice.value = selectedDeliveryPrice.value.price;
						discountDeliveryPrice.value = selectedDeliveryPrice.value.discount;
						// using parterner distance instead map tool (leaflet) api.
						driving_distance = selectedDeliveryPrice.value.distance;
					}
					else {
						deliveryPrice.value = null;
						discountDeliveryPrice.value = null;
						listOptionsDeliveryPrice.value = null;
						selectedDeliveryPrice.value = null;
					}
				}
				else {
					deliveryPrice.value = null;
					discountDeliveryPrice.value = selectedDeliveryPrice.value.discount;
					listOptionsDeliveryPrice.value = null;
					selectedDeliveryPrice.value = null;
				}
				delivery_price_checking.value = false;
			}).catch(() => {
				deliveryPrice.value = null;
				delivery_price_checking.value = false;
				listOptionsDeliveryPrice.value = null;
				selectedDeliveryPrice.value = null;
			})
		}
		else {
			deliveryPrice.value = 0
			discountDeliveryPrice.value = null;
		}
	}, 300);


}

// async function initLeafletMap() {
// 	// markersCluster = new nuxtApp.$L.MarkerClusterGroup({
// 	// 	maxClusterRadius: 5,
// 	// 	iconCreateFunction: (cluster) => createClusterElement(cluster),
// 	// }).addTo(leafletMap);
// 	console.log("init map")
// 	await setCurrentLocationLeaflet();
// 	// (leafletMap as any)["gestureHandling"].enable();
// }
// async function setCurrentLocationLeaflet() {
// 	if (latitude.value && longitude.value) {
// 		console.log("có lat lng");
// 		leafletMap.setView([latitude.value, longitude.value], 17);
// 		// setLocationLeafletMarker(latitude.value, longitude.value);
// 	} else {

// 		// if ("geolocation" in navigator) {
// 		// 	navigator.geolocation.getCurrentPosition(
// 		// 		(position) => {

// 		// 			latitude.value = position.coords.latitude;
// 		// 			longitude.value = position.coords.longitude;
// 		// 			leafletMap.setView(
// 		// 				[position.coords.latitude, position.coords.longitude],
// 		// 				17
// 		// 			);
// 		// 			// getUserAddress()
// 		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
// 		// 		},
// 		// 		(error) => {
// 		// 			// toast.warning(t('OrderComponent.chua_cung_cap_vi_tri'), {
// 		// 			// 	autoClose: 1000,
// 		// 			// 	hideProgressBar: true,
// 		// 			// });
// 		// 			latitude.value = appConst.defaultCoordinate.latitude;
// 		// 			longitude.value = appConst.defaultCoordinate.longitude;
// 		// 			leafletMap.setView([latitude.value, longitude.value], 17);
// 		// 			// getUserAddress()
// 		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
// 		// 		},
// 		// 		{
// 		// 			enableHighAccuracy: false, // Use less accurate but faster methods
// 		// 			timeout: 5000, // Set a timeout (in milliseconds)

// 		// 		}
// 		// 	);
// 		// }
// 		latitude.value = user_latitude.value;
// 		longitude.value = user_longitude.value;
// 		leafletMap.setView(
// 			[user_latitude.value, user_longitude.value],
// 			17
// 		);
// 	}
// }
function getCurrentLocation() {
	// if ("geolocation" in navigator) {
	// 	navigator.geolocation.getCurrentPosition(
	// 		(position) => {

	// 			latitude.value = position.coords.latitude;
	// 			longitude.value = position.coords.longitude;
	// 			getDistance()
	// 		},
	// 		(error) => {
	// 			// toast.warning(t('OrderComponent.chua_cung_cap_vi_tri'), {
	// 			// 	autoClose: 1000,
	// 			// 	hideProgressBar: true,
	// 			// });
	// 			// latitude.value = appConst.defaultCoordinate.latitude;
	// 			// longitude.value = appConst.defaultCoordinate.longitude;
	// 			// getDistance()
	// 		},
	// 		{
	// 			enableHighAccuracy: false, // Use less accurate but faster methods
	// 			timeout: 5000, // Set a timeout (in milliseconds)

	// 		}
	// 	);
	// }
	latitude.value = user_latitude.value;
	longitude.value = user_longitude.value;
	getDistance()
}
// function getUserAddress() {
// 	clearTimeout(searchAddressTimeout);
// 	addressSeacrhing.value = true;
// 	searchAddressTimeout = setTimeout(() => {
// 		placeService
// 			.myGeocoderByLatLngToAddress(latitude.value, longitude.value)
// 			.then((res: any) => {
// 				if (res.body.data && res.body.data.length) {
// 					address.value = res.body.data[0].address
// 						? res.body.data[0].address
// 						: "";
// 					province_id.value = res.body.data[0].province_id ? res.body.data[0].province_id : null;
// 					district_id.value = res.body.data[0].district_id ? res.body.data[0].district_id : null;
// 					ward_id.value = res.body.data[0].ward_id ? res.body.data[0].ward_id : null;
// 					addressValidation()
// 				}
// 				addressSeacrhing.value = false;
// 			});
// 	}, 500);

// }

// async function gotoCurrentLocationLeaflet(event?: Event) {
// 	if (!event || event.isTrusted == true) {

// 		// if ("geolocation" in navigator) {
// 		// 	navigator.geolocation.getCurrentPosition(
// 		// 		(position) => {
// 		// 			leafletMap.flyTo(
// 		// 				[position.coords.latitude, position.coords.longitude],
// 		// 				17
// 		// 			);
// 		// 			latitude.value = position.coords.latitude;
// 		// 			longitude.value = position.coords.longitude;
// 		// 			getUserAddress()
// 		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
// 		// 		},
// 		// 		(error) => {
// 		// 			// toast.warning(t('OrderComponent.chua_cung_cap_vi_tri'), {
// 		// 			// 	autoClose: 1000,
// 		// 			// 	hideProgressBar: true,
// 		// 			// });
// 		// 			latitude.value = appConst.defaultCoordinate.latitude;
// 		// 			longitude.value = appConst.defaultCoordinate.longitude;
// 		// 			leafletMap.flyTo([latitude.value, longitude.value], 17);
// 		// 			getUserAddress()
// 		// 			// setLocationLeafletMarker(latitude.value, longitude.value);
// 		// 		},
// 		// 		{
// 		// 			enableHighAccuracy: false, // Use less accurate but faster methods
// 		// 			timeout: 5000, // Set a timeout (in milliseconds)

// 		// 		}
// 		// 	);
// 		// }

// 		leafletMap.flyTo(
// 			[user_latitude.value, user_longitude.value],
// 			17
// 		);
// 		latitude.value = user_latitude.value;
// 		longitude.value = user_longitude.value;
// 		getUserAddress()

// 	}
// }

function checkSelectedUserInfo(userInfo: any) {
	return userInfo.name == name.value
		&& userInfo.phone == phone.value
		&& userInfo.address == address.value
		&& userInfo.latitude == latitude.value
		&& userInfo.longitude == longitude.value
}

function setFirstDeliveryTime() {
	let currentDate = moment();

	if (moment().minutes() > 30) {
		currentDate.add(1, 'hours').minutes(0).seconds(0);
	}
	else {
		currentDate.minutes(30).seconds(0);
	}

	orderData.value.delivery_time = currentDate.format("DD/MM/YYYY HH:mm")
}

function setDataOnSelected(selectedObj: any) {
	orderData.value = {
		...orderData.value,
		customer_name: selectedObj ? selectedObj.name : null,
		customer_phone: selectedObj ? selectedObj.phone : null,
		address: selectedObj ? selectedObj.address : null,
		latitude: selectedObj ? selectedObj.latitude : null,
		longitude: selectedObj ? selectedObj.longitude : null,
		province_id: selectedObj ? selectedObj.province_id : null,
		district_id: selectedObj ? selectedObj.district_id : null,
		ward_id: selectedObj ? selectedObj.ward_id : null,
		extra_data: {
			location: {
				note: selectedObj?.note,
				images: selectedObj?.images ? JSON.parse(JSON.stringify(selectedObj?.images)) : []
			}
		}
	};
	name.value = selectedObj ? selectedObj.name : null;
	phone.value = selectedObj ? selectedObj.phone : null;
	address.value = selectedObj ? selectedObj.address : null;
	latitude.value = selectedObj ? selectedObj.latitude : null;
	longitude.value = selectedObj ? selectedObj.longitude : null;
	province_id.value = selectedObj ? selectedObj.province_id : null;
	district_id.value = selectedObj ? selectedObj.district_id : null;
	ward_id.value = selectedObj ? selectedObj.ward_id : null;
	address_note.value = selectedObj ? selectedObj.note : null;
	address_images.value = selectedObj?.images ? JSON.parse(JSON.stringify(selectedObj.images)) : null;
	getDistance();
	checkDeliveryPrice();
}

async function changeCartItem(cartItem: CartDto, index: number) {
	let cartArr = JSON.parse(JSON.stringify(selectedItemsCart.value));
	cartArr[index] = JSON.parse(JSON.stringify(cartItem));
	selectedItemsCart.value = JSON.parse(JSON.stringify(cartArr));
}

function getShopDeliveryParters() {
	let shop_id = shopData.value?.id;
	return new Promise((resolve) => {
		deliverPartnerService.shopListPartner(shop_id).then((res) => {
			if (res.status == 200) {
				listPartners.value = res.body?.partners.filter((e: any) => e.is_connected && e.is_enabled);
				let defaultPartner = res.body?.partners.filter((e: any) => e.is_connected && e.is_enabled && e.is_default);
				selectedPartner.value = defaultPartner[0] ?? listPartners.value[0] ?? null;
			}
			resolve(listPartners.value);
		})
	})

}

function checkCanOrder() {
	return shopData.value?.settings?.general?.is_open?.value == false ? false : true
}

function chatToShop() {
	chatToShopInfo.value = {
		members: [{
			member_id: shopData.value.id,
			member: JSON.parse(JSON.stringify(shopData.value)),
			member_type: member_type.shop
		}],
		name: null,
		type: channel_type.user,
		avatar: shopData.value.logo
	};
	showChatToShop.value = true;
}
</script>

<style lang="scss" src="./OrderV2Styles.scss"></style>
