<template>
  <div class="stock-reports-component">
    <!-- Sub Header -->
    <SubHeaderV2Component
      :title="$t('StockManagement.bao_cao_ton_kho')"
      :show-back="true"
      @back="$router.back()"
    />

    <!-- Header -->
    <div class="component-header">
      <p>{{ $t('StockManagement.xem_bao_cao_thong_ke') }}</p>
    </div>

    <!-- Date Filter -->
    <div class="filter-section">
      <div class="date-filter">
        <h3>{{ $t('StockManagement.chon_thoi_gian') }}</h3>
        <div class="date-range">
          <div class="date-input-group">
            <label>{{ $t('StockManagement.tu_ngay') }}</label>
            <input
              v-model="dateFilter.from"
              type="date"
              class="date-input"
              @change="loadReports"
            />
          </div>
          <div class="date-input-group">
            <label>{{ $t('StockManagement.den_ngay') }}</label>
            <input
              v-model="dateFilter.to"
              type="date"
              class="date-input"
              @change="loadReports"
            />
          </div>
        </div>
        
        <!-- Quick Date Filters -->
        <div class="quick-filters">
          <button 
            v-for="filter in quickFilters" 
            :key="filter.key"
            @click="setQuickFilter(filter.key)"
            :class="['quick-filter-btn', { active: activeQuickFilter === filter.key }]"
          >
            {{ filter.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="summary-section">
      <div v-if="loading" class="loading-state">
        <Icon name="solar:refresh-bold" size="32" class="loading-icon" />
        <p>{{ $t('StockManagement.dang_tai') }}</p>
      </div>

      <div v-else class="summary-cards">
        <div class="summary-card import">
          <div class="card-icon">
            <Icon name="solar:import-bold" size="24" />
          </div>
          <div class="card-content">
            <h3>{{ formatNumber(reportData.total_imports) }}</h3>
            <p>{{ $t('StockManagement.tong_nhap_kho') }}</p>
            <span class="card-value">{{ formatCurrency(reportData.total_import_value) }}</span>
          </div>
        </div>

        <div class="summary-card export">
          <div class="card-icon">
            <Icon name="solar:export-bold" size="24" />
          </div>
          <div class="card-content">
            <h3>{{ formatNumber(reportData.total_exports) }}</h3>
            <p>{{ $t('StockManagement.tong_xuat_kho') }}</p>
            <span class="card-value">{{ formatCurrency(reportData.total_export_value) }}</span>
          </div>
        </div>

        <div class="summary-card waste">
          <div class="card-icon">
            <Icon name="solar:trash-bin-trash-bold" size="24" />
          </div>
          <div class="card-content">
            <h3>{{ formatNumber(reportData.total_waste) }}</h3>
            <p>{{ $t('StockManagement.tong_hang_huy') }}</p>
            <span class="card-value">{{ formatCurrency(reportData.total_waste_value) }}</span>
          </div>
        </div>

        <div class="summary-card profit">
          <div class="card-icon">
            <Icon name="solar:chart-bold" size="24" />
          </div>
          <div class="card-content">
            <h3>{{ formatCurrency(reportData.estimated_profit) }}</h3>
            <p>{{ $t('StockManagement.loi_nhuan_uoc_tinh') }}</p>
            <span class="card-percentage" :class="getProfitClass(reportData.profit_margin)">
              {{ formatPercentage(reportData.profit_margin) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Reports -->
    <div class="reports-section">
      <!-- Top Products -->
      <div class="report-card">
        <div class="report-header">
          <h3>{{ $t('StockManagement.san_pham_ban_chay') }}</h3>
          <Icon name="solar:chart-2-bold" size="20" />
        </div>
        <div class="report-content">
          <div v-if="reportData.top_products?.length === 0" class="empty-report">
            <p>{{ $t('StockManagement.chua_co_du_lieu') }}</p>
          </div>
          <div v-else class="product-list">
            <div 
              v-for="(product, index) in reportData.top_products" 
              :key="product.id"
              class="product-item"
            >
              <div class="product-rank">{{ index + 1 }}</div>
              <div class="product-info">
                <h4>{{ product.name }}</h4>
                <p>{{ $t('StockManagement.da_ban') }}: {{ formatNumber(product.sold_quantity) }} {{ product.unit }}</p>
              </div>
              <div class="product-value">
                {{ formatCurrency(product.revenue) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Waste Analysis -->
      <div class="report-card">
        <div class="report-header">
          <h3>{{ $t('StockManagement.phan_tich_hang_huy') }}</h3>
          <Icon name="solar:danger-triangle-bold" size="20" />
        </div>
        <div class="report-content">
          <div v-if="reportData.waste_analysis?.length === 0" class="empty-report">
            <p>{{ $t('StockManagement.chua_co_du_lieu') }}</p>
          </div>
          <div v-else class="waste-list">
            <div 
              v-for="waste in reportData.waste_analysis" 
              :key="waste.reason"
              class="waste-item"
            >
              <div class="waste-reason">
                <Icon :name="getWasteIcon(waste.reason)" size="16" />
                {{ getWasteReasonText(waste.reason) }}
              </div>
              <div class="waste-stats">
                <span class="waste-quantity">{{ formatNumber(waste.quantity) }}</span>
                <span class="waste-value">{{ formatCurrency(waste.value) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Export Actions -->
    <div class="export-section">
      <button @click="exportReport" :disabled="loading" class="export-btn">
        <Icon name="solar:download-bold" size="20" />
        {{ $t('StockManagement.xuat_bao_cao') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue'
import { HttpStatusCode } from "axios";

// Props
const props = defineProps<{
  mode?: string
}>()

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Use the centralized shop data composable
const {
  activeShop,
  isLoading: shopDataLoading,
  initializeShopData,
  getShopBySlug
} = useShopData()

// Services
const stockService = new StockService()

// Reactive data
const loading = ref(false)
const activeQuickFilter = ref('week')

const dateFilter = ref({
  from: '',
  to: ''
})

const reportData = ref({
  total_imports: 0,
  total_exports: 0,
  total_waste: 0,
  total_import_value: 0,
  total_export_value: 0,
  total_waste_value: 0,
  estimated_profit: 0,
  profit_margin: 0,
  top_products: [],
  waste_analysis: []
})

// Quick filters
const quickFilters = computed(() => [
  { key: 'today', label: t('StockManagement.hom_nay') },
  { key: 'week', label: t('StockManagement.tuan_nay') },
  { key: 'month', label: t('StockManagement.thang_nay') },
  { key: 'quarter', label: t('StockManagement.quy_nay') }
])

const shopSlug = route.params.id as string
const shopId = computed(() => {
    const shop = getShopBySlug(shopSlug)
    return shop?.id || shopSlug
})


// Methods
const setQuickFilter = (filterKey: string) => {
  activeQuickFilter.value = filterKey
  const today = new Date()
  let fromDate = new Date()

  switch (filterKey) {
    case 'today':
      fromDate = new Date(today)
      break
    case 'week':
      fromDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      fromDate = new Date(today.getFullYear(), today.getMonth(), 1)
      break
    case 'quarter':
      const quarter = Math.floor(today.getMonth() / 3)
      fromDate = new Date(today.getFullYear(), quarter * 3, 1)
      break
  }

  dateFilter.value.from = fromDate.toISOString().split('T')[0]
  dateFilter.value.to = today.toISOString().split('T')[0]
  
  loadReports()
}

const formatNumber = (num: number) => {
  return new Intl.NumberFormat('vi-VN').format(num || 0)
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount || 0)
}

const formatPercentage = (value: number) => {
  return `${(value || 0).toFixed(1)}%`
}

const getProfitClass = (margin: number) => {
  if (margin > 20) return 'profit-high'
  if (margin > 10) return 'profit-medium'
  return 'profit-low'
}

const getWasteIcon = (reason: string) => {
  const icons = {
    expired: 'solar:calendar-mark-bold',
    damaged: 'solar:danger-bold',
    spoiled: 'solar:close-circle-bold',
    contaminated: 'solar:shield-warning-bold',
    quality_issue: 'solar:star-bold',
    customer_return: 'solar:restart-bold',
    other: 'solar:question-circle-bold'
  }
  return icons[reason] || 'solar:question-circle-bold'
}

const getWasteReasonText = (reason: string) => {
  const reasons = {
    expired: t('StockManagement.het_han'),
    damaged: t('StockManagement.hong_hoc'),
    spoiled: t('StockManagement.bi_hong'),
    contaminated: t('StockManagement.bi_nhiem'),
    quality_issue: t('StockManagement.chat_luong_kem'),
    customer_return: t('StockManagement.khach_tra_lai'),
    other: t('StockManagement.ly_do_khac')
  }
  return reasons[reason] || reason
}

const loadReports = async () => {
  if (!dateFilter.value.from || !dateFilter.value.to) return

  loading.value = true
  try {
    const reportRequest = {
      shop_id: shopId.value,
      date_from: dateFilter.value.from,
      date_to: dateFilter.value.to
    }

    const response = await stockService.getDailySummary(reportRequest)
    if (response.status === HttpStatusCode.Ok) {
      reportData.value = response.data || {}
    }
  } catch (error) {
    console.error('Error loading reports:', error)
  } finally {
    loading.value = false
  }
}

const exportReport = async () => {
  // Implementation for exporting report
  // This could generate a PDF or Excel file
  try {
    const reportRequest = {
      shop_id: shopId.value,
      date_from: dateFilter.value.from,
      date_to: dateFilter.value.to,
      format: 'excel'
    }

    // Call export API endpoint
    // const response = await stockService.exportReport(reportRequest)
    // Handle file download

    alert(t('StockManagement.xuat_bao_cao_thanh_cong'))
  } catch (error) {
    console.error('Error exporting report:', error)
    alert(t('StockManagement.loi_xuat_bao_cao'))
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize shop data first
  await initializeShopData()
  // Set default to current week
  setQuickFilter('week')
})
</script>

<style scoped>
.stock-reports-component {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750px;
}

.component-header {
  margin-bottom: 20px;
  text-align: center;

  p {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.filter-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 15px 0;
  }
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;

  .date-input-group {
    label {
      display: block;
      font-size: 12px;
      font-weight: 600;
      color: #7f8c8d;
      margin-bottom: 5px;
    }

    .date-input {
      width: 100%;
      border: 2px solid #ecf0f1;
      border-radius: 8px;
      padding: 10px 12px;
      font-size: 14px;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #3498db;
        background-color: #f8f9fa;
      }
    }
  }
}

.quick-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .quick-filter-btn {
    padding: 8px 16px;
    border: 2px solid #ecf0f1;
    border-radius: 20px;
    background: white;
    color: #7f8c8d;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3498db;
      color: #3498db;
    }

    &.active {
      background: #3498db;
      border-color: #3498db;
      color: white;
    }
  }
}

.summary-section {
  margin-bottom: 20px;
}

.loading-state {
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  color: #7f8c8d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .loading-icon {
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;

  .summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .card-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    &.import .card-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    &.export .card-icon {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }

    &.waste .card-icon {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    }

    &.profit .card-icon {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    }

    .card-content {
      flex: 1;

      h3 {
        font-size: 18px;
        font-weight: 700;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0 0 4px 0;
      }

      .card-value {
        font-size: 11px;
        font-weight: 600;
        color: #34495e;
      }

      .card-percentage {
        font-size: 11px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;

        &.profit-high {
          background: #d5f4e6;
          color: #27ae60;
        }

        &.profit-medium {
          background: #fef9e7;
          color: #f39c12;
        }

        &.profit-low {
          background: #fadbd8;
          color: #e74c3c;
        }
      }
    }
  }
}

.reports-section {
  margin-bottom: 20px;

  .report-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .report-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 2px solid #ecf0f1;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
      }
    }

    .report-content {
      .empty-report {
        text-align: center;
        padding: 20px;
        color: #7f8c8d;

        p {
          font-size: 14px;
          margin: 0;
        }
      }

      .product-list,
      .waste-list {
        .product-item,
        .waste-item {
          display: flex;
          align-items: center;
          gap: 15px;
          padding: 12px 0;
          border-bottom: 1px solid #ecf0f1;

          &:last-child {
            border-bottom: none;
          }
        }

        .product-item {
          .product-rank {
            width: 24px;
            height: 24px;
            background: #3498db;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
          }

          .product-info {
            flex: 1;

            h4 {
              font-size: 14px;
              font-weight: 600;
              margin: 0 0 4px 0;
              color: #2c3e50;
            }

            p {
              font-size: 12px;
              color: #7f8c8d;
              margin: 0;
            }
          }

          .product-value {
            font-size: 14px;
            font-weight: 600;
            color: #27ae60;
          }
        }

        .waste-item {
          .waste-reason {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #2c3e50;
          }

          .waste-stats {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;

            .waste-quantity {
              font-size: 12px;
              color: #7f8c8d;
            }

            .waste-value {
              font-size: 14px;
              font-weight: 600;
              color: #e74c3c;
            }
          }
        }
      }
    }
  }
}

.export-section {
  text-align: center;

  .export-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .stock-reports-component {
    padding: 10px;
  }

  .date-range {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .summary-cards {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .filter-section,
  .report-card {
    padding: 15px;
  }

  .quick-filters {
    .quick-filter-btn {
      font-size: 11px;
      padding: 6px 12px;
    }
  }
}
</style>
