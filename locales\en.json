{"AppRouteTitle": {"TabBottom": "<PERSON><PERSON>", "HomeComponent": "Remagan - Easy online buying and selling", "LoginComponent": "<PERSON><PERSON>", "SignupComponent": "Sign Up", "SplashScreenComponent": "Loading...", "ProfileComponent": "Profile", "ProfileInfoComponent": "Profile Information", "MyShopComponent": "My Shop", "MyOrdersComponent": "My Orders", "MyOrderDetailComponent": "Order Detail", "RegisterShopComponent": "Register shop with Remagan", "EditShopInfoComponent": "Edit shop information", "AddProductsFromSystemComponent": "Add products from the system", "BatchImportComponent": "Batch Import", "ShopProductsComponent": "Product List", "CreatePrivateProductComponent": "Add new product", "EditProductComponent": "Edit product", "DetailShopComponent": "Shop Information", "ElegantMenuShopComponent": "Super Menu", "MyShopManageComponent": "Management", "ManageCategoryComponent": "Categories", "ManageCategoriesComponent": "Management - Categories", "ManageProductsComponent": "Management - Products", "CreateCategoryComponent": "Create Category", "EditCategoryComponent": "Update Category", "ProductsCategoryComponent": "Categories", "ProductComponent": "Product Information", "CartComponent": "<PERSON><PERSON>", "OrderComponent": "Order Information", "ManageOrdersComponent": "Shop Orders", "MyShopOrderDetailComponent": "Order Detail", "MyShopEditOrderComponent": "Edit Order", "OAuthZaloComponent": "Get Zalo Information", "QuotationComponent": "Quotation List", "QuotationFilterComponent": "Quotation Check", "QuotationRequestComponent": "Request Quotation", "QuotationReplyComponent": "Create Quotation", "PageNotFoundComponent": "Page Not Found !!!", "DeleteAccountComponent": "Delete account", "OTPConfirmComponent": "OTP Verification", "ForgetPasswordComponent": "Forgot Password?", "WelcomeComponent": "Welcome to Remagan", "AboutComponent": "About us", "PolicyComponent": "Remagan Policy", "AroundComponent": "Nearby", "SearchComponent": "Search Everything", "AgentDashboardComponent": "Agent", "AgentShopManageComponent": "Shop Management", "AgentOrderManageComponent": "Consignment Orders", "AgentOrderDetailComponent": "Consignment Order Detail", "AgentEditOrderComponent": "Edit Consignment Order", "AgentCreateShopComponent": "Register shop with Remagan", "AgentEditShopInfoComponent": "Edit Shop Information", "AgentShopDetailDashboardComponent": "Authorized Shop Information", "AgentShopDetailInfoComponent": "Shop Information", "AgentAddProductsFromSystemComponent": "Add Products from the System", "AgentCreatePrivateProductComponent": "Add New Product", "AgentEditProductComponent": "Edit Product", "AgentShopCategoriesComponent": "Categories", "AgentProductsCategoryComponent": "Product List", "AgentShowProductInAShopComponent": "Product List", "ReelsComponent": "Explore", "SavedAddressComponent": "Address Book", "ShopConfigComponent": "Shop Configuration", "AddCommentComponent": "Add review", "DateTimePickerComponent": "Select time", "FindShipperComponent": "Find driver for the order", "CreateDeliveryComponent": "Place delivery order", "SelectUserInfoComponent": "User information", "SupportComponent": "Support", "CreatePasswordComponent": "Create password", "DriverDashboardComponent": "Tools for delivery person", "DeliveryHistoryComponent": "Delivery history", "DeliveryDetailComponent": "Delivery details", "MyShopCreateOrderComponent": "Create Order", "HourMinutePickerComponent": "Select time", "ChangeEmailComponent": "Change email", "ChatManageComponent": "Cha<PERSON>", "ChatDetailComponent": "Chat: {name}", "Product404Component": "Product not found", "ManageDeliveryPartnerComponent": "Delivery Partner"}, "DayInWeek": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "DayInWeekSort": {"monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat", "sunday": "Sun"}, "Map": {"google_map": "GoogleMap", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Click to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_va_nhan": "Satellite + labels", "vi_tri_mac_dinh": "Default location"}, "TimeUnit": {"giay": "second", "phut": "minute", "gio": "hour", "ngay": "day", "tuan": "week", "thang": "month", "nam": "year", "second": "second", "minute": "minute", "hour": "hour", "day": "day", "week": "week", "month": "month", "year": "year"}, "DefaultPage": {"re_ma_gan_da_co_phien_ban_moi": "Remagan has a new version.", "vui_long_cap_nhat_ung_dung_va_quay_lai": "Please update the app and try again.", "cap_nhat": "Update", "ban_dang_offline": "You are Offline", "vui_long_kiem_tra_ket_noi_va_tai_lai": "Please check your connection and reload.", "tai_lai": "Reload", "co_don_hang_moi": "there is a new order", "kiem_tra_don_hang": "Check orders", "tat_thong_bao_trong_5_phut": "Turn off notifications for 5 minutes"}, "FooterComponent": {"trang_chu": "Home", "kham_pha": "<PERSON><PERSON>", "gan_day": "Nearby", "gio_hang": "My Cart", "toi": "Profile"}, "ProfileComponent": {"trang_ca_nhan": "Personal Page", "cua_hang_cua_toi": "My Store", "tai_khoan": "Profile", "ca_nhan": "Personal", "thong_tin_tai_khoan": "Profile Information", "so_dia_chi": "Address Book", "yeu_cau_xoa_tai_khoan": "Account deletion request", "tao_mat_khau": "Create password", "dai_ly": "Agent", "quan_ly_cua_hang": "Store Management", "don_hang_ky_gui": "Consignment Orders", "tro_giup": "Help", "thong_tin_chung": "General Information", "cau_hinh": "Settings", "ngon_ngu": "Language", "ban_kinh_tim_kiem": "Search Radius", "dang_xuat": "Logout", "dang_nhap": "<PERSON><PERSON>", "dang_ky": "Register", "phien_ban": "Version", "doi_tai_khoan": "Switch Account", "cap_nhat_anh_dai_dien_thanh_cong": "Profile picture updated successfully", "cap_nhat_anh_dai_dien_that_bai": "Profile picture update failed", "don_hang_da_dat": "My orders", "chung": "General", "giao_hang": "Delivery", "ho_tro": "Faq", "cong_cu_cho_shipper": "Tools for delivery drivers", "quan_ly": "Management", "lich_su_giao_hang": "Delivery history", "chua_co_sdt": "Phone number not provided", "don_giao_hang": "Delivery order", "doi_mat_khau": "Change password", "tin_nhan": "Message", "nhan_thong_bao": "Receive notifications", "dang_bat": "Enabled", "dang_tat": "Disabled", "thay_doi": "Change", "mang_xa_hoi": "Social Network", "tai_ung_dung": "Download App", "fan_page": "Fan Page", "nhan_tin_qua_zalo": "Message via Zalo OA", "kenh_tiktok": "Tiktok Channel", "tai_qua_ch_play": "Download via CH Play", "tai_qua_app_store": "Download via App Store"}, "HomeComponent": {"giao_hang_den": "Delivery", "khong_co_dia_chi": "No address", "logo": "Logo", "tim_kiem_placeholder": "Search...", "goi_y": "Suggestions:", "xem_gan_day": "Recently viewed:", "gia_lien_he": "Contact for price", "them_vao_gio": "Add to cart", "dang_tai": "Loading...", "da_hien_thi_toan_bo_ket_qua": "All results displayed", "quang_cao": "Advertisement", "de_xuat": "Recommendation", "giam_gia": "Discount", "duoc_ua_chuong_nhat": "Most popular", "uu_dai_hot": "HOT Offer", "san_pham": "Product", "xem_het": "View all", "chua_co_san_pham_giam_gia": "No discounted products near by you", "cua_hang_duoc_ua_chuong_nhat": "Most popular store near by you", "xem_san_pham": "View product", "cac_chuong_trinh_uu_dai": "Ongoing promotions", "chua_co_uu_dai": "No offers yet", "loading_more": "Loading more", "chua_co_san_pham": "No products yet", "tim_kiem_them": "Search more", "lich_su_tim_kiem": "Search history", "danh_muc": "Category", "loai_hinh_kinh_doanh": "Business type"}, "CartComponent": {"gio_hang_trong": "Cart is empty", "hay_them_san_pham_vao_gio_hang": "Please add items to the cart", "tat_ca_$sl_san_pham": "All ({ count } items)", "gia_lien_he": "Contact for price", "xoa": "Remove", "ghi_chu_cho_san_pham": "Note for the item", "tong_tien": "Total", "vui_long_chon_san_pham": "Please select an item", "tiep_theo": "Next", "xoa_san_pham_khoi_gio_hang": "Remove the item from the cart?", "xoa_san_pham": "Remove item", "khoi_gio_hang": "from the cart?", "khong": "No", "co": "Yes", "xoa_tat_ca_san_pham_khoi_gio_hang": "Remove all items from the cart?", "mat_hang": "{count} items", "nhan_tin": "Message", "cua_hang_dang_dong_cua": "The store is closed.\nPlease leave a message"}, "LoginComponent": {"ve_trang_chu": "Back to home", "dang_ky": "Sign up", "dang_nhap": "Log in", "chao_mung_ban_den_voi_re_ma_gan": "Welcome to Rẻ mà gần", "dang_nhap_bang_email_ten_dang_nhap": "Log in with email / username", "dang_nhap_bang_so_dien_thoai": "Log in with phone number", "so_dien_thoai": "Phone number", "nhap_so_dien_thoai": "Enter phone number", "email_ten_dang_nhap": "Email / Username", "dia_chi_email": "Email address", "nhap_email_hoac_ten_dang_nhap": "Enter email or username", "mat_khau": "Password", "nhap_mat_khau": "Enter password", "quen_mat_khau": "Forgot password?", "hoac": "or", "dang_nhap_voi_google": "Log in with Google", "dang_nhap_voi_zalo": "Log in with <PERSON><PERSON>", "ban_can_hotro_them": "Need more help?", "lien_he_voi_chung_toi": "Contact us", "vui_long_nhap_so_dien_thoai": "Please enter phone number", "so_dien_thoai_khong_dung": "Invalid phone number", "vui_long_nhap_email": "Please enter email", "vui_long_nhap_mat_khau": "Please enter password", "dang_nhap_thanh_cong": "Login successful", "dang_nhap_that_bai": "<PERSON><PERSON> failed", "ban_da_dang_nhap": "You are already logged in", "thong_tin_khong_dung": "Incorrect information", "tai_khoan_da_bi_khoa": "The account has been locked.", "vui_long_lien_he": "Please contact", "ho_tro": "Support", "dang_nhap_voi_apple": "Sign in with Apple"}, "SignupComponent": {"ve_trang_chu": "Back to Home", "dang_nhap": "Log in", "dang_ky": "Sign up", "chao_mung_ban_tham_gia_vao_re_ma_gan": "Welcome to Remagan", "dang_ky_bang_email": "Sign up with email", "dang_ky_bang_so_dien_thoai": "Sign up with phone number", "so_dien_thoai": "Phone number", "nhap_so_dien_thoai": "Enter phone number", "ma_xac_thuc_dien_thoai_di_dong": "Mobile phone verification code", "ma_xac_thuc_so_dien_thoai": "Phone verification code", "lay_ma": "Get code", "phut": "m", "giay": "s", "lay_ma_qua": "Get code via", "zalo": "<PERSON><PERSON>", "sms": "SMS", "dia_chi_email": "Email address", "nhap_email": "Enter email", "ma_xac_thuc_email": "Email verification code", "nhap_lai_mat_khau": "Re-enter password", "nhap_ma_xac_thuc": "Enter verification code", "lay_lai_sau": "<PERSON><PERSON> later", "mat_khau": "Password", "nhap_mat_khau": "Enter password", "phai_chua_so_chu_cai_ky_tu_dac_biet": "Must contain numbers, letters, special characters", "ky_tu_dac_biet": "Special characters", "phai_tu_6_20_ky_tu": "Must be 6-20 characters", "xac_nhan_mat_khau": "Confirm password", "ten_dang_nhap": "Username", "vui_long_nhap_ten_dang_nhap": "Please enter username", "ten": "Name", "ten_placeholder": "<PERSON><PERSON><PERSON>", "ban_can_ho_tro_them": "Need more help?", "lien_he_voi_chung_toi": "Contact us", "dang_ky_thanh_cong": "Sign up successfully", "dang_ky_that_bai": "Sign up failed", "vui_long_nhap_ten": "Please enter name", "vui_long_nhap_so_dien_thoai": "Please enter phone number", "sdt_da_duoc_su_dung": "Phone number already in use", "so_dien_thoai_khong_dung": "Invalid phone number", "vui_long_nhap_email": "Please enter email", "vui_long_nhap_ma_xac_thuc": "Please enter verification code", "vui_long_nhap_mat_khau_xac_nhan": "Please enter confirm password", "nhap_lai_mat_khau_khong_khop": "Confirm password does not match", "email_da_duoc_su_dung": "Email has already in use", "email_khong_dung_dinh_dang": "Invalid email format", "ten_dang_nhap_da_duoc_su_dung": "Username has already in use", "ten_dang_nhap_regex": "Username must be at least 8 characters long, including letters, numbers, and underscores '_'", "ma_xac_thuc_sai": "Incorrect or expired verification code"}, "AroundComponent": {"giao_hang_den": "Delivery", "khong_co_dia_chi": "No address", "tim_kiem": "Search...", "hien_thi_ban_do": "Show map", "hien_thi_danh_sach": "Show list", "google_map": "Google Map", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Tap to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_va_nhan": "Satellite + labels", "chua_cap_dia_chi": "No address assigned", "gia_lien_he": "Contact for price", "goi_y": "Suggestions", "xem_gan_day": "Recently viewed", "loading": "Loading...", "khong_tim_thay": "No results found in your area.", "chuyen_qua_ban_do": "Switch to map and zoom out", "khoang_cach": "Distance", "xem_shop": "View shop", "da_hien_thi_toan_bo": "All results displayed", "them_vao_gio": "Add to cart", "chua_cung_cap_vi_tri": "You haven't provided a location! Using default location", "ban_do": "Map", "tat_ca": "All", "tim_kiem_khu_vuc_nay": "Search this area", "gan_toi": "Near me (current location)", "kham_pha": "Explore", "san_pham_da_xem": "Viewed products", "cua_hang_da_xem": "Viewed stores", "tim_kiem_them": "Search more", "lich_su_tim_kiem": "Search history", "luot_thich": "{count} likes", "luot_ban": "{count} sales", "danh_muc": "Category", "flash_sale": "Flash sale", "hang_tuyen": "Top-tier", "gia_thap_den_cao": "Price: Low to High", "gia_cao_den_thap": "Price: High to Low", "sap_xep": "Sort", "moi_nhat": "Newest", "san_pham_ban_chay": "Best-selling products", "san_pham_khuyen_mai": "Discounted products", "gan_nhat": "Nearest", "giam_gia": "Sale off"}, "ProductComponent": {"da_sao_chep_lien_ket": "Link copied", "khong_co_avatar": "No avatar", "quay_ve": "Go back", "toi_gio_hang": "Go to cart", "chia_se_qua_zalo": "Share via Zalo", "chia_se": "Share", "sao_chep_dia_chi": "Copy address", "chia_se_qua_facebook": "Share via Facebook", "khac": "Other...", "danh_gia": "rating", "Danh_gia": "Rating", "da_ban": "Sold { count }", "flash_sale": "FLASH SALE", "ket_thuc_sau": "ends in", "gia_lien_he": "Contact for price", "lua_chon": "Options", "chua_chon": "Not selected", "thay_doi": "Change", "noi_bat": "Featured", "note_1": "Instant noodles must be kept cold or stored to maintain flavor", "note_2": "The dough is soft, chewy, and particularly aromatic with turmeric and rich cheese", "note_3": "Includes passion fruit sauce, disposable gloves, and mouth wipes", "them_ghi_chu": "Add a note when adding to cart", "them_vao_gio": "Add to cart", "mua_nhanh": "Buy now", "ghi_chu_cho_san_pham": "Note for the product", "uu_dai": "Discount", "voucher_5": "5%", "voucher_10k": "10K", "voucher_35k": "35K", "voucher_25k": "25K", "voucher_22k": "22K", "voucher_12": "12%", "voucher_15k": "15K", "voucher_8": "8%", "xem_shop": "View shop", "ty_le_don_hang_thanh_cong": "Order success rate", "don_hang": "orders", "da_online": "Online", "phut_truoc": "minutes ago", "tuong_tu": "Similar", "thong_tin_phan_loai": "Classification information", "danh_muc": "Category", "thuong_hieu": "Brand", "xuat_xu": "Origin", "viet_nam": "Vietnam", "mo_ta": "Description", "thu_gon": "Collapse", "mo_rong": "Expand", "them_danh_gia": "Add review", "rat_khong_hai_long": "Very dissatisfied", "khong_hai_long": "Dissatisfied", "tam_on": "Neutral", "tot": "Good", "tuyet_voi": "Excellent", "xem_them": "See more", "hien_thi_tat_ca": "All reviews displayed", "loi_anh": "Image error. Click to reload"}, "AddProductToCartComponent": {"gia_lien_he": "Contact for price", "them_vao_gio": "Add to cart", "trong_gio_dang_co": "Quantity currently in the cart"}, "ShopComponent": {"tim_san_pham": "Search for products in the store", "chi_duong": "Directions", "sua_thong_tin": "Edit information", "go_call_cua_hang": "Call the store", "chia_se_qua_zalo": "Share via Zalo", "chia_se": "Share", "xem_menu": "View menu", "sao_chep_dia_chi": "Copy address", "chia_se_qua_facebook": "Share via Facebook", "khac": "Other...", "dang_giam_gia": "On sale", "tat_ca_san_pham": "All products", "menu_sieu_cap": "Super menu", "gia_lien_he": "Contact for price", "chinh_sua": "Edit", "them_vao_gio": "Add to cart", "khong_tim_thay_san_pham_nao": "No products found!", "ban_khong_co_quyen_truy_cap": "You do not have access", "da_sao_chep_lien_ket": "Link copied", "cua_hang_chua_cap_sdt": "The shop's phone number is not provided ", "gio_mo_cua": "Opening hours", "nguoi_theo_doi": "Followers", "": "Message", "goi": "Call", "theo_doi": "Follow", "da_theo_doi": "Following", "san_pham_hot": "Hot / Featured Product", "giam_gia": "Sale off", "noi_bat": "Featured", "sap_xep_danh_sach_san_pham": "Sort product list", "luot_ban": "{count} sales", "luot_thich": "{count} likes", "goi_dien_cho_shop": "Call", "chat_voi_shop": "Cha<PERSON>", "cua_hang_khong_ton_tai_hoac_ngung_hoat_dong": "Store does not exist or is no longer in operation", "dang_mo_cua": "Open", "da_dong_cua": "Closed", "khuyen_mai": "Promotion", "xem_dieu_kien": "View conditions", "khong_can_dieu_kien": "No conditions needed", "toi_da": "Maximum", "equal_to": "equal to", "greater_than": "greater than", "less_than": "less than", "greater_than_or_equal_to": "greater than or equal to", "less_than_or_equal_to": "less than or equal to", "not_equal_to": "not equal to", "in": "in", "not_in": "not in", "dieu_kien": "Condition", "gia_tri_don_hang": "Order value", "khoang_cach_giao": "Delivery distance", "giam_toi_da": "Maximum discount", "chi_tiet": "Details", "giam_x_phi_giao_hang": "Reduce {x} delivery fee", "and": "and", "or": "or", "xem_chi_tiet": "View details", "tinh_thanh_pho": "Province/City", "thoa_man_tat_ca_dieu_kien": "satisfy all conditions", "thoa_man_mot_trong_cac_dieu_kien": "satisfy one of the conditions", "thong_bao": "Notification", "che_do_hien_thi": "Display mode"}, "DirectionComponent": {"dinh_vi_chi_duong": "Get directions", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Tap to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_phan_hoi": "Satellite + labels", "vi_tri_hien_tai_cua_ban": "Your current location to the store", "khoang_cach": "Distance", "thoi_gian": "Time", "khong_tim_thay_tuyen_duong_phu_hop": "No suitable route found", "di_bo": "Walking", "xe_may": "Motorbike", "o_to": "Car", "chi_mang_tinh_chat_tham_khao": "For reference only", "xuat_phat": "Start"}, "MyShopComponent": {"trang_ca_nhan": "Personal Page", "che_do_khach": "Guest mode", "chua_co_ten": "No name yet", "chua_cap_sdt": "No phone number yet", "san_pham": "Products", "don_hang_cua_shop": "Shop orders", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "da_giao_hom_nay": "Delivered today", "dang_san_pham_moi": "Post new product", "dang_moi": "Post new", "them_tu_he_thong": "Add from system", "toan_bo_san_pham": "All products", "quan_ly_san_pham": "Manage products", "cua_hang": "Shop", "thong_tin_shop": "Shop information", "sua_thong_tin": "Edit information", "gioi_thieu_bio": "Bio introduction", "doanh_thu": "Revenue", "voucer_quang_cao": "Advertising voucher", "nguoi_dung_bi_chan": "Blocked users", "cai_dat_thong_bao": "Notification settings", "yeu_cau_xoa_cua_hang": "Request to delete shop", "tro_giup": "Help", "lien_he_cskh": "Contact customer service", "thong_tin_chung": "General information", "chua_dang_ky_cua_hang": "Shop not registered", "ban_chua_co_cua_hang": "You don't have a shop", "dang_ky_ngay": "Register now", "tin_nhan": "Message", "cua_hang_chua_co_san_pham": "The store has no products", "tao_moi": "Create new", "de_sau": "Remind me later", "don_vi_van_chuyen": "Shipping Partner", "cua_hang_bi_khoa": "Store is locked", "cau_hinh": "Settings", "cua_hang_dang_cho_duyet": "Waiting for system approval", "bao_gia": "Quotation"}, "EditShopInfoComponent": {"chinh_sua": "Edit: {name}", "ten_cua_hang": "Shop name", "nhap_ten_cua_hang": "Enter shop name", "vui_long_nhap_ten_cua_hang": "Please enter the shop name", "nganh_hang_chu_luc": "Main category", "hang_muc": "Category", "vui_long_chon_loai_hinh_kinh_doanh": "Please select a business type", "chua_chon": "Not selected", "chua_co_hang_muc_duoc_luu": "No categories saved", "khong_tim_thay": "Not found", "email": "Email", "nhap_dia_chi_email": "Enter email address", "email_khong_dung_dinh_dang": "Invalid email format", "so_dien_thoai": "Phone number", "nhap_so_dien_thoai": "Enter phone number", "vui_long_nhap_sdt": "Please enter phone number", "sdt_khong_dung": "Invalid phone number", "vui_long_chon_ngon_ngu": "Please select a language", "ngon_ngu": "Language", "ngon_ngu_cua_cua_hang": "Shop language", "don_vi_tien_te": "<PERSON><PERSON><PERSON><PERSON>", "don_vi_tien_te_cua_san_pham": "Product currency", "ghi_chu": "Notes", "thoi_gian_hoat_dong_mo_ta_duong_di": "Operating hours, directions, etc.", "dia_chi": "Address", "vui_long_nhap_dia_chi": "Please enter the address", "vi_tri_cua_ban": "Your location", "vui_long_chon_vi_tri": "Please select a specific location", "nhan_de_chuyen_loai_map": "Tap to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_nhan": "Satellite + labels", "tinh_thanh_pho": "Province/City", "tim_tinh_thanh_pho": "Search Province/City...", "chon_tinh_thanh_pho": "Select province/city", "danh_sach_tinh_thanh_pho_trong": "No provinces/cities found", "quan_huyen": "District", "tim_quan_huyen": "Search District...", "chon_quan_huyen": "Select district", "danh_sach_quan_huyen_trong": "No districts found", "phuong_xa": "Ward/Commune", "tim_phuong_xa": "Search Ward/Commune...", "chon_phuong_xa": "Select Ward/Commune", "danh_sach_phuong_xa_trong": "No Wards/Communes found", "logo_cua_hang": "Shop logo", "chua_chon_anh": "No image selected", "doi_anh": "Change image", "dieu_chinh": "Adjust", "xoa_anh": "Remove image", "lon_nho": "Size", "tren_duoi": "Top/bottom", "trai_phai": "Left/right", "banner_cua_hang": "Shop banner", "hoan_thanh": "Complete", "dang_xu_ly": "Processing", "bat_buoc": "Required", "tuy_chon": "Optional", "ban_khong_co_quyen_truy_cap": "You do not have access", "cap_nhat_cua_hang_thanh_cong": "Store updated successfully", "cap_nhat_logo_that_bai": "Logo update failed\nPlease try again later", "cap_nhat_banner_that_bai": "Banner update failed\nPlease try again later", "dien_day_du_thong_tin": "Please fill in all the information", "chua_cung_cap_vi_tri": "You haven't provided a location! Using default location", "cap_nhat_cua_hang_that_bai": "Store update failed\nPlease check the information", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "gio_mo_cua": "Opening hours", "anh_bia": "Banner", "anh_bia_kich_thuoc_de_xuat": "Recommended size 720x360px", "anh_dai_dien": "Avatar", "anh_dai_dien_kich_thuoc_de_xuat": "Recommended size 512x512px", "linh_vuc": "Field", "thiet_lap": "Settings", "gioi_thieu": "Introduction", "gioi_thieu_placeholder": "This introduction can boost your sales...", "cac_thong_tin_bat_buoc": "Fields marked with {char} are required", "mo_shop": "Open shop", "dang_ky_cua_hang_cua_ban": "Register your shop"}, "ElegantMenuComponent": {"menu": "MENU", "shop_name": "Shop Name", "bat_dau": "Start", "toi_gio_hang": "Go to Cart", "gia_lien_he": "Contact for price", "ghi_chu": "Note", "ghi_chu_placeholder": "Message for the shop...", "chua_tao_menu": "Menu is not created", "chua_co_san_pham": "No products available"}, "OrderComponent": {"chua_co_ten": "No Name", "chua_co_sdt": "No Phone number", "chua_co_dia_chi": "No Address provided", "gia_lien_he": "Contact for price", "hinh_thuc_thanh_toan": "Payment method", "thanh_toan_khi_nhan_hang": "Pay on delivery", "phuong_thuc_nhan_hang": "Delivery", "tu_lay_hang": "Self-Pickup", "giao_tan_noi": "Home delivery", "ghi_chu_cho_cua_hang": "Note for the Shop", "ghi_chu_cho_cua_hang_placeholder": "I would like...", "tam_tinh": "Subtotal", "giam_gia": "Discount", "thanh_tien": "Total", "chua_bao_gom_phi_van_chuyen": "Shipping fee is not included", "gio_hang_trong": "Empty cart", "hay_them_san_pham_vao_gio_hang": "Please add products to the cart", "thong_tin_nguoi_nhan": "Recipient information", "chon_tu_so_dia_chi": "Select from address book", "thong_tin_da_luu": "Saved information", "chua_luu_thong_tin": "No information saved", "ten": "Name", "ten_placeholder": "<PERSON><PERSON><PERSON>", "vui_long_nhap_ten_nguoi_nhan": "Please enter the recipient's name", "so_dien_thoai": "Phone number", "so_dien_thoai_placeholder": "0123456789", "vui_long_nhap_sdt": "Please enter phone number", "sdt_khong_dung": "Invalid phone number", "dia_chi": "Address", "dia_chi_placeholder": "No. xy, Street A, Ward B, District C,…", "vui_long_nhap_dia_chi": "Please enter address", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Click to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_nhan": "Satellite + Labels", "luu_thong_tin": "Save information", "huy": "Cancel", "xac_nhan": "Confirm", "chon_thong_tin_nguoi_nhan": "Select recipient information", "dang_chon": "Selecting", "chua_co_thong_tin_nao_duoc_luu": "No information saved", "don_hang_da_duoc_gui": "Order has been sent", "don_cua_ban_da_duoc_gui": "Your order has been sent", "dat_don_khac": "Place another order", "tong_tien": "Total amount", "dat_hang": "Order", "lien_he_de_biet_gia": "Contact for price", "doi_bao_gia": "Waiting for quotation", "phi_ship_null": "Shipping fee not determined, please call the store", "dat_hang_that_bai": "Order failed! Re-check order's information", "chua_cung_cap_vi_tri": "You haven't provided a location! Using default location", "phi_van_chuyen": "Shipping fee", "thoi_gian_nhan_hang": "Delivery time", "chon_ngay_gio": "Select date/time", "bay_gio": "Now", "thong_tin_se_duoc_bao_mat": "Your information will be kept confidential", "x_mat_hang_tong_cong_y": "{x} items, total {y}", "chi_tiet_thanh_toan": "Payment details", "tong_tien_hang": "Total item cost", "tong": "Total", "x_mat_hang": "{x} items", "tong_thanh_toan": "Total payment", "ban_da_tiet_kiem_duoc": "You have saved", "dieu_khoan_dat_hang": "Clicking {action} means you agree to", "dieu_khoan_remagan": "Rẻ mà <PERSON>'s Terms", "don_vi_van_chuyen": "Delivery unit", "thay_doi": "Change", "cang_nhanh_cang_tot": "As fast as possible", "dang_kiem_tra": "Checking...", "goi_dich_vu": "Service package", "chua_ket_noi_don_vi_van_chuyen": "Waiting for a quote from the store", "chua_chon": "Not selected", "giao_hang": "Delivery", "giam_phi_van_chuyen": "Discount delivery fee", "nhan_tin": "Message", "cua_hang_dang_dong_cua": "The store is closed.\nPlease leave a message"}, "ProfileInfoComponent": {"avatar": "Avatar", "ten": "Name", "ten_placeholder": "Name...", "vui_long_nhap_ten": "Please enter the user's name", "ten_dang_nhap": "Username", "ten_dang_nhap_placeholder": "Username...", "vui_long_nhap_ten_dang_nhap": "Please enter the username", "so_dien_thoai": "Phone number", "chua_cung_cap": "Not provided", "vui_long_nhap_so_dien_thoai": "Please enter the phone number", "dia_chi_email": "Email address", "gioi_tinh": "Gender", "ngay_sinh": "Date of Birth", "huy": "Cancel", "luu": "Save", "sua": "Edit", "nam": "Male", "nu": "Female", "khac": "Others", "email_da_duoc_su_dung": "Email has already in use", "email_khong_dung_dinh_dang": "Invalid email format", "vui_long_nhap_sdt": "Please enter phone number", "sdt_khong_dung": "Phone number is incorrect", "sdt_da_duoc_su_dung": "Phone number already in use", "ten_dang_nhap_regex": "Username must be at least 8 characters long, including letters, numbers, and underscores '_'", "ten_dang_nhap_da_duoc_su_dung": "Username has already in use", "cap_nhat_thanh_cong": "Updated successfully", "cap_nhat_that_bai": "Update failed", "doi_anh": "Change image", "xoa_anh": "Remove image", "hoan_tac": "Undo", "cap_nhat_anh_dai_dien_thanh_cong": "Profile picture updated successfully", "cap_nhat_anh_dai_dien_that_bai": "Profile picture update failed", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "huy_lien_ket_google": "Unlink Google", "da_lien_ket_google": "Linked with Google", "da_lien_ket_zalo": "Linked with <PERSON><PERSON>", "doi_email": "Change email", "ma_gioi_thieu": "Referral Code", "ma_gioi_thieu_placeholder": "Enter referral code...", "vui_long_nhap_ma_gioi_thieu": "Please enter referral code", "ma_gioi_thieu_regex": "Referral code must be at least 6 & max 20 characters with letters, numbers and underscore '_'", "ma_gioi_thieu_da_duoc_su_dung": "Referral code already taken", "tao_ma_gioi_thieu": "Create referral code", "sao_chep": "Copy", "da_sao_chep_ma_gioi_thieu": "Referral code copied", "sao_chep_that_bai": "Co<PERSON> failed"}, "SavedAddressComponent": {"mac_dinh": "<PERSON><PERSON><PERSON>", "dang_chon": "Selecting", "dung_dia_chi_mot_lan": "Use one time address ", "chon_tu_ban_do": "Select from map", "huy_dat_mac_dinh": "<PERSON><PERSON>", "dat_mac_dinh": "Set as <PERSON><PERSON><PERSON>", "chinh_sua": "Edit", "xoa": "Delete", "chua_co_dia_chi_nao_duoc_luu": "No addresses saved", "them_dia_chi_moi": "Add new address", "thong_tin": "Information", "ten": "Name", "so_dien_thoai": "Phone number", "dia_chi": "Address", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Tap to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_nhan": "Satellite + Labels", "vui_long_nhap_ten": "Please enter the name", "vui_long_nhap_dia_chi": "Please enter the address", "vui_long_nhap_sdt": "Please enter phone number", "sdt_khong_dung": "Invalid phone number", "ten_placeholder": "<PERSON><PERSON><PERSON>", "so_dien_thoai_placeholder": "0123456789", "dia_chi_placeholder": "123A Street, Ward B, District C...", "dong": "Close", "luu": "Save", "chua_cung_cap_vi_tri": "You haven't provided a location! Using default location", "dat_mac_dinh_that_bai": "Set as default failed", "cap_nhat_that_bai": "Update failed", "luu_dia_chi_that_bai": "Failed to save address\nPlease check the information", "ten_dia_chi": "Address name", "ten_dia_chi_placeholder": "Address name (e.g., School, Gym,...)", "nha_rieng": "Home", "cong_ty": "Work", "khac": "Other", "vui_long_nhap_ten_dia_chi": "Please enter an address name", "cap_nhat_thanh_cong": "Updated successfully", "them_thanh_cong": "Create successfully", "cap_nhat_dia_chi_khi_di_chuyen_ban_do": "Update address when moving the map", "nhap_toa_do": "Enter coordinates", "toa_do_x_y": "xxx.xxxx, yyy.yyyyy"}, "DeleteAccountComponent": {"xoa_tai_khoan": "Delete account", "xoa_tai_khoan_cua_hang_va_san_pham": "Delete my account, store, and products", "quay_lai": "Go back", "chac_chan": "Sure", "xoa_thanh_cong": "Request successfully submitted", "xoa_that_bai": "Request submission failed\nPlease try again later", "xoa_tai_khoan_ghi_chu": "Your account will be locked immediately and fully deleted after 30 days.\n Please contact us via email if there are any changes."}, "AgentShopManageComponent": {"tim_cua_hang": "Search for shops...", "hien_thi": "Showing { current }/{ total } shops", "khong_tim_thay_cua_hang": "No shops found", "dang_ky_ngay": "Register now", "chua_cung_cap_dia_chi": "Address not provided", "xem_thong_tin": "View Information", "tao_ban_sao": "Duplicate", "sua_thong_tin": "Edit Information", "xoa_cua_hang": "Delete Shop", "loading": "Loading...", "tao_cua_hang": "Create Shop", "khong": "No, maybe later", "co": "Yes, I'm sure", "ban_la_chu_cua_hang": "You are the shop owner", "chac_chan_xoa_cua_hang": "Are you sure to delete the shop?", "xoa_cua_hang_thanh_cong": "Store deleted successfully", "xoa_that_bai_vui_long_gui_lai_sau": "Store deletion failed \n Please try again later", "dang_cho_duyet": "Pending approval", "cua_hang_bi_khoa": "Store is locked", "mo_cua": "Open", "dong_cua": "Closed"}, "AgentShopDetailDashboardComponent": {"chua_cap_sdt": "Phone number not provided", "trang_ca_nhan": "Profile Page", "che_do_khach": "Guest Mode", "don_hang_cua_shop": "Shop Orders", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "da_giao_hom_nay": "Delivered Today", "dang_san_pham_moi": "Post New Product", "dang_moi": "New Post", "them_tu_he_thong": "Add from System", "toan_bo_san_pham": "All Products", "san_pham": "Products", "quan_ly_san_pham": "Manage Products", "cua_hang": "Shop", "thong_tin_shop": "Shop Information", "sua_thong_tin": "Edit Information", "gioi_thieu": "Introduction (Bio)", "danh_muc": "Category", "quan_ly": "Management", "doanh_thu": "Revenue", "voucer_quang_cao": "Voucher & Ads", "nguoi_dung_bi_chan": "Blocked Users", "cai_dat_thong_bao": "Notification Settings", "yeu_cau_xoa_cua_hang": "Request Shop Deletion", "tro_giup": "Help", "lien_he_cskh": "Contact Customer Service", "thong_tin_chung": "General Information", "alt_image": "Shop not registered", "khong_quyen_quan_ly": "You do not have management rights for this shop", "tin_nhan": "Message", "don_vi_van_chuyen": "Delivery Partner", "cua_hang_bi_khoa": "Store is locked", "cau_hinh": "Settings"}, "MyShopManageComponent": {"tieu_de": "Manage My Shop", "products": "Products", "categories": "Categories", "chua_dang_ky_cua_hang": "You have not registered a shop", "gia_tu_thap_den_cao": "Price: Low to High", "gia_tu_cao_den_thap": "Price: High to Low", "moi_nhat": "Newest", "xep_theo_ten_a_z": "Sort by Name A-Z", "xep_theo_ten_z_a": "Sort by Name Z-A", "dac_trung": "Featured"}, "ManageProductsComponent": {"tat_ca": "All", "tim_san_pham": "Search for products...", "hien_thi": "Showing {current}/{total} products", "dang_tai": "Loading...", "khong_co_san_pham": "No products", "khong_tim_thay_san_pham": "No matching products found!", "gia_lien_he": "Contact for price", "dang_moi": "Create a new product", "them_tu_he_thong": "Add from System", "nhap_hang_loat": "Batch Import", "tu_choi_quyen_truy_cap": "Access Denied", "ban_chua_dang_ky_cua_hang": "You have not registered a shop", "ton_kho": "Stock", "khong_quan_ly_ton_kho": "Not managing stock", "commission_percent": "Commission rate", "hien_thi_enable": "Display", "danh_muc": "Category", "chua_phan_loai": "Not classified"}, "BatchImportComponent": {"tieu_de": "Batch Import", "buoc_1": "Step 1: Enter Text", "buoc_2": "Step 2: Review and Confirm", "nhap_van_ban": "Enter product description text", "placeholder_van_ban": "Example:\nSaigon Beer 180 thousand per case\n180k Coca Cola bottle\nFish sauce case 250 thousand\nBread 15k\n50 thousand Fresh milk", "phan_tich": "Parse", "dang_phan_tich": "Parsing...", "ket_qua_phan_tich": "Parse Results", "san_pham_goc": "Original Product", "ten_san_pham": "Product Name", "so_luong": "Quantity", "don_vi": "Unit", "gia": "Price", "san_pham_de_xuat": "Suggested Products", "chon_san_pham": "Select Product", "khong_co_de_xuat": "No suggestions", "tao_moi": "Create New", "nhap_thanh_cong": "Successfully imported {count} products", "nhap_that_bai": "Import failed", "quay_lai": "Go Back", "xac_nhan_nhap": "Confirm Import", "dang_nhap": "Importing...", "loi_phan_tich": "Text parsing error", "vui_long_nhap_van_ban": "Please enter product description text", "don_vi_khac": "Other unit", "nhap_don_vi": "Enter new unit", "hinh_anh": "Image", "gia_ban": "Sale Price", "nhap_kho": "Inventory", "gia_nhap": "Import Price", "van_ban_goc": "Original Text"}, "ManageCategoriesComponent": {"san_pham": "p", "danh_sach_trong": "List is empty", "dang_tai": "Loading...", "them_danh_muc": "Add Category", "xoa_danh_muc": "Delete Category?", "ban_chac_chan_xoa_danh_muc": "Are you sure you want to delete the category {categoryName}?", "khong": "No", "co": "Yes", "tu_choi_quyen_truy_cap": "Access Denied", "ban_chua_dang_ky_cua_hang": "You have not registered a shop"}, "ResetCartComponent": {"tao_gio_hang_moi": "Create a new cart?", "ban_co_muon_xoa_gio_hang_tai": "Do you want to remove the cart at", "va_them_mon_moi_nay": "and add this new item?", "khong": "No", "co": "Yes"}, "ReelsComponent": {"gia_lien_he": "Contact for price", "them_vao_gio": "Add to cart", "chia_se": "Share", "sao_chep_dia_chi": "Copy address", "chia_se_qua_facebook": "Share on Facebook", "chia_se_qua_zalo": "Share on Zalo", "khac": "Others...", "da_sao_chep_lien_ket": "Link copied"}, "WelcomeComponent": {"bat_dau": "Start"}, "EditProductComponent": {"khong_co_avatar": "No avatar", "danh_gia": "review", "da_ban": "Sold {count}", "gia_lien_he": "Contact for price", "thong_tin_phan_loai": "Classification information", "danh_muc": "Category", "thuong_hieu": "Brand", "xuat_xu": "Origin", "viet_nam": "Vietnam", "mo_ta": "Description", "chua_phan_loai": "Not classified", "anh_san_pham": "Product image", "chon_anh": "Choose image", "xoa_anh": "Delete image", "mac_dinh": "<PERSON><PERSON><PERSON>", "ten_san_pham": "Product name", "mo_ta_san_pham": "Product description", "tao_danh_muc": "Create category", "tim_danh_muc": "Find category...", "da_chon": "Selected", "chon_danh_muc": "Select category", "chua_co_danh_muc": "No category saved", "khong_tim_thay": "Not found", "chon_danh_muc_san_pham": "Select product's category", "khong_tim_thay_danh_muc": "Category not found", "gia": "Price", "gia_ban_mac_dinh": "Default selling price", "commission_percent": "Commission rate", "de_trong_neu_dung_commission_cua_shop": "Leave empty to use shop's commission", "commission_percent_phai_la_kieu_so_2_thap_phan": "Commission rate must be a number with up to 2 decimal places", "commission_percent_phai_lon_hon_0": "Commission rate must be greater than or equal to 0%", "commission_percent_khong_vuot_qua_100": "Commission rate cannot exceed 100%", "vui_long_nhap_gia": "Please enter the selling price", "hien_thi_san_pham": "Show product", "san_pham_dac_trung": "Featured product", "san_pham_con": "Sub-product", "ten": "Name", "ten_san_pham_con": "Sub-product name...", "vui_long_nhap_ten_san_pham": "Please enter the product name", "vui_long_nhap_gia_muon_ban": "Please enter the desired selling price", "gia_uu_dai": "Discounted price", "de_trong_khi_khong_giam_gia": "Leave empty if no discount", "hien_thi": "Show", "xoa": "Delete", "hoan_tac": "Undo", "luu": "Save", "chinh_sua": "Edit", "xem": "View", "xoa_san_pham": "Delete product", "xoa_san_pham_con": "Delete sub-product", "khong": "No", "co": "Yes", "cap_nhat_san_pham_thanh_cong": "Product updated successfully", "cap_nhat_san_pham_that_bai": "Update failed\nPlease check the information", "xoa_san_pham_thanh_cong": "Product has been deleted", "xoa_san_pham_that_bai": "Deletion failed\nPlease try again later", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "so_luong_anh_toi_da": "The maximum number of images is {amount}", "ton_kho": "Stock", "khong_quan_ly_ton_kho": "Not managing stock", "de_trong_khi_khong_quan_ly_ton_kho": "Leave blank if not managing stock", "gia_phai_la_kieu_so": "Price must be a number", "gia_phai_la_kieu_so_2_thap_phan": "Price must be a number with up to 2 decimal places", "ton_kho_phai_la_kieu_so": "Stock must be a number", "de_trong_neu_gia_can_lien_he": "Leave blank if price is contact", "de_trong_neu_khong_giam_gia": "Leave blank if no discount"}, "CreatePrivateProductComponent": {"anh_san_pham": "Product image", "doi_anh": "Change image", "xoa_anh": "Delete image", "chon_anh": "Select image", "ten_san_pham": "Product name", "mo_ta_san_pham": "Product description", "danh_muc": "Category", "tao_danh_muc": "Create category", "chon_danh_muc_san_pham": "Select product's category", "tim_danh_muc": "Find category...", "da_chon": "Selected", "chon_danh_muc": "Select category", "chua_co_danh_muc": "No category saved", "khong_tim_thay": "Not found", "khong_tim_thay_danh_muc": "Category not found", "vui_long_nhap_ten_san_pham": "Please enter the product name", "vui_long_nhap_gia_muon_ban": "Please enter the desired selling price", "mon_chinh": "Main dish", "bo_chon": "Unselect if the product is a secondary item / gift / accessory", "dac_trung": "Feature", "uu_tien_hien_thi": "Display priority", "gia": "Price", "de_trong_neu_gia_can_lien_he": "Leave empty if price requires contact", "gia_uu_dai": "Discounted price", "de_trong_neu_khong_giam_gia": "Leave empty if no discount", "thoat": "Exit", "tao": "Create", "tieu_de": "Do not create product?", "thong_bao": "Your data will not be saved", "o_lai": "Stay", "them_thanh_cong": "Create successfully", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "so_luong_anh_toi_da": "The maximum number of images is {amount}", "ton_kho": "Stock", "khong_quan_ly_ton_kho": "Not managing stock", "de_trong_khi_khong_quan_ly_ton_kho": "Leave blank if not managing stock", "gia_phai_la_kieu_so": "Price must be a number", "gia_phai_la_kieu_so_2_thap_phan": "Price must be a number with up to 2 decimal places", "ton_kho_phai_la_kieu_so": "Stock must be a number", "unit": "Unit", "advanced_info": "Advanced information", "category_global": "System Category", "chon_danh_muc_global": "Select system category", "chi_hien_thi_noi_bo": "internal display only", "dang_tai_danh_muc": "Loading categories...", "tim_kiem_danh_muc": "Search categories..."}, "AddProductsFromSystemComponent": {"tim_san_pham": "Find system product...", "khong_tim_thay": "Product not found", "anh_dai_dien": "Product thumbnail", "loading": "Loading...", "da_chon": "Selected", "chon_het": "Select all", "bo_chon": "Unselect", "xong": "Done", "chua_chon": "No product selected", "gia_muon_ban": "Desired sale price", "thoat": "Exit", "luu": "Save", "chua_dang_ky_cua_hang": "You have not registered the store", "ban_khong_phai_chu_shop": "You are not the shop owner!\nPlease check again", "co_loi_xay_ra": "An error occurred!\nPlease try again later", "ton_kho": "Stock", "khong_quan_ly_ton_kho": "Not managing stock", "de_trong_khi_khong_quan_ly_ton_kho": "Leave blank if not managing stock"}, "CreateCategoryComponent": {"ten_danh_muc": "Category name", "vui_long_nhap_ten_danh_muc": "Please enter the category name", "danh_muc_cha": "Parent category", "tim_danh_muc": "Find category...", "danh_muc": "Category", "chon_danh_muc_cha": "Select parent category", "chua_co_danh_muc_duoc_luu": "No categories saved", "khong_tim_thay": "Not found {query}", "thoat": "Exit", "tao": "Create", "tao_danh_muc_thanh_cong": "Category created successfully", "tao_danh_muc_that_bai": "Category creation failed", "anh_dai_dien": "Profile picture", "chua_chon_anh": "No image selected", "doi_anh": "Change image", "xoa_anh": "Delete image", "dung_luong_anh_toi_da": "Maximum image size is {size}MB", "luu_anh_that_bai": "Failed to save image"}, "ProductsCategoryComponent": {"cap_nhat_danh_sach": "Update list", "chua_co_san_pham_nao": "No products available", "xoa_san_pham": "Remove product", "them_san_pham": "Add product", "them_san_pham_cua_ban": "Add your products...", "khong_tim_thay_san_pham": "Product not found", "da_hien_thi_toan_bo_ket_qua": "All results displayed", "dang_tai": "Loading...", "da_chon": "Selected", "quay_lai": "Back", "bo_chon": "Unselect", "luu": "Save", "chon_san_pham_xoa_khoi_danh_muc": "Select products to remove from category", "chua_co_san_pham": "No products", "xoa": "Remove", "xoa_danh_muc": "Delete category?", "ban_chac_chan_muon_xoa_danh_muc": "Are you sure you want to delete the category?", "khong": "No", "co": "Yes", "ban_chua_dang_ky_cua_hang": "You have not registered the store", "cap_nhat_that_bai": "Update failed", "cua_hang_khong_ton_tai": "The shop does not exist or has been closed"}, "EditCategoryComponent": {"ten_danh_muc": "Category name", "vui_long_nhap_ten_danh_muc": "Please enter the category name", "danh_muc_cha": "Parent category", "tim_danh_muc": "Find category...", "danh_muc": "<PERSON><PERSON>", "chon_danh_muc_cha": "Select parent category", "chua_co_danh_muc_duoc_luu": "No categories saved", "khong_tim_thay": "Not found", "thoat": "Exit", "luu": "Save", "cap_nhat_danh_muc_thanh_cong": "Category updated successfully", "cap_nhat_danh_muc_that_bai": "Category update failed", "anh_dai_dien": "Profile picture", "chua_chon_anh": "No image selected", "doi_anh": "Change image", "xoa_anh": "Delete image", "dung_luong_anh_toi_da": "Maximum image size is {size}MB", "luu_anh_that_bai": "Failed to save image"}, "ManageOrdersComponent": {"tim_theo_ma_don_hang": "Search by order number", "tat_ca": "All", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "hoan_thanh": "Completed", "tra_hang": "Returns", "tu_choi": "Rejected", "moi": "New", "huy_bo": "Cancel", "xac_nhan": "Confirm", "da_giao": "Delivered", "tong_cong": "Total", "danh_sach_trong": "List is empty", "khong_tim_thay_don_hang_nao": "No orders found", "lam_moi": "Refresh", "dang_tai": "Loading...", "chua_dang_ky_cua_hang": "Store is not registered", "ban_chua_co_cua_hang": "You do not have a store", "dang_ky_ngay": "Register now", "tu_choi_nhan_don_hang": "Reject order?", "huy_don_hang": "Cancel order", "khong": "No", "co": "Yes", "loc_don_hang": "Filter orders", "chua_cung_cap": "Not provided", "dong": "Close", "ap_dung": "Apply", "toan_thoi_gian": "All time", "hom_nay": "Today", "hom_qua": "Yesterday", "thang_nay": "This month", "thang_truoc": "Last month", "thoi_gian_khac": "Other time", "thong_tin_giao_hang": "Delivery information", "chua_tao": "Not created", "trang_thai_giao_hang_1": "Waiting for {driver_name} to confirm", "trang_thai_giao_hang_2": "{driver_name} has confirmed", "trang_thai_giao_hang_3": "Order is in progress", "trang_thai_giao_hang_4": "{driver_name} has picked up the order", "trang_thai_giao_hang_5": "{driver_name} is delivering", "trang_thai_giao_hang_6": "{driver_name} has successfully delivered", "trang_thai_giao_hang_7": "{driver_name} declined the order", "trang_thai_giao_hang_8": "Delivery unsuccessful", "chua_chon_shipper": "No driver selected", "khach_hang_tu_toi_lay": "Customer will pick up", "tao_don_hang": "Create order", "chon_cua_hang": "Choose another shop", "cua_hang_khong_ton_tai": "The shop does not exist or has been closed", "tao_moi": "Create new", "chi_duong": "Directions"}, "MyShopOrderDetailComponent": {"dang_tai": "Loading...", "ma_don": "Order code", "chua_co_ma": "No code yet", "da_sao_chep_ma_don": "Order code copied", "sao_chep": "Copy", "trang_thai": "Status", "mo_rong": "Expand", "giao_hang_thanh_cong": "Delivery successful", "xac_nhan": "Confirm", "dat_hang": "Order", "nguoi_nhan": "Recipient", "goi_dien": "Call", "nhan_tin": "Message", "gia_lien_he": "Contact for price", "ghi_chu_don_hang": "Order notes", "hinh_thuc_thanh_toan": "Payment method", "thanh_toan_khi_nhan_hang": "Cash on delivery", "hinh_thuc_nhan_hang": "Delivery method", "tu_toi_lay": "Self-Pickup", "toi_can_giao_tan_noi": "Need a delivery", "tam_tinh": "Subtotal", "giam_gia": "Discount", "phi_van_chuyen": "Shipping fee", "thanh_tien": "Total", "chua_bao_gom_phi_van_chuyen": "Shipping fee is not included", "huy_don": "Cancel order", "tu_choi_don": "Reject order", "nhan_don": "Accept order", "da_giao": "Delivered", "xong": "Done", "xem_don_khac": "View another order", "tu_choi_nhan_don_hang": "Reject this order?", "huy_don_hang": "Cancel order", "khong": "No", "co": "Yes", "khong_tim_thay_don_hang": "Order not found", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "hoan_thanh": "Completed", "tra_hang": "Returns", "tu_choi": "Rejected", "don_hang_khong_ton_tai": "Order does not exist", "co_loi_xay_ra": "An error occurred.\nPlease try again later", "ban_khong_phai_chu_cua_hang": "You are not the store owner.", "dat_giao_hang": "Place delivery order", "thong_tin_giao_hang": "Delivery information", "trang_thai_giao_hang": "Delivery status", "cap_nhat": "Update", "chi_tiet_don_giao": "Delivery order details", "cod": "Advance payment", "thoi_gian_lay_hang": "Pick-up time", "giao_trong": "Deliver within", "phut": "minutes", "thong_tin_goi_hang": "Package information", "khoi_luong": "Weight", "less_2_kg": "< 2kg", "from_2_to_5_kg": "2 - 5kg", "from_5_to_10_kg": "5 - 10kg", "more_10_kg": "> 10kg", "yeu_cau_dac_biet": "Special requirements", "giao_tan_tay": "Hand delivery", "giao_hang_cong_kenh": "Bulky delivery", "mo_ta_giao_hang_cong_kenh": "Standard 50x40x60: 30kg | {price}", "giao_hang_de_vo": "Fragile delivery", "gia_di_kem": "{price}", "quay_lai_diem_lay_hang": "Return to pick-up point", "gui_sms_cho_nguoi_nhan": "Send SMS to recipient", "tui_giu_nhiet": "Thermal bag", "ho_tro_tai_xe": "Driver support", "tai_xe": "Driver", "chua_chon_shipper": "No driver selected", "chua_chon": "Not selected", "bay_gio": "Now", "ghi_chu_cho_tai_xe": "Notes for the driver", "muon_nhan_hang_luc": "Want to receive the item at", "chua_tao_don_giao": "Delivery order not created", "khoang_cach": "Distance", "trang_thai_giao_hang_1": "Waiting for confirmation", "trang_thai_giao_hang_2": "Confirmed", "trang_thai_giao_hang_3": "Order is in progress", "trang_thai_giao_hang_4": "Picked up", "trang_thai_giao_hang_5": "Delivering", "trang_thai_giao_hang_6": "Successfully delivered", "trang_thai_giao_hang_7": "Driver did not accept the order", "trang_thai_giao_hang_8": "Delivery failed", "anh_don_hang": "Order image", "toi_thieu_x_anh": "At least {amount} images", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "so_luong_anh_toi_thieu": "Minimum number of images is {amount}", "so_luong_anh_toi_da": "The maximum number of images is {amount}", "luu": "Save", "hoan_tac": "Undo", "ban_chua_luu_anh": "You haven't saved the image?", "thao_tac_anh_don_hang_chua_luu": "Complete saving the image and try again", "bo_qua": "<PERSON><PERSON>", "xem_lai": "Review", "vi_tri": "Location", "vi_tri_shipper": "<PERSON><PERSON>'s location", "trang_thai_don_giao_1": "Pending confirmation", "trang_thai_don_giao_2": "Confirmed", "trang_thai_don_giao_3": "Order is being processed", "trang_thai_don_giao_4": "Picked up", "trang_thai_don_giao_5": "Delivering", "trang_thai_don_giao_6": "Successfully delivered", "trang_thai_don_giao_7": "Delivery canceled", "trang_thai_don_giao_8": "Delivery failed", "huy_don_giao": "Cancel delivery", "lo_trinh": "Route", "loai_dich_vu_giao": "Type of delivery service", "chua_xac_dinh": "Not determined", "chi_duong": "Directions", "chon_shipper_khac": "Choose another driver", "lay_ngay": "Get now", "giam_phi_van_chuyen": "Discount delivery fee", "phi_van_chuyen_khach_da_chon": "Delivery's fee selected by customer", "xem_chi_tiet": "View details"}, "MyShopEditOrderComponent": {"ma_don_hang": "Order code:", "them_san_pham": "Add product", "anh_san_pham": "Product image", "anh_dai_dien": "<PERSON><PERSON><PERSON><PERSON>", "dia_chi": "Address", "dia_chi_placeholder": "Address...", "vui_long_nhap_dia_chi": "Please enter the address", "tong": "Total", "san_pham": "product", "phi_van_chuyen": "Shipping fee", "giam_gia": "Discount", "tong_cong": "Total amount", "ghi_chu": "Note", "ghi_chu_don_hang": "Order notes...", "huy_thay_doi": "Cancel changes", "cap_nhat": "Update", "tim_san_pham": "Find product...", "da_chon": "Selected", "bo_chon": "Unselect", "xong": "Done", "don_hang_khong_ton_tai": "Order does not exist", "khong_tim_thay_don_hang": "Order not found"}, "AgentOrderManageComponent": {"tim_cua_hang": "Search for shops...", "tim_theo_ma_don_hang": "Search by order code", "tat_ca": "All", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "hoan_thanh": "Completed", "tra_hang": "Returns", "tu_choi": "Rejected", "moi": "New", "huy_bo": "Cancel", "xac_nhan": "Confirm", "da_giao": "Delivered", "tong_cong": "Total", "danh_sach_trong": "List is empty", "khong_tim_thay_don_hang_nao": "No orders found", "lam_moi": "Refresh", "dang_tai": "Loading...", "chua_dang_ky_cua_hang": "Store is not registered", "ban_chua_co_cua_hang": "You do not have a store", "dang_ky_ngay": "Register now", "tu_choi_nhan_don_hang": "Reject order?", "huy_don_hang": "Cancel order", "khong": "No", "co": "Yes", "loc_don_hang": "Filter orders", "chua_cung_cap": "Not provided", "dong": "Close", "ap_dung": "Apply", "toan_thoi_gian": "All time", "hom_nay": "Today", "hom_qua": "Yesterday", "thang_nay": "This month", "thang_truoc": "Last month", "thoi_gian_khac": "Other time", "ban_khong_co_quyen_truy_cap": "You do not have access", "tao_don_hang": "Create order"}, "AuthZaloComponent": {"dang_cho_xac_nhan": "Waiting for confirmation...", "dang_nhap_thanh_cong": "<PERSON><PERSON> successfully", "dang_nhap_that_bai": "<PERSON><PERSON> failed", "khong_nhan_duoc_token": "Zalo token isn't provided", "khong_nhan_duoc_verify_code": "Verify code isn't provided"}, "AgentOrderDetailComponent": {"dang_tai": "Loading...", "ma_don": "Order code", "chua_co_ma": "No code yet", "da_sao_chep_ma_don": "Order code copied", "sao_chep": "Copy", "trang_thai": "Status", "mo_rong": "Expand", "giao_hang_thanh_cong": "Delivery successful", "xac_nhan": "Confirm", "dat_hang": "Order", "nguoi_nhan": "Recipient", "zalo": "<PERSON><PERSON>", "goi_dien": "Call", "gia_lien_he": "Contact for price", "ghi_chu_don_hang": "Order notes", "hinh_thuc_thanh_toan": "Payment method", "thanh_toan_khi_nhan_hang": "Cash on delivery", "hinh_thuc_nhan_hang": "Delivery method", "tu_toi_lay": "Self-Pickup", "toi_can_giao_tan_noi": "Need a delivery", "tam_tinh": "Subtotal", "giam_gia": "Discount", "phi_van_chuyen": "Shipping fee", "thanh_tien": "Total", "chua_bao_gom_phi_van_chuyen": "Shipping fee is not included", "huy_don": "Cancel order", "tu_choi_don": "Reject order", "nhan_don": "Accept order", "da_giao": "Delivered", "xong": "Done", "xem_don_khac": "View another order", "tu_choi_nhan_don_hang": "Reject this order?", "huy_don_hang": "Cancel order", "khong": "No", "co": "Yes", "khong_tim_thay_don_hang": "Order not found", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "hoan_thanh": "Completed", "tra_hang": "Returns", "tu_choi": "Rejected", "don_hang_khong_ton_tai": "Order does not exist", "co_loi_xay_ra": "An error occurred.\nPlease try again later", "ban_khong_phai_chu_cua_hang": "You are not the store owner.", "muon_nhan_hang_luc": "Want to receive the item at"}, "AgentEditOrderComponent": {"ma_don_hang": "Order code:", "them_san_pham": "Add product", "anh_san_pham": "Product image", "anh_dai_dien": "<PERSON><PERSON><PERSON><PERSON>", "dia_chi": "Address", "dia_chi_placeholder": "Address...", "vui_long_nhap_dia_chi": "Please enter the address", "tong": "Total", "san_pham": "product", "phi_van_chuyen": "Shipping fee", "giam_gia": "Discount", "tong_cong": "Total amount", "ghi_chu": "Note", "ghi_chu_don_hang": "Order notes...", "huy_thay_doi": "Cancel changes", "cap_nhat": "Update", "tim_san_pham": "Find product...", "da_chon": "Selected", "bo_chon": "Unselect", "xong": "Done", "don_hang_khong_ton_tai": "Order does not exist", "khong_tim_thay_don_hang": "Order not found"}, "ForgetPasswordComponent": {"quen_mat_khau": "Forgot password?", "khong_nho_duoc_mat_khau": "Can't remember your password?", "so_dien_thoai": "Phone number", "dia_chi_email": "Email address", "email_cua_ban": "Your email", "lay_ma": "Get code", "ma_xac_thuc_email": "Email verification code", "nhap_ma_xac_thuc": "Enter verification code", "lay_lai_ma_sau": "Get code again later", "so_dien_thoai_cua_ban": "Your phone number", "so_dien_thoai_placeholder": "0xxx xxx xxx", "phut": "m", "giay": "s", "ma_xac_thuc_dien_thoai_di_dong": "Mobile phone verification code", "ma_xac_thuc_so_dien_thoai": "Phone number verification code", "lay_ma_qua": "Get code via", "zalo": "<PERSON><PERSON>", "sms": "SMS", "mat_khau_moi": "New password", "mat_khau": "Password", "phai_chua_so_chu_cai_ky_tu_dac_biet": "Must contain numbers, letters, and special characters", "ky_tu_dac_biet": "Special characters", "phai_tu_6_den_20_ky_tu": "Must be between 6-20 characters", "xac_nhan_mat_khau_moi": "Confirm new password", "xac_nhan_mat_khau": "Confirm Password", "dat_lai_mat_khau": "Reset password", "vui_long_nhap_email": "Please enter email", "email_khong_dung_dinh_dang": "Invalid email format", "vui_long_nhap_sdt": "Please enter phone number", "sdt_khong_dung": "Invalid phone number", "sdt_chua_duoc_su_dung": "Phone number is not used for any account", "email_chua_duoc_su_dung": "Email is not used for any account", "vui_long_nhap_ma_xac_thuc": "Please enter verification code", "ma_xac_thuc_sai": "Incorrect or expired verification code", "chua_nhap_ma_xac_thuc": "Verification code not entered", "cap_nhat_mat_khau_thanh_cong": "Password updated successfully", "cap_nhat_mat_khau_that_bai": "Password update failed. Please try again", "vui_long_nhap_mat_khau_xac_nhan": "Please enter confirm password", "nhap_lai_mat_khau_khong_khop": "Confirm password does not match"}, "RegisterShopComponent": {"chi_phi_canh_tranh": "Competitive costs", "ve_don_that_nhanh": "Fast order fulfillment", "ten_cua_hang": "Shop name", "nhap_ten_cua_hang": "Enter shop name", "vui_long_nhap_ten_cua_hang": "Please enter shop name", "nganh_hang_chu_luc": "Main industry", "hang_muc": "Category", "vui_long_chon_loai_hinh_kinh_doanh": "Please select a business type", "chua_chon": "Not selected", "chua_co_hang_muc_duoc_luu": "No categories saved", "khong_tim_thay": "Not found", "email": "Email", "email_khong_dung_dinh_dang": "Invalid email format", "vui_long_nhap_sdt": "Please enter phone number", "sdt_khong_dung": "Phone number is incorrect", "nhap_dia_chi_email": "Enter email address", "so_dien_thoai": "Phone number", "nhap_so_dien_thoai": "Enter phone number", "ngon_ngu": "Language", "ngon_ngu_cua_cua_hang": "Shop language", "vui_long_chon_ngon_ngu": "Please select a language", "don_vi_tien_te": "<PERSON><PERSON><PERSON><PERSON>", "don_vi_tien_te_cua_san_pham": "Product currency", "ghi_chu": "Note", "thoi_gian_hoat_dong_mo_ta_duong_di": "Operating hours, directions, etc.", "dia_chi": "Address", "vui_long_nhap_dia_chi": "Please enter address", "vi_tri_cua_ban": "Your location", "vui_long_chon_vi_tri": "Please select a specific location", "nhan_de_chuyen_loai_map": "Click to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_nhan": "Satellite + labels", "tinh_thanh_pho": "Province/City", "tim_tinh_thanh_pho": "Search Province/City...", "chon_tinh_thanh_pho": "Select Province/City", "danh_sach_tinh_thanh_pho_trong": "No Province/City available", "quan_huyen": "District", "tim_quan_huyen": "Search District...", "chon_quan_huyen": "Select District", "danh_sach_quan_huyen_trong": "No District available", "phuong_xa": "Ward/Commune", "tim_phuong_xa": "Search Ward/Commune...", "chon_phuong_xa": "Select Ward/Commune", "danh_sach_phuong_xa_trong": "No Ward/Commune available", "logo_cua_hang": "Shop logo", "chua_chon_anh": "No image selected", "doi_anh": "Change image", "dieu_chinh": "Adjust", "xoa_anh": "Delete image", "lon_nho": "Resize", "tren_duoi": "Top and bottom", "trai_phai": "Left and right", "banner_cua_hang": "Shop banner", "hoan_thanh": "Complete", "tu_choi_quyen_truy_cap": "Deny access", "vui_long_dang_nhap": "Please log in", "truoc_khi_dang_ky_cua_hang": "before registering a shop", "chua_cung_cap_vi_tri": "You haven't provided a location! Using default location", "dang_ky_cua_hang_thanh_cong": "Shop registration successful", "dang_ky_cua_hang_that_bai": "Shop registration failed\n Please check your information", "cap_nhat_banner_that_bai": "Banner update failed\n Please try again later", "cap_nhat_logo_that_bai": "Logo update failed\n Please try again later", "bat_buoc": "Required", "tuy_chon": "Optional", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "gio_mo_cua": "Opening hours", "vui_long_chon_gio_mo_cua": "Please select opening hours", "anh_bia": "Banner", "anh_bia_kich_thuoc_de_xuat": "Recommended size 720x360px", "anh_dai_dien": "Avatar", "anh_dai_dien_kich_thuoc_de_xuat": "Recommended size 512x512px", "linh_vuc": "Field", "thiet_lap": "Settings", "gioi_thieu": "Introduction", "gioi_thieu_placeholder": "This introduction can boost your sales...", "cac_thong_tin_bat_buoc": "Fields marked with {char} are required", "mo_shop": "Open shop", "dang_ky_cua_hang_cua_ban": "Register your shop", "ban_da_co_cua_hang": "You already have a store", "tao_dai_ly": "Create agent"}, "OTPConfirmComponent": {"ma_otp_da_gui_den": "The OTP code has been sent to", "khong_nhan_duoc_ma": "Did not receive the code?", "gui_lai": "Resend", "gui_lai_sau": "Resend later", "phut": "m", "giay": "s", "dong": "Close", "xac_thuc": "Verify", "ma_xac_thuc_sai": "Incorrect or expired verification code"}, "AddressSearchingInputComponent": {"dia_chi": "Address", "ten_duong_toa_nha_so_nha": "Street name, building, house number", "khong_tim_thay_dia_chi_phu_hop": "No suitable address found"}, "Confirm18AgeComponent": {"san_pham_bia_ruou_chi_danh_cho_khach_hang_tren_18_tuoi": "Alcoholic products are only for customers over 18 years old.", "vui_long_xac_nhan_truoc_khi_tiep_tuc": "Please confirm before proceeding", "thoat": "Exit", "toi_da_du_18_tuoi": "I am over 18 years old"}, "PageNotFoundComponent": {"khong_tim_thay_trang": "Page not found !!!", "quay_lai_trang_chu": "Go back to Home"}, "AboutComponent": {"ve_chung_toi": "About Us", "dia_chi": "Address", "so_dien_thoai": "Phone Number", "email": "Email", "lien_ket_nhanh": "Quick Links", "gioi_thieu": "Introduction", "tin_tuc": "News", "lien_he": "Contact", "chinh_sach": "Policy", "ho_tro": "Faq"}, "MyOrdersComponent": {"tim_cua_hang": "Search for shops...", "tim_theo_ma_don_hang": "Search by order code", "tat_ca": "All", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "hoan_thanh": "Completed", "tra_hang": "Returns", "tu_choi": "Rejected", "moi": "New", "huy_bo": "Cancel", "xac_nhan": "Confirm", "da_giao": "Delivered", "tong_cong": "Total", "danh_sach_trong": "List is empty", "khong_tim_thay_don_hang_nao": "No orders found", "vui_long_dang_nhap_truoc_khi_xem_cac_don_hang_da_dat": "Please log in before viewing your orders", "lam_moi": "Refresh", "dang_tai": "Loading...", "chua_dang_ky_cua_hang": "Store is not registered", "ban_chua_co_cua_hang": "You do not have a store", "dang_ky_ngay": "Register now", "tu_choi_nhan_don_hang": "Reject order?", "huy_don_hang": "Cancel order", "khong": "No", "co": "Yes", "loc_don_hang": "Filter orders", "chua_cung_cap": "Not provided", "dong": "Close", "ap_dung": "Apply", "toan_thoi_gian": "All time", "hom_nay": "Today", "hom_qua": "Yesterday", "thang_nay": "This month", "thang_truoc": "Last month", "thoi_gian_khac": "Other time", "dang_nhap": "<PERSON><PERSON>"}, "MyOrderDetailComponent": {"dang_tai": "Loading...", "ma_don": "Order code", "chua_co_ma": "No code yet", "da_sao_chep_ma_don": "Order code copied", "sao_chep": "Copy", "trang_thai": "Status", "mo_rong": "Expand", "giao_hang_thanh_cong": "Delivery successful", "xac_nhan": "Confirm", "dat_hang": "Order", "nguoi_nhan": "Recipient Information", "zalo": "<PERSON><PERSON>", "goi_dien": "Call", "gia_lien_he": "Contact for price", "ghi_chu_don_hang": "Order notes", "hinh_thuc_thanh_toan": "Payment method", "thanh_toan_khi_nhan_hang": "Cash on delivery", "hinh_thuc_nhan_hang": "Delivery method", "tu_toi_lay": "Self-Pickup", "toi_can_giao_tan_noi": "Need a delivery", "tam_tinh": "Subtotal", "giam_gia": "Discount", "phi_van_chuyen": "Shipping fee", "thanh_tien": "Total", "chua_bao_gom_phi_van_chuyen": "Shipping fee is not included", "huy_don": "Cancel order", "tu_choi_don": "Reject order", "nhan_don": "Accept order", "da_giao": "Delivered", "xong": "Done", "xem_don_khac": "View another order", "tu_choi_nhan_don_hang": "Reject this order?", "huy_don_hang": "Cancel order", "khong": "No", "co": "Yes", "khong_tim_thay_don_hang": "Order not found", "cho_xac_nhan": "Waiting", "dang_xu_ly": "Processing", "hoan_thanh": "Completed", "tra_hang": "Returns", "tu_choi": "Rejected", "don_hang_khong_ton_tai": "Order does not exist", "co_loi_xay_ra": "An error occurred.\nPlease try again later", "ban_khong_phai_chu_cua_hang": "You are not the store owner.", "muon_nhan_hang_luc": "Want to receive the item at", "anh_don_hang": "Order image", "cua_hang_chua_cung_cap": "The store has not provided", "chua_tao_don_giao": "Delivery order not created", "khoang_cach": "Distance", "trang_thai_giao_hang_1": "Pending...", "trang_thai_giao_hang_2": "Confirmed", "trang_thai_giao_hang_3": "Order is in progress", "trang_thai_giao_hang_4": "Picked up", "trang_thai_giao_hang_5": "In delivery", "trang_thai_giao_hang_6": "Successfully delivered", "trang_thai_giao_hang_7": "Driver did not accept the order", "trang_thai_giao_hang_8": "Delivery failed", "vi_tri": "Location", "vi_tri_shipper": "<PERSON><PERSON>'s location", "thong_tin_giao_hang": "Delivery information", "chi_tiet_don_giao": "Delivery order details", "cod": "COD", "thoi_gian_lay_hang": "Pick-up time", "bay_gio": "Now", "phut": "m", "chua_chon": "Not selected", "thong_tin_goi_hang": "Package information", "yeu_cau_dac_biet": "Special requirements", "ghi_chu_cho_tai_xe": "Note for driver", "tai_xe": "Shipper", "giao_trong": "Deliver in", "trang_thai_don_giao_1": "Pending confirmation", "trang_thai_don_giao_2": "Confirmed", "trang_thai_don_giao_3": "Order is being processed", "trang_thai_don_giao_4": "Picked up", "trang_thai_don_giao_5": "Delivering", "trang_thai_don_giao_6": "Successfully delivered", "trang_thai_don_giao_7": "Delivery canceled", "trang_thai_don_giao_8": "Delivery failed", "chi_duong": "Directions", "nhan_tin": "Message", "giam_phi_van_chuyen": "Discount delivery fee", "xem_chi_tiet": "View details"}, "AddCommentComponent": {"binh_luan": "Comment", "noi_dung_danh_gia": "Review content", "anh_dinh_kem": "Attached image", "thoat": "Exit", "luu_danh_gia": "Save review", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "them_that_bai": "Review submission failed!\nPlease try again later", "them_thanh_cong": "Review successfully submitted", "rat_khong_hai_long": "Very dissatisfied", "khong_hai_long": "Dissatisfied", "tam_on": "Neutral", "tot": "Good", "tuyet_voi": "Excellent", "them_danh_gia": "Add review", "so_luong_toi_da": "The maximum number of images is {amount}", "cap_nhat_danh_gia": "Update review"}, "DateTimePickerComponent": {"hom_nay": "Today", "chon": "Select", "bay_gio": "Now"}, "FindShipperComponent": {"goi_dien": "Call", "chua_cap_sdt": "No phone number", "chon": "Select", "nam": "Male", "nu": "Female", "khac": "Other", "khong_tim_thay_shipper": "No delivery person found in the area", "dang_online": "Online", "da_offline": "Offline", "dang_ban": "Busy", "vua_moi_offline": "Just went offline", "online_x_phut_truoc": "Online {x} minutes ago", "online_x_gio_truoc": "Online {x} hours ago", "online_x_ngay_truoc": "Online {x} days ago", "tim_khu_vuc_nay": "Search this area", "da_thich": "Liked", "them_vao_yeu_thich": "Add to favorites", "yeu_thich": "Favorite", "ban_kinh": "<PERSON><PERSON>", "dong_danh_sach": "Close list", "mo_rong_danh_sach": "Expand list"}, "CreateDeliveryComponent": {"lo_trinh": "Route", "lay_hang_tai": "Pick up at...", "thong_tin_dia_diem_lay_hang": "Pick-up location information", "giao_den": "Deliver to...", "thong_tin_dia_diem_giao_hang": "Delivery location information", "loai_giao_hang": "Delivery type", "mo_ta_loai_giao_hang": "Express, Fast, Slow,...", "thoi_gian_lay_hang": "Pick-up time", "bay_gio": "Now", "thong_tin_goi_hang": "Package information", "mo_ta_thong_tin_goi_hang": "Weight, size, type of goods,...", "ghi_chu_cho_tai_xe": "Notes for the driver", "ghi_chu_cho_tai_xe_placeholder": "I want...", "yeu_cau_dac_biet": "Special requirements", "giao_tan_tay": "Hand delivery", "giao_hang_cong_kenh": "Bulky delivery", "mo_ta_giao_hang_cong_kenh": "Standard 50x40x60: 30kg | {price}", "giao_hang_de_vo": "Fragile delivery", "gia_di_kem": "{price}", "quay_lai_diem_lay_hang": "Return to pick-up point", "gui_sms_cho_nguoi_nhan": "Send SMS to recipient", "tui_giu_nhiet": "Thermal bag", "ho_tro_tai_xe": "Driver support", "tao": "Create", "luu": "Save", "lien_he_de_biet_gia": "Contact for price", "tong_phi": "Total fee", "sieu_toc": "Express", "mo_ta_sieu_toc": "Express delivery within 30 minutes", "nhanh": "Fast", "mo_ta_nhanh": "Fast delivery within 60 minutes", "2_gio": "2 hours", "mo_ta_2_gio": "Up to 2 hours", "4_gio": "4 hours", "mo_ta_4_gio": "Up to 4 hours", "khoi_luong": "Weight", "less_2_kg": "< 2kg", "from_2_to_5_kg": "2 - 5kg", "from_5_to_10_kg": "5 - 10kg", "more_10_kg": "> 10kg", "loai_hang_hoa": "Type of goods", "kich_thuoc": "Size", "kich_thuoc_placeholder": "length x width x height", "xong": "Done", "cod": "COD", "cod_placeholder": "COD...", "tai_xe": "Driver", "chon_shipper_khac": "Choose another driver", "chua_chon_shipper": "No driver selected", "chon_ngay": "Select now", "don_vi_van_chuyen": "Shipping unit", "phi_ship_khach_chon": "Shipping fee selected by customer", "luu_y": "Note", "luu_y_phi_ship": "The shipping fee has changed, please contact the customer to notify them again", "phi_ship": "Shipping fee", "thay_doi": "Change", "dang_kiem_tra": "Checking...", "chua_ket_noi_don_vi_van_chuyen": "Not yet connected to a delivery partner", "dong": "Close", "chua_chon": "Not selected", "nguoi_tra_phi": "Shipping fee payer", "nguoi_tra_cash": "Paid by store", "nguoi_tra_cash_by_recipient": "Paid by customer", "chua_xac_dinh_phi_ship": "Shipping fee not determined", "chua_xac_dinh": "Not determined", "khong_the_dat_don_giao_khi_phi_ship_chua_xac_dinh": "Cannot place delivery order when shipping fee is not determined", "khong_tim_thay_dich_vu_phu_hop": "No suitable service found", "dat": "Place", "lay_ngay": "Get now", "thoi_gian_cho_tai_xe_nhan_don": "Driver waiting time to accept order", "don_vi_thoi_gian": "Time unit", "mac_dinh_5_phut": "<PERSON><PERSON><PERSON> 5 minutes", "mac_dinh_30_phut": "De<PERSON>ult 30 minutes", "phi_ship_khach_duoc_giam": "Shipping fee reduced for customer due to meeting conditions", "khong_ho_tro": "Not supported", "khong_the_dat_don_giao_khi_khong_ho_tro": "Cannot place order when the delivery service is not supported", "chua_cap_sdt": "No phone number", "ket_noi_ngay": "Connect now"}, "SelectUserInfoComponent": {"ten": "Name", "ten_placeholder": "<PERSON><PERSON><PERSON>", "vui_long_nhap_ten_nguoi_nhan": "Please enter the recipient's name", "so_dien_thoai": "Phone number", "vui_long_nhap_sdt": "Please enter the phone number", "sdt_khong_dung": "Invalid phone number", "so_dien_thoai_placeholder": "0123456789", "dia_chi": "Address", "dia_chi_placeholder": "No. xy, A Street, B Ward, C District,...", "vui_long_nhap_dia_chi": "Please enter the address", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Tap to change map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_nhan": "Satellite + labels", "luu": "Save", "chon_tu_so_dia_chi": "Select from address book"}, "SupportComponent": {"ve_chung_toi": "About Us", "dia_chi": "Address", "so_dien_thoai": "Phone Number", "email": "Email", "cau_hoi_thuong_gap": "Frequently Asked Questions"}, "CreatePasswordComponent": {"dong": "Close", "tao": "Create", "tao_mat_khau_cho": "Create password for account", "mat_khau": "Password", "nhap_mat_khau": "Enter password", "phai_chua_so_chu_cai_ky_tu_dac_biet": "Must contain numbers, letters, and special characters", "ky_tu_dac_biet": "Special characters", "phai_tu_6_20_ky_tu": "Must be 6-20 characters long", "tao_mat_khau_that_bai": "Failed to create password.\nPlease try again later", "tao_mat_khau_thanh_cong": "Password created successfully"}, "SelectOpenTimeComponent": {"huy": "Cancel", "xong": "Done", "chinh_sua_tat_ca": "Edit all days", "chinh_sua_t2_t6": "Edit Monday to Friday", "chinh_sua_t7_cn": "Edit Saturday and Sunday", "chua_chon": "Not selected"}, "EditOpenTimeComponent": {"chon_ngay_va_gio": "Select date and time", "huy": "Cancel", "xong": "Done", "chon": "Select", "thoat": "Exit", "gio_mo_cua": "Opening time", "gio_dong_cua": "Closing time", "thoi_gian_placeholder": "HH:mm", "them_gio": "Add time", "chua_chon_gio_mo_cua": "Opening time not selected"}, "DriverDashboardComponent": {"danh_gia": "rating", "chua_co_sdt": "No phone number", "dang_nhap": "<PERSON><PERSON>", "dang_ky": "Register", "trang_ca_nhan": "Profile", "nhan_don": "Accept order", "giao_hang": "Deliver goods", "cho_nguoi": "Transport people", "quan_ly_don_hang": "Order management", "don_hang_hien_tai": "Current orders", "lich_su_giao_hang": "Delivery history", "online": "Ready to accept orders", "offline": "Not accepting orders", "nhan_de_tat": "Tap to turn off accepting orders", "nhan_de_bat": "Tap to turn on accepting orders", "thay_doi_trang_thai_that_bai": "Status change failed", "thay_doi_trang_thai_thanh_cong": "Status change successful", "dang_co_don": "Delivering", "danh_sach_don_giao": "Delivery list", "lich_su_van_don": "Delivery history", "don_dang_cho": "Pending order", "chua_co_danh_gia": "No reviews yet", "danh_sach_don_hang": "Order list"}, "DeliveryHistoryComponent": {"hien_thi_tren_tong_don_hang": "Displaying {amount} orders", "phi_ship": "Shipping fee", "cod": "COD", "trang_thai_don_hang_1": "Waiting...", "trang_thai_don_hang_2": "Confirmed", "trang_thai_don_hang_3": "Order being processed", "trang_thai_don_hang_4": "Picked up", "trang_thai_don_hang_5": "In delivery...", "trang_thai_don_hang_6": "Delivered", "trang_thai_don_hang_7": "Refused to accept", "trang_thai_don_hang_8": "Delivery failed", "nhan_don": "Accept order", "chi_tiet": "View details", "goi_dien": "Call", "dong": "Close", "ap_dung": "Apply", "toan_thoi_gian": "All time", "hom_nay": "Today", "hom_qua": "Yesterday", "thang_nay": "This month", "thang_truoc": "Last month", "thoi_gian_khac": "Other time", "loc_don_giao": "Filter deliveries", "chua_co_don_giao": "No delivery order yet", "dang_tai": "Loading...", "tat_ca": "All", "da_hoan_thanh": "Completed", "da_huy": "Cancelled / Rejected"}, "DeliveryDetailComponent": {"don_giao_khong_ton_tai": "Delivery does not exist", "xem_don_khac": "View other deliveries", "mo_rong": "Expand", "thu_gon": "Collapse", "giao_trong": "Deliver in", "phut": "minutes", "thoi_gian_lay_hang": "Pick-up time", "bay_gio": "Now", "ghi_chu_cho_tai_xe": "Note for driver", "cod": "Cash on delivery", "phi_ship": "Shipping fee", "thong_tin_goi_hang": "Package information", "yeu_cau_dac_biet": "Special request", "giao_tan_tay": "Deliver to hand", "giao_hang_cong_kenh": "Bulky delivery", "mo_ta_giao_hang_cong_kenh": "Standard 50x40x60:30kg | {price}", "giao_hang_de_vo": "Fragile delivery", "gia_di_kem": "{price}", "quay_lai_diem_lay_hang": "Return to pick-up point", "gui_sms_cho_nguoi_nhan": "Send SMS to recipient", "tui_giu_nhiet": "Thermal bag", "ho_tro_tai_xe": "Driver support", "less_2_kg": "< 2kg", "from_2_to_5_kg": "2 - 5kg", "from_5_to_10_kg": "5 - 10kg", "more_10_kg": "> 10kg", "loai_hang_hoa": "Type of goods", "kich_thuoc": "Dimensions", "nhan_don": "Accept order", "tu_choi": "Decline", "da_lay_hang": "Picked up", "hoan_thanh": "Complete", "giam_gia": "Discount", "thu_tu_nguoi_nhan": "Collect from recipient", "thu_tu_nguoi_gui": "Collect from store", "tra_cho_nguoi_gui": "Pay to store", "tra_boi_cash": "Paid by store", "tra_boi_cash_by_recipient": "Paid by recipient", "toi_diem_lay_hang": "Arrived at pick-up point", "di_giao": "Going to deliver", "cap_nhat_that_bai": "Update failed\nPlease try again", "goi_dien": "Call", "trang_thai_giao_hang_1": "Pending...", "trang_thai_giao_hang_2": "Confirmed", "trang_thai_giao_hang_3": "Order being processed", "trang_thai_giao_hang_4": "Picked up", "trang_thai_giao_hang_5": "In delivery", "trang_thai_giao_hang_6": "Successful", "trang_thai_giao_hang_7": "Declined", "trang_thai_giao_hang_8": "Failed", "duong_di": "Route", "thu_dau_shop": "Initial shop revenue", "thu_dau_khach": "Initial customer revenue", "thu_ve_thuc_te": "Actual revenue", "xem_chi_tiet": "View details"}, "LoginFirstComponent": {"ban_can_dang_nhap_truoc": "You need to log in first", "thoat": "Exit", "dang_nhap": "Log In"}, "MyShopCreateOrderComponent": {"them_san_pham": "Add product", "chua_co_ten": "No name", "chua_co_sdt": "No phone number", "chua_co_dia_chi": "No address provided", "thoi_gian_nhan_hang": "Delivery time", "bay_gio": "Now", "gia_lien_he": "Contact for price", "hinh_thuc_thanh_toan": "Payment method", "thanh_toan_khi_nhan_hang": "Pay on delivery", "phuong_thuc_nhan_hang": "Delivery", "giao_tan_noi": "Home delivery", "tu_lay_hang": "Self pick-up", "ghi_chu": "Note", "ghi_chu_placeholder": "Order note...", "tam_tinh": "Estimated total", "giam_gia": "Discount", "phi_van_chuyen": "Shipping fee", "doi_bao_gia": "Waiting for price quote", "thanh_tien": "Amount", "tong_tien": "Total amount", "san_pham": "Product", "thong_tin_nguoi_nhan": "Recipient information", "tim_san_pham": "Search product in store", "da_chon": "Selected", "bo_chon": "Deselect", "xong": "Done", "goi_bao_gia": "Call for quote", "luu": "Save", "xoa": "Delete", "tao_don_hang_that_bai": "Order creation failed\nPlease try again later", "tao_don_hang_thanh_cong": "Order created successfully", "vui_long_nhap_ten_nguoi_nhan": "Please enter recipient's name", "thong_tin_da_luu": "Information saved", "chua_luu_thong_tin": "No information saved", "chon_tu_so_dia_chi": "Choose from address book", "ten": "Name", "ten_placeholder": "Name...", "so_dien_thoai": "Phone number", "so_dien_thoai_placeholder": "0xxx xxx xxx", "dia_chi": "Address", "dia_chi_placeholder": "Address...", "huy": "Cancel", "xac_nhan": "Confirm", "vui_long_nhap_sdt": "Please enter phone number", "sdt_khong_dung": "Invalid phone number", "vui_long_nhap_dia_chi": "Please enter address", "chua_chon": "Not selected"}, "HourMinutePickerComponent": {"chon_thoi_gian": "Select time", "chon": "Select", "gio": "Hour", "phut": "Minute"}, "NewDeliveryNotiComnponent": {"nhan_don": "Accept order", "khong_nhan": "Reject order", "khoang_cach": "Distance", "thoi_gian_lay_hang": "Pickup time", "bay_gio": "Now", "da_tu_choi_nhan_don": "Order declined", "co_loi_xay_ra": "An error occurred.\nPlease try again later", "khong_tim_thay_don_hang": "Order not found"}, "ChangePasswordComponent": {"doi_mat_khau": "Change password", "ten_dang_nhap": "Username", "tai_khoan": "Account", "mat_khau_hien_tai": "Current password", "mat_khau_moi": "New password", "nhap_mat_khau": "Enter password", "phai_chua_so_chu_cai_ky_tu_dac_biet": "Must include numbers, letters, special characters", "ky_tu_dac_biet": "Special characters", "phai_tu_6_20_ky_tu": "Must be 6-20 characters", "xac_nhan_mat_khau": "Confirm password", "nhap_lai_mat_khau": "Re-enter password", "vui_long_nhap_mat_khau_xac_nhan": "Please enter confirmation password", "nhap_lai_mat_khau_khong_khop": "Passwords do not match", "luu": "Save", "doi_mat_khau_thanh_cong": "Password changed successfully", "doi_mat_khau_that_bai": "Password change failed"}, "ChangeEmailComponent": {"doi_email": "Change email", "dong": "Close", "luu": "Save", "email_hien_tai": "Current email", "email_hien_tai_placeholder": "Enter your current email here...", "ma_xac_thuc_email": "Email verification code", "nhap_ma_xac_thuc": "Enter verification code", "lay_ma": "Get code", "email_moi": "New email", "email_moi_placeholder": "Enter your new email here...", "email_khong_dung_dinh_dang": "Email format is incorrect", "email_da_duoc_su_dung": "Email has already been used", "vui_long_nhap_email_moi": "Please enter a new email", "vui_long_nhap_email_hien_tai": "Please enter your current email", "day_khong_phai_email_hien_tai": "This is not your current email", "lay_lai_sau": "Retry after", "phut": "minutes", "giay": "seconds", "doi_email_thanh_cong": "Email change successful", "doi_email_that_bai": "Email change failed", "ma_xac_thuc_da_gui_toi_email": "Verification code sent to {email}", "xac_thuc": "Verify", "ma_xac_thuc_sai": "Verification code is incorrect or expired", "vui_long_nhap_ma_xac_thuc": "Please enter the verification code"}, "HeaderV2Component": {"mo_shop": "Open shop", "mien_phi": "Free"}, "FooterV2Component": {"chien_dich": "Campaign", "gan_day": "Nearby", "trang_chu": "Home", "moi_ra_lo": "New", "toi": "Me", "gio_hang": "<PERSON><PERSON>", "chat": "Cha<PERSON>"}, "HomeV2Component": {"tim_kiem_placeholder": "Eating, shopping, and more...", "hang_tuyen_que_minh": "Top-tier", "tin_don_gan_day": "Recent rumors", "cua_hang_gan_ban": "Stores nearby", "xem_tat_ca": "View all", "xem_gan_day": "Recently viewed", "them_vao_gio": "Add to cart", "goi_y": "Suggestions:", "giam_gia": "Sale off", "san_pham_gan_ban": "Products nearby", "san_pham_moi_rao": "New products", "cua_hang_yeu_thich": "Favorite stores", "dang_tai": "Loading...", "gia_lien_he": "Contact for price"}, "VideoHLSPlayerComponent": {"tai_lai": "Reload"}, "ChatManageComponent": {"gio_truoc": "hours ago", "phut_truoc": "minutes ago", "giay_truoc": "seconds ago", "vua_xong": "Just now", "ngay_truoc": "days ago", "hom_qua": "Yesterday", "ban": "You", "hinh_anh": "Image", "san_pham": "Product", "don_hang": "Order", "lien_ket": "Link", "chua_co_cuoc_tro_chuyen": "No conversations yet", "co_loi_xay_ra": "An error occurred.\nPlease try again", "tai_lai": "Reload", "tai_khoan_khong_ton_tai": "Account does not exist", "da_hien_thi_het_tin_nhan": "All messages displayed", "tin_nhan_da_duoc_thu_hoi": "Message retracted", "ca_nhan": "Personal", "cua_hang": "Shop", "ban_dang_xem_tin_nhan_cua": "You are viewing messages from {shop_name}", "chon_cua_hang": "Choose another shop", "cua_hang_khong_ton_tai_hoac_ngung_hoat_dong": "Store does not exist or is no longer in operation"}, "ChatDetailComponent": {"dang_online": "Online", "online_x_truoc": "Online {time} ago", "gio_truoc": "hours ago", "phut_truoc": "minutes ago", "giay_truoc": "seconds ago", "vua_xong": "Just now", "ngay_truoc": "days ago", "hom_qua": "Yesterday", "x_thanh_vien": "{amount} members", "nhap_tin_nhan": "Enter a message...", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "bat_dau_cuoc_tro_chuyen": "Start a conversation!", "gui_loi": "Failed to send message", "dang_tai": "Loading...", "da_xem_het": "All viewed.", "dang_soan_tin": "is typing", "thuong_tra_loi_trong_vong_30_phut": "Usually replies within 30 minutes", "notice_1": "By sending a message, you acknowledge that Rẻ mà gần will process and review your chat information according to", "notice_2": "Rẻ M<PERSON>'s Privacy Policy.", "hoi_ve_san_pham_nay": "Ask about this product", "gui": "Send", "khong_co_avatar": "No product image", "gia_lien_he": "Price on request", "ban_dang_su_dung_ngon_ngu": "You are using the language", "thay_doi": "Change", "anh": "Image", "video": "Video", "don_hang": "Order", "san_pham": "Product", "da_chon_x_anh": "Selected {count} images", "xoa_het": "Delete all", "dang_tai_anh_len": "Uploading image...", "da_duoc_dich_sang": "translated to {lang_name}", "duoc_dich_tu": "translated from {lang_name}", "xem_ban_goc": "View original", "xem_ban_dich": "View translation", "cap_nhat_ban_dich": "Update translation", "dong": "Close", "chua_chon": "Not selected", "chon_san_pham": "Select product", "tim_san_pham": "Search for product...", "tim_don_hang": "Order ID...", "chon_don_hang_da_dat": "Select previous order", "tin_nhan_da_phuong_tien": "Multimedia message", "loi_khi_lay_danh_sach_don_hang": "Failed to retrieve order list", "tong_cong": "Total", "danh_sach_trong": "Empty list", "khong_tim_thay_don_hang_nao": "No orders found", "lam_moi": "Refresh", "cho_xac_nhan": "Waiting for confirmation", "dang_xu_ly": "Processing", "hoan_thanh": "Completed", "tra_hang": "Return", "tu_choi": "Decline", "moi": "New", "huy_bo": "Cancel", "xac_nhan": "Confirm", "da_giao": "Delivered", "hoi_ve_don_hang_nay": "Inquire about this order", "nguoi_nhan": "Recipient", "sao_chep": "Copy", "sao_chep_lien_ket": "Copy link", "thu_hoi": "Retract", "an_phia_toi": "Hide from me", "da_sao_chep_lien_ket": "Link copied", "thu_hoi_that_bai": "Retract failed", "an_that_bai": "<PERSON><PERSON> failed", "tin_nhan_da_duoc_thu_hoi": "Message retracted", "khong_the_thu_hoi_sau_24h": "Cannot retract messages after 24 hours", "khong_the_an_sau_24h": "Cannot hide messages after 24 hours", "khong_tim_thay_san_pham_nao": "No products found!"}, "ShopInfoComponent": {"chi_tiet_cua_hang": "Shop details", "nguoi_theo_doi": "Followers", "gio_mo_cua": "Opening hours", "ban_khong_co_quyen_truy_cap": "You do not have access", "danh_muc": "Category", "danh_gia": "Rating", "x_danh_gia": "{count} reviews", "san_pham": "Products", "da_tham_gia": "Joined", "hom_qua": "Yesterday", "ngay_truoc": "days ago", "thang_truoc": "months ago", "nam_truoc": "years ago", "so_dien_thoai": "Phone number", "dia_chi": "Address", "link_shop": "Shop link", "gioi_thieu": "Introduction", "xem_them": "See more", "thu_gon": "Collapse", "chua_co_gioi_thieu": "No introduction yet", "trang_thai": "Status", "dang_mo_cua": "Open", "da_dong_cua": "Closed", "danh_gia_cua_khach_hang": "Customer reviews", "them_danh_gia": "Add review", "phan_hoi_tu_cua_hang": "Response from the store", "hien_thi_them_danh_gia": "Show more reviews", "hien_thi_tat_ca": "All reviews displayed", "cap_nhat": "Update", "danh_gia_cua_ban": "Your review", "chua_co_danh_gia": "No reviews yet"}, "DirectionV2Component": {"vi_tri_cua_ban_den_cua_hang": "Your current location to the shop", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Tap to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_phan_hoi": "Satellite + Labels", "vi_tri_hien_tai_cua_ban": "Your current location to the shop", "khoang_cach": "Distance", "thoi_gian": "Time", "khong_tim_thay_tuyen_duong_phu_hop": "No suitable route found", "di_bo": "Walking", "xe_may": "Motorbike", "o_to": "Car", "thon_tin_mang_tinh_chat_tham_khao": "Information is for reference only", "xuat_phat": "Start", "di_toi_gg_map": "Go to Google Maps"}, "SharePopupComponent": {"chia_se_qua_zalo": "Share via Zalo", "chia_se_qua_facebook": "Share via Facebook", "sao_chep_link": "Copy Link", "chia_se_qua_ung_dung": "Share via app", "chia_se": "Share"}, "Product404Component": {"khong_tim_thay_san_pham": "Product not found or out of stock!!!"}, "ChangePermissionComponent": {"thay_doi_quyen": "Change permissions", "dong": "Close", "thao_tac_can_thuc_hien": "Actions to perform", "mo": "Open", "chrome": {"mo_setting": "Open Settings", "privacy_and_security": "Privacy and security", "site_settings": "Site settings", "chon_domain": "Select {domain}", "thay_doi_quyen": "Change the setting for the required permission"}, "firefox": {"mo_setting": "Open Settings", "privacy_and_security": "Privacy & Security", "keo_xuong_permission": "Scroll to Permissions section", "chon_setting_ben_canh_quyen": "Select Settings... next to the permission to change", "thay_doi_cho_domain": "Change the setting for {domain}"}, "safari": {"mo_setting": "Open Settings", "chuyen_qua_tab_website": "Switch to the Websites tab", "thay_doi_cho_domain": "Change the setting for {domain}"}, "edge": {"mo_setting": "Open Settings", "cookies_and_site_permissions": "Cookies and site permissions", "chon_domain": "Select {domain}", "thay_doi_quyen": "Change the setting for the required permission"}, "copy_link": "Copy link", "da_sao_chep": "<PERSON>pied"}, "ProductV2Component": {"tim_san_pham_khac": "Find other products...", "khong_co_avatar": "No avatar", "tong_quan": "Overview", "danh_gia": "Ratings", "review": "Review", "de_xuat": "Recommended", "ket_thuc_sau": "Ends in", "tiet_kiem_toi": "Save up to {percent}%", "gia_lien_he": "Contact for price", "da_ban": "Sold: {count}", "them_vao_yeu_thich": "Add to favorites", "da_thich": "Liked", "hang_tuyen": "Top-tier", "giai_thich_hang_tuyen": "Experienced by Rẻ m<PERSON>n", "mo_ta_san_pham": "Product description", "chi_tiet_san_pham": "Product details", "thu_gon": "Collapse", "mo_rong": "Expand", "danh_gia_cua_khach_hang": "Customer reviews", "them_danh_gia": "Add review", "phan_hoi_tu_cua_hang": "Store's response", "hien_thi_them_danh_gia": "Show more reviews", "hien_thi_tat_ca": "All reviews displayed", "video_review_san_pham": "Product video review", "nguoi_theo_doi": "Followers", "ghe_shop": "Visit shop", "co_the_ban_cung_thich": "You may also like", "luot_ban": "{count} sales", "luot_thich": "{count} likes", "di_den_cua_hang": "Go to store", "chat_ngay": "Chat now", "dich_thuat_thong_minh": "Smart translation", "them_vao_gio_hang": "Add to cart", "dang_nhap_truoc_khi_chat": "Please log in first", "chua_co_danh_gia": "No reviews yet", "chua_co_san_pham_de_xuat": "No recommended products yet", "chua_co_review": "No reviews yet", "danh_gia_cua_ban": "Your review", "cap_nhat": "Update"}, "RequireLoginComponent": {"ban_can_dang_nhap": "You need to log in first!", "dang_nhap": "Log in", "dong": "Close", "de_sau": "Remind me later"}, "LocalizedLanguageName": {"vi": "Vietnamese", "en": "English", "ru": "Russian", "ko": "Korean"}, "CustomSelectComponent": {"tim_kiem": "Search...", "danh_sach_trong": "Empty list", "chon": "Select", "bo_chon_tat_ca": "Deselect all", "ban_la_chu_shop": "You are the shop owner"}, "ClearSearchHistoryComponent": {"xoa_lich_su_tim_kiem": "Clear search history", "xoa_lich_su": "Clear history", "dong": "Close"}, "ManageDeliveryPartnerComponent": {"khong_quyen_quan_ly": "You do not have permission to manage this shop", "co_loi_xay_ra": "An error occurred!\nPlease try again later", "da_ket_noi": "Connected", "chua_ket_noi": "Not connected", "ngung_ket_noi": "Disconnect", "ket_noi": "Connect", "ket_noi_lai": "Reconnect", "xem_chi_tiet": "View details", "chua_ho_tro": "Not supported yet", "dat_mac_dinh": "Set as default", "mac_dinh": "<PERSON><PERSON><PERSON>", "cap_nhat_thanh_cong": "Update successful"}, "ShopDeliveryPartnerInfoComponent": {"ket_noi_voi": "Connect with", "ten_dia_diem_nhan_hang": "Pickup location name", "dia_chi": "Address", "so_dien_thoai": "Phone number", "email": "Email", "dong": "Close", "luu": "Save", "ket_noi": "Connect", "ket_noi_don_vi_van_chuyen": "Connect to shipping unit", "vui_long_nhap_ten_dia_diem_nhan_hang": "Please enter the pickup location name", "vui_long_nhap_dia_chi_nhan_hang": "Please enter the pickup address", "vui_long_nhap_so_dien_thoai": "Please enter the phone number", "vui_long_nhap_email": "Please enter the email address", "email_khong_dung_dinh_dang": "Invalid email format", "co_loi_xay_ra": "An error occurred!\nPlease try again later", "chinh_sua": "Edit", "so_dien_thoai_khong_dung": "Invalid phone number", "ket_noi_that_bai": "Connection failed.\nPlease try again later", "ban_khong_co_quyen": "You do not have permission to connect the store to the shipping provider"}, "DisconnectDeliveryPartnerComponent": {"ngung_ket_noi": "Disconnect", "thoi_de_sau": "Maybe later"}, "CancelDeliveryComponent": {"huy_don_giao": "Cancel delivery", "co_loi_xay_ra": "An error occurred!\nPlease try again later", "ly_do_huy_don": "Reason", "ly_do_huy_don_placeholder": "Reason for cancellation...", "dong": "Close", "xac_nhan_huy": "Confirm cancellation", "huy_that_bai": "Cancellation failed", "khong_the_huy_luc_nay": "Cannot cancel the delivery at this time", "don_vi_van_chuyen": "Delivery Partner"}, "UnauthorizedComponent": {"ban_khong_co_quyen_truy_cap": "You do not have access", "khong_tao_dai_ly": "You do not have permission to add an agent"}, "ShopConfigComponent": {"khong_co_quyen_quan_ly_shop": "You do not have permission to manage the shop", "chung": "General", "phi_giao_hang": "Delivery fee", "thoi_gian_hoan_thanh_don_toi_thieu": "Minimum order completion time", "phut": "minutes", "chua_cap_nhat": "Not updated", "cap_nhat": "Update", "thoi_gian_cho_nhan_don_giao_toi_da": "Maximum waiting time for order acceptance", "trang_thai": "Status", "dang_mo_cua": "Open", "da_dong_cua": "Closed", "don_hang": "Orders", "nhan_don_online": "Accept online orders", "cho_phep_nhan_don_online": "Allow accepting online orders", "khong_nhan_don_online": "Do not accept online orders", "minutes": "minutes", "ap_dung": "Apply", "khong_ap_dung": "Do not apply", "xoa": "Delete", "them_khuyen_mai": "Add promotion", "ban_chua_dang_ky_cua_hang": "You have not registered a shop", "co_loi_xay_ra": "An error occurred.\nPlease try again later", "chua_tao_khuyen_mai": "No promotion created", "luu": "Save", "hoan_tac": "Undo", "cap_nhat_thanh_cong": "Update successful", "dieu_kien_ap_dung": "Conditions applied: {count}", "khong_can_dieu_kien": "No conditions needed", "giam_toi_da": "Maximum discount", "thoi_gian_cho_tai_xe_nhan_don": "Driver waiting time to accept order", "loi_chao": "Greeting", "thong_bao_cho_khach_hang": "Notify the customer", "thong_bao_khong_tim_duoc_dich_vu_van_chuyen": "Notification when no delivery service is found", "lich_dong_cua_dip_dac_biet": "Special Closure Schedule", "lich_nghi": "Holiday schedule", "ngay_nghi": "Day Off", "ngay_nghi_dip_dac_biet": "Special Holiday", "ngay_nghi_hang_tuan": "Weekly Day Off", "ngay_nghi_hang_thang_am_lich": "Monthly Lunar Calendar Day Off", "ly_do": "Reason", "nhap_ly_do": "Enter reason...", "nhap_ly_do_vi": "Nghỉ lễ ...", "nhap_ly_do_en": "Holiday ...", "nhap_ly_do_ru": "Праздник ...", "nhap_ly_do_ko": "휴일 ...", "giao_dien": "Layout", "bang_mau": "Color palette", "phong_chu": "Font", "hoa_hong": "Commission", "phan_tram_hoa_hong": "Commission rate", "bat_tinh_nang_hoa_hong": "Enable commission feature", "nhap_phan_tram_hoa_hong": "Enter commission rate (0-100%)", "phan_tram_hoa_hong_tu_0_den_100": "Commission rate from 0% to 100%"}, "AddDeliveryPromotionComponent": {"them_khuyen_mai_phi_ship": "Add shipping fee promotion", "huy": "Cancel", "tao": "Create", "ten_khuyen_mai": "Promotion name", "vui_long_nhap_ten_khuyen_mai": "Please enter the promotion name", "gia_tri": "Value", "vui_long_nhap_gia_tri_khuyen_mai": "Please enter the promotion value", "gia_tri_phai_la_kieu_so": "The promotion value must be a number", "gia_tri_khuyen_mai_vuot_qua_gioi_han": "The promotion value exceeds the limit", "quan_he_khong_hop_le": "Invalid relationship", "mo_ta_khuyen_mai": "Promotion description", "vui_long_nhap_gia_tri_dieu_kien": "Please enter the condition value", "loai_dieu_kien_khong_hop_le": "Invalid condition type", "toan_tu_khong_hop_le": "Invalid operator", "dieu_kien": "Condition", "truong": "Field", "them_dieu_kien": "Add condition", "chua_co_dieu_kien": "No condition added", "gia_tri_don_hang": "Order value", "khoang_cach": "Distance", "tinh_thanh_pho": "City/Province", "quan_huyen": "District", "xa_phuong": "Ward/Commune", "toan_tu": "Operator", "gia_tri_dieu_kien": "Value", "quan_he_giua_cac_dieu_kien": "Relationship between conditions", "and": "And", "or": "Or", "trong": "(empty)", "kieu_du_lieu_khong_dung": "Incorrect data type", "gia_tri_toi_da_phai_la_kieu_so": "The maximum value must be a number", "khong_vuot_qua_gia_tri_khuyen_mai": "Does not exceed the promotion value", "gia_tri_toi_da": "Maximum value", "chon_tinh": "Select City/Province", "chua_chon": "Not selected"}, "UpdateDeliveryPromotionComponent": {"cap_nhat_khuyen_mai": "Update promotion", "huy": "Cancel", "luu": "Save", "ten_khuyen_mai": "Promotion name", "vui_long_nhap_ten_khuyen_mai": "Please enter the promotion name", "gia_tri": "Value", "vui_long_nhap_gia_tri_khuyen_mai": "Please enter the promotƒion value", "gia_tri_phai_la_kieu_so": "The promotion value must be a number", "gia_tri_khuyen_mai_vuot_qua_gioi_han": "The promotion value exceeds the limit", "quan_he_khong_hop_le": "Invalid relationship", "mo_ta_khuyen_mai": "Promotion description", "vui_long_nhap_gia_tri_dieu_kien": "Please enter the condition value", "loai_dieu_kien_khong_hop_le": "Invalid condition type", "toan_tu_khong_hop_le": "Invalid operator", "dieu_kien": "Condition", "truong": "Field", "them_dieu_kien": "Add condition", "chua_co_dieu_kien": "No conditions", "gia_tri_don_hang": "Order value", "khoang_cach": "Distance", "tinh_thanh_pho": "City/Province", "quan_huyen": "District", "xa_phuong": "Ward/Commune", "toan_tu": "Operator", "gia_tri_dieu_kien": "Value", "quan_he_giua_cac_dieu_kien": "Relationship between conditions", "and": "And", "or": "Or", "trong": "(empty)", "kieu_du_lieu_khong_dung": "Incorrect data type", "gia_tri_toi_da_phai_la_kieu_so": "The maximum value must be a number", "khong_vuot_qua_gia_tri_khuyen_mai": "Does not exceed the promotion value", "gia_tri_toi_da": "Maximum value", "chon_tinh": "Select City/Province", "chua_chon": "Not selected"}, "DeleteDeliveryPromotionComponent": {"xoa_khuyen_mai": "Delete promotion", "khong": "No", "co": "Yes"}, "SaveDataBeforeLeaveComponent": {"chua_luu_du_lieu": "Data not saved", "du_lieu_thay_doi_nhung_chua_duoc_luu": "Data has changed but not saved", "khong_luu": "Don't save", "luu_va_thoat": "Save and exit", "luu": "Save"}, "UpdatePendingTimeComponent": {"thoi_gian_cho_nhan_don_giao": "Waiting time to receive delivery order", "du_lieu_phai_la_kieu_so": "Data must be a number", "vui_long_nhap_du_lieu": "Please enter data", "don_vi_thoi_gian": "Time unit", "huy": "Cancel", "luu": "Save", "ngay_gio_phut_giay": "day / hour / minute / second", "thoi_gian_cho": "Waiting time..."}, "UpdateMessageComponent": {"tin_nhan": "Message", "tieng_viet_placeholder_loi_chao": "<PERSON><PERSON>o mừng đến với...", "tieng_anh_placeholder_loi_chao": "Welcome to...", "tieng_han_placeholder_loi_chao": "환영합니다...", "tieng_nga_placeholder_loi_chao": "Добро пожаловать в...", "tieng_viet_placeholder_thong_bao": "<PERSON><PERSON><PERSON><PERSON> báo cho khách hàng...", "tieng_anh_placeholder_thong_bao": "Notify the customer...", "tieng_han_placeholder_thong_bao": "고객에게 알림...", "tieng_nga_placeholder_thong_bao": "Уведомить клиента...", "tieng_viet_placeholder_thong_bao_don_giao_mac_dinh": "<PERSON><PERSON><PERSON><PERSON> tìm đ<PERSON><PERSON><PERSON> dịch vụ giao hàng phù hợp...", "tieng_anh_placeholder_thong_bao_don_giao_mac_dinh": "No suitable delivery service found...", "tieng_han_placeholder_thong_bao_don_giao_mac_dinh": "적합한 배송 서비스를 찾을 수 없습니다...", "tieng_nga_placeholder_thong_bao_don_giao_mac_dinh": "Не найдено подходящей службы доставки...", "toi_da_x_ky_tu": "Maximum {x} characters", "huy": "Cancel", "luu": "Save"}, "ShopWaitingConfirmComponent": {"thong_bao_cho_duyet": "You have successfully created a store, please wait for system approval", "dong": "Close"}, "EditReceiverInfoComponent": {"thong_tin_nguoi_nhan": "Receiver information", "chon_tu_so_dia_chi": "Select from address book", "thong_tin_da_luu": "Saved information", "chua_luu_thong_tin": "No information saved", "ten": "Name", "ten_placeholder": "<PERSON><PERSON><PERSON>", "vui_long_nhap_ten_nguoi_nhan": "Please enter the receiver's name", "so_dien_thoai": "Phone number", "vui_long_nhap_sdt": "Please enter the phone number", "sdt_khong_dung": "Invalid phone number", "so_dien_thoai_placeholder": "0123456789", "dia_chi": "Address", "vi_tri_dia_chi": "Location - address", "dia_chi_placeholder": "No. xy, A street, B ward, C district,…", "vui_long_nhap_dia_chi": "Please enter the address", "vi_tri_cua_ban": "Your location", "nhan_de_chuyen_loai_map": "Tap to switch map type", "ve_tinh": "Satellite", "co_dien": "Classic", "ve_tinh_nhan": "Satellite + labels", "luu_thong_tin": "Save information", "huy": "Cancel", "xac_nhan": "Confirm", "cap_nhat_dia_chi_khi_di_chuyen_ban_do": "Update address when moving the map", "nhap_toa_do": "Enter coordinates", "toa_do_x_y": "xxx.xxxx, yyy.yyyyy", "ghi_chu_cho_dia_chi": "Address notes", "ghi_chu_cho_dia_chi_placeholder": "Color gate..., opposite...", "anh": "Photo", "so_luong_toi_da": "Maximum number of photos is {amount}", "dung_luong_anh_toi_da": "Maximum photo size is {size}MB", "lay_vi_tri_hien_tai": "Get current location"}, "AddOrUpdateSavedAddressComponent": {"thong_tin": "Information", "ten": "Name", "so_dien_thoai": "Phone number", "dia_chi": "Address", "vui_long_nhap_ten": "Please enter the name", "vui_long_nhap_dia_chi": "Please enter the address", "vui_long_nhap_sdt": "Please enter the phone number", "sdt_khong_dung": "Invalid phone number", "ten_placeholder": "<PERSON><PERSON><PERSON>", "so_dien_thoai_placeholder": "0123456789", "dia_chi_placeholder": "No. xy, A street, B ward, C district...", "dong": "Close", "luu": "Save", "chua_cung_cap_vi_tri": "You have not provided a location! Using default location", "dat_mac_dinh_that_bai": "Setting default failed", "cap_nhat_that_bai": "Update failed", "luu_dia_chi_that_bai": "Saving address failed\nPlease check the information", "ten_dia_chi": "Address name", "ten_dia_chi_placeholder": "Address name (e.g., School, Gym,...)", "nha_rieng": "Home", "cong_ty": "Company", "khac": "Other", "vui_long_nhap_ten_dia_chi": "Please enter the address name", "cap_nhat_thanh_cong": "Update successful", "them_thanh_cong": "Add successful", "cap_nhat_dia_chi_khi_di_chuyen_ban_do": "Update address when moving the map", "nhap_toa_do": "Enter coordinates", "toa_do_x_y": "xxx.xxxx, yyy.yyyyy", "ghi_chu_cho_dia_chi": "Address notes", "ghi_chu_cho_dia_chi_placeholder": "Color gate..., opposite...", "anh": "Photo", "so_luong_toi_da": "Maximum number of photos is {amount}", "dung_luong_anh_toi_da": "Maximum photo size is {size}MB", "lay_vi_tri_hien_tai": "Get current location"}, "OrderReceiverInfoComponent": {"thong_tin_nguoi_nhan": "Receiver information", "ten": "Name", "so_dien_thoai": "Phone", "vi_tri_dia_chi": "Location - address", "anh": "Photo", "ghi_chu_cho_dia_chi": "Address notes"}, "NotificationsComponent": {"thong_bao": "Notification", "chua_co_thong_bao": "No notifications", "vua_xong": "Just now", "phut_truoc": "minutes ago", "gio_truoc": "hours ago", "hom_qua": "Yesterday", "ngay_truoc": "days ago", "danh_dau_da_doc": "<PERSON> as read", "danh_dau_doc_tat_ca": "Mark all as read", "dang_tai": "Loading...", "tai_them_thong_bao": "Load more notifications", "da_tai_het_thong_bao": "All notifications loaded"}, "QuotationComponent": {"danh_sach_bao_gia_nguyen_vat_lieu": "Material Quotation List", "trang_thai_1": "Requesting", "trang_thai_2": "Responded", "trang_thai_3": "Saved", "yeu_cau_bao_gia": "Request", "kiem_bao_gia": "Check", "loc_bao_gia": "Filter", "nhap_bao_gia": "Enter", "chua_tim_thay_bao_gia": "Quotation not found"}, "MonthYearPickerComponent": {"chon_thang_nam": "Select month/year", "chon": "Select", "thang": "Month", "nam": "Year", "thang_1": "January", "thang_2": "February", "thang_3": "March", "thang_4": "April", "thang_5": "May", "thang_6": "June", "thang_7": "July", "thang_8": "August", "thang_9": "September", "thang_10": "October", "thang_11": "November", "thang_12": "December", "tat_ca": "All"}, "ImportQuotationComponent": {"nhap_bao_gia_nguyen_vat_lieu": "Enter material quotation", "tieu_de": "Title", "ghi_chu": "Note", "tu": "From", "den": "To", "nhap_tu_excel": "Import from Excel", "chon_sheet": "Select sheet", "nha_cung_cap": "Supplier", "lay_danh_sach_nha_cung_cap_that_bai": "Failed to get supplier list", "them_nha_cung_cap": "Add supplier", "stt": "No.", "ten_tieng_anh": "Name (English)", "ten": "Name", "thuong_hieu": "Brand", "xuat_xu": "Origin", "dong_goi": "Packaging", "don_vi": "Unit", "gia": "Price", "gia_vat": "VAT Price", "mo_ta": "Description", "chon_cot_tuong_ung": "Select corresponding column", "chua_co_du_lieu": "No data available", "mo_rong": "Expand", "thu_gon": "Collapse", "luu": "Save", "tac_vu": "Action", "nguyen_lieu_tuong_ung": "Corresponding material", "nguyen_lieu_moi": "New material", "luu_thanh_cong": "Quotation saved successfully", "luu_that_bai": "Quotation save failed\nPlease try again later", "da_luu": "Saved", "chua_luu_du_lieu": "Data not saved. Are you sure you want to switch sheets?"}, "ImportFromFileComponent": {"nhap_tu_excel": "Import from Excel", "chon_file": "Select file...", "chon_file_khac": "Select another file...", "hang_chua_ten_cot": "Row containing column names", "hang_bat_dau_du_lieu": "Row starting data", "cot_bat_dau_du_lieu": "Column starting data", "cot_ket_thuc_du_lieu": "Column ending data", "doc_file": "Read file", "doc_that_bai": "Failed to read file\nPlease try again", "hang_placeholder": "1, 2, 3,...", "cot_placeholder": "A, B, C, AB, BC,..."}, "UpdateHolidaySettingComponent": {"lich_dong_cua": "Closure Schedule", "ngay_nghi_dip_dac_biet": "Special Holiday", "ngay_nghi_hang_tuan": "Weekly Day Off", "huy": "Cancel", "hoan_tat": "Complete", "chua_cap_nhat": "Not Updated", "them_ngay_nghi": "Add Day Off", "nhap_ly_do": "Enter reason...", "nhap_ly_do_vi": "Nghỉ lễ ...", "nhap_ly_do_en": "Holiday ...", "nhap_ly_do_ru": "Праздник ...", "nhap_ly_do_ko": "휴일 ...", "ly_do": "Reason", "chon_ngay": "Select Date"}, "AddSupplierComponent": {"them_nha_cung_cap": "Add supplier", "thoat": "Exit", "luu": "Save", "vui_long_nhap_ten_nha_cung_cap": "Please enter supplier name", "toi_da_x_ky_tu": "Maximum {x} characters", "email_khong_dung_dinh_dang": "Invalid email format", "vui_long_nhap_email": "Please enter email", "vui_long_nhap_so_dien_thoai": "Please enter phone number", "so_dien_thoai_khong_dung": "Invalid phone number", "vui_long_nhap_dia_chi": "Please enter address", "ten_nha_cung_cap": "Supplier name", "dia_chi": "Address", "dia_chi_nha_cung_cap": "Supplier address", "so_dien_thoai": "Phone number", "email": "Email", "them_nha_cung_cap_thanh_cong": "Supplier added successfully", "them_nha_cung_cap_that_bai": "Failed to add supplier\nPlease try again later"}, "RequestQuotationComponent": {"tao_yeu_cau_bao_gia": "Create quotation request", "tieu_de": "Title", "ghi_chu": "Note", "tu": "From", "den": "To", "nha_cung_cap": "Supplier", "them_nha_cung_cap": "Add supplier", "thu_gon": "Collapse", "mo_rong": "Expand", "chon_nguyen_lieu": "Select material", "nguyen_lieu": "Material", "ghi_chu_cho_nguyen_lieu": "Note for material", "tao": "Create", "stt": "No.", "ten_tieng_anh": "Name (English)", "ten": "Name", "thuong_hieu": "Brand", "xuat_xu": "Origin", "dong_goi": "Packaging", "don_vi": "Unit", "gia": "Price", "gia_vat": "VAT Price", "mo_ta": "Description", "chua_co_du_lieu": "No data available", "tao_yeu_cau_bao_gia_thanh_cong": "Quotation request created successfully", "tao_yeu_cau_bao_gia_that_bai": "Failed to create quotation request\nPlease try again later"}, "DetailQuotationComponent": {"bao_gia_khong_ton_tai": "Quotation does not exist", "co_loi_xay_ra": "An error occurred!\nPlease try again later", "thong_tin_bao_gia": "Quotation information", "nha_cung_cap": "Supplier", "ghi_chu": "Note", "tu": "From date", "den": "To date", "cua_hang_yeu_cau": "Requesting store", "trang_thai": "Status", "trang_thai_1": "Requesting", "trang_thai_2": "Responded", "trang_thai_3": "Saved", "tao_yeu_cau_luc": "Request created at", "cap_nhat_gan_nhat": "Last updated", "sao_chep": "Copy", "da_sao_chep": "<PERSON>pied", "duong_dan": "Link"}, "ShareLinkToSupplierComponent": {"duong_dan_phan_hoi": "Feedback link", "da_sao_chep": "<PERSON>pied", "sao_chep": "Copy"}, "ReplyQuotationComponent": {"bao_gia_khong_ton_tai": "Quotation does not exist", "co_loi_xay_ra": "An error occurred!\nPlease try again later", "phan_hoi_bao_gia": "Quotation response", "nha_cung_cap": "Supplier", "ghi_chu": "Note", "tu": "From date", "den": "To date", "cua_hang_yeu_cau": "Requesting store", "trang_thai": "Status", "trang_thai_1": "Requested", "trang_thai_2": "Responded", "trang_thai_3": "Saved", "tao_yeu_cau_luc": "Request created at", "cap_nhat_gan_nhat": "Last updated", "sao_chep": "Copy", "da_sao_chep": "<PERSON>pied", "duong_dan": "Link", "gui_bao_gia_thanh_cong": "Quotation sent successfully", "gui_bao_gia_that_bai": "Failed to send quotation\nPlease try again later", "stt": "No.", "ten_tieng_anh": "Name (English)", "ten": "Name", "thuong_hieu": "Brand", "xuat_xu": "Origin", "dong_goi": "Packaging", "don_vi": "Unit", "gia": "Price", "gia_vat": "VAT Price", "mo_ta": "Description", "chua_co_du_lieu": "No data available", "toi_da_la_x_ty": "Maximum is {x} billion"}, "CheckQuotationComponent": {"kiem_tra_bao_gia": "Check quotation", "tu": "From date", "den": "To date", "xem": "View", "nguyen_lieu": "Ingredient", "chon_nguyen_lieu": "Select ingredient", "chua_co_du_lieu": "No data available", "mo_rong": "Expand", "thu_gon": "Collapse", "kiem_tra_that_bai": "Quotation check failed\nPlease try again later", "stt": "No.", "ten_tieng_anh": "Name (English)", "ten": "Name", "thuong_hieu": "Brand", "xuat_xu": "Origin", "dong_goi": "Packaging", "don_vi": "Unit", "gia": "Price", "gia_vat": "VAT Price", "mo_ta": "Description", "nha_cung_cap": "Supplier"}, "SideBarComponent": {"dang_nhap": "<PERSON><PERSON>", "dang_ky": "Register", "gio_hang": "<PERSON><PERSON>", "chat": "Cha<PERSON>", "thong_bao": "Notification", "trang_chu": "Home", "gan_day": "Recent", "trang_ca_nhan": "Profile", "dang_xuat": "Logout", "da_dang_xuat": "Logged out", "cua_hang_cua_toi": "My Store"}, "CartOverviewComponent": {"gio_hang": "<PERSON><PERSON>", "gio_hang_trong": "Cart is empty", "xem_gio_hang": "View cart", "dat_hang_ngay": "Order now", "gia_lien_he": "Contact for price", "tam_tinh": "Subtotal", "mat_hang": "{count} items"}, "ShopTemplateComponent": {"mau_hien_thi": "Display Template", "mac_dinh": "<PERSON><PERSON><PERSON>", "nha_hang": "Restaurant", "anh_bia": "Banner", "anh_nen": "Background Image"}, "UpdateViewTemplateComponent": {"chon_giao_dien": "Choose layout", "huy": "Cancel", "luu": "Save", "chinh_sua": "Edit", "khuyen_mai": "Promotion", "nguoi_theo_doi": "Followers", "dang_mo_cua": "Open", "da_dong_cua": "Closed", "chi_duong": "Directions", "theo_doi": "Follow", "da_theo_doi": "Following", "chia_se": "Share", "gio_mo_cua": "Opening hours", "thong_bao": "Notification", "ten_khuyen_mai": "Promotion name", "mo_ta_khuyen_mai": "Promotion description", "toi_da": "Maximum", "xem_chi_tiet": "View details", "san_pham_hot": "Hot / Featured Products", "giam_gia": "Sale off", "noi_bat": "Featured", "tim_san_pham": "Search product...", "che_do_hien_thi": "Display mode", "goi_dien_cho_shop": "Call shop", "chat_voi_shop": "Chat with shop", "bang_mau_goi_y": "Suggested color palette", "mac_dinh": "<PERSON><PERSON><PERSON>", "he_thong": "System", "thien_nhien": "Nature", "hien_dai": "Modern", "nang_dong": "Dynamic", "nhan_cho_mau_1": "Primary text color", "nhan_cho_mau_2": "Primary color", "nhan_cho_mau_3": "Secondary color", "nhan_cho_mau_4": "Primary background", "nhan_cho_mau_5": "Accent color", "phong_chu": "Font", "dung_luong_anh_toi_da": "Maximum image size {size}MB", "luu_anh_nen_that_bai": "Failed to save background image", "luu_anh_bia_that_bai": "Failed to save cover photo", "dung_anh_mac_dinh": "Use default image", "dung_banner_chinh": "Use main banner", "chon": "Select"}, "delivery_cancel_reason": {"user_incorrect_pickup": "User changes pick up", "user_incorrect_dropoff": "User changes drop off", "user_not_input_promotion": "User has yet input promotion", "user_incorrect_time_delivery": "User changes order time", "user_incorrect_recipient": "User changes recipient information", "user_no_driver_accept": "Driver no accept", "user_pickup_is_so_far_from_driver": "Driver is so far from pickup", "user_driver_asked_cancel": "Driver asked to cancel", "user_driver_takes_too_time_to_pickup": "Driver pick order too many time", "user_no_item": "User has no item", "user_user_change_driver": "User chages driver", "user_user_change_vehicle": "User changes vehicle", "user_user_use_another_app": "User used another app", "user_incorrect_item_description": "User changes intem description", "user_incorrect_special_requests": "User changes special request", "user_incorrect_payment_method": "User changes payment method", "user_package_too_large": "Package is too large", "user_driver_collect_extra_fee": "Driver suggests collect extra fee", "user_driver_behavior": "Driver has bad behavior", "user_incorrect_vehicle": "Vehicle is unsuitable", "user_user_wrong_address": "User has mistakenn"}, "NetworkError": {"title": "No internet connection", "message": "Please check your network connection and try again", "reload": "Reload", "close": "Close"}, "StockManagement": {"quan_ly_kho": "Stock Management", "nhap_kho": "Stock Import", "xuat_kho": "Stock Export", "hong_mat": "Waste/Loss", "lich_su": "History", "bao_cao": "Reports", "ton_kho_hien_tai": "Current Stock", "ghi_nhan_hang_nhap": "Record Stock Import", "ghi_nhan_ban_hang": "Record Sales", "ghi_nhan_hong_mat": "Record Waste/Loss", "xem_lich_su_kho": "View Stock History", "thong_ke_bao_cao": "Statistics & Reports", "xem_ton_kho": "View Current Stock", "so_luong": "Quantity", "don_vi": "Unit", "gia_nhap": "Purchase Price", "gia_ban": "Sale Price", "ly_do": "Reason", "ghi_chu": "Notes", "nha_cung_cap": "Supplier", "chon_san_pham": "Select Product", "nhap_so_luong": "Enter Quantity", "nhap_gia": "Enter Price", "chon_ly_do": "Select Reason", "them_ghi_chu": "Add Notes", "tai_len_hinh": "Upload Image", "luu_thong_tin": "Save Information", "huy_bo": "Cancel", "thanh_cong": "Success", "that_bai": "Failed", "khong_du_ton_kho": "Insufficient Stock", "cap_nhat_thanh_cong": "Updated Successfully", "loi_cap_nhat": "Update Error", "ton_kho_thap": "Low Stock", "het_hang": "Out of Stock", "bao_cao_nhap_kho": "Import Report", "bao_cao_xuat_kho": "Export Report", "bao_cao_hong_mat": "Waste/Loss Report", "tong_ket_kho": "Stock Summary", "tu_ngay": "From Date", "den_ngay": "To Date", "loc_theo_ngay": "Filter by Date", "tim_kiem": "Search", "tat_ca": "All", "nhap": "Import", "xuat": "Export", "hong": "Waste", "ngay_gio": "Date/Time", "loai_giao_dich": "Transaction Type", "nguoi_thuc_hien": "Performed By", "gia_tri": "Value", "tong_nhap": "Total Import", "tong_xuat": "Total Export", "tong_hong": "Total Waste", "gia_tri_kho": "Stock Value", "so_san_pham": "Number of Products", "canh_bao_ton_kho": "<PERSON>", "het_han": "Expired", "sap_het_han": "Expiring Soon", "ly_do_hong": "Waste Reason", "hong_do_van_chuyen": "Damaged in Transit", "het_han_su_dung": "Expired", "hong_do_bao_quan": "Damaged in Storage", "ly_do_khac": "Other Reason", "ma_don_hang": "Order ID", "khong_co_du_lieu": "No Data", "dang_tai": "Loading", "tai_lai": "Reload", "xac_nhan": "Confirm", "ban_co_chac_chan": "Are you sure?", "hanh_dong_khong_the_hoan_tac": "This action cannot be undone", "chup_hinh": "Take Photo", "chon_tu_thu_vien": "Choose from Gallery", "xoa_hinh": "Remove Image", "hinh_anh_minh_chung": "Evidence Image", "bien_lai_nhap_hang": "Purchase Receipt", "hinh_anh_hong_mat": "Waste/Loss Image", "ghi_nhan_hang_xuat": "Record Stock Export", "chon_don_hang": "Select Order", "thong_tin_khach_hang": "Customer Information", "ten_khach_hang": "Customer Name", "tim_don_hang": "Search Order", "chua_co_du_lieu": "No Data Available", "tai_them": "Load More", "xem_ton_kho_san_pham": "View Product Stock", "nhap_gia_ban": "Enter Sale Price", "tim_kiem_san_pham": "Search Product", "tong_san_pham": "Total Products", "sap_het_hang": "Low Stock", "khong_co_san_pham": "No Products", "khong_tim_thay_san_pham": "No Products Found", "lich_su_ton_kho": "Stock History", "chua_co_lich_su": "No History Available", "chua_co_giao_dich": "No Transactions Available", "thay_doi": "Change", "ton_kho_sau_giao_dich": "Stock After Transaction", "hang_huy": "Waste/Disposal", "ghi_nhan_hang_huy": "Record Waste/Disposal", "ton_kho": "Stock", "so_luong_huy": "Quantity to Dispose", "ly_do_huy": "Disposal Reason", "hong_hoc": "Damaged", "bi_hong": "Spoiled", "bi_nhiem": "Contaminated", "chat_luong_kem": "Poor Quality", "khach_tra_lai": "Customer Return", "ghi_chu_chi_tiet": "Detailed Notes", "mo_ta_chi_tiet": "Detailed Description", "bao_cao_ton_kho": "Stock Report", "xem_bao_cao_thong_ke": "View Reports & Statistics", "chon_thoi_gian": "Select Time Period", "tong_nhap_kho": "Total Stock In", "tong_xuat_kho": "Total Stock Out", "tong_hang_huy": "Total Waste", "loi_nhuan_uoc_tinh": "Estimated Profit", "san_pham_ban_chay": "Best Selling Products", "da_ban": "Sold", "phan_tich_hang_huy": "Waste Analysis", "xuat_bao_cao": "Export Report", "gia_lien_he": "Contact Price", "tai_len_hinh_thanh_cong": "Image uploaded successfully", "tai_len_hinh_that_bai": "Image upload failed"}}