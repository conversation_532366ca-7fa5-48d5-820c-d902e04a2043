.batch-import-container {
    padding: 20px;
    width: 100%;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .batch-import-container {
        max-width: 800px;
    }
}

.header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    gap: 15px;
}

.back-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.back-button:hover {
    background-color: #f5f5f5;
}

.title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.step-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.step-header {
    margin-bottom: 25px;
}

.step-header h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: #333;
}

.results-count {
    color: #666;
    margin: 0;
}

.form-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

.form-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.text-input-container {
    position: relative;
    width: 100%;
}

.text-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 200px;
    box-sizing: border-box;
}

.text-input:focus {
    outline: none;
    border-color: #007bff;
}

.text-input:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.speech-button {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    z-index: 1;
}

.speech-button:hover:not(:disabled) {
    background-color: #218838;
}

.speech-button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.speech-button.recording {
    background-color: #dc3545;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.parse-button, .submit-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.parse-button:hover:not(:disabled), .submit-button:hover:not(:disabled) {
    background-color: #0056b3;
}

.parse-button:disabled, .submit-button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.back-step-button {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.back-step-button:hover:not(:disabled) {
    background-color: #545b62;
}

.back-step-button:disabled {
    background-color: #adb5bd;
    cursor: not-allowed;
}

.products-review {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
}

.product-item {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    background-color: #f8f9fa;
    width: 100%;
    box-sizing: border-box;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.product-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    background-color: white;
    padding: 10px;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    flex: 1;
}

.edit-button {
    background-color: #17a2b8;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    margin-left: 10px;
    transition: background-color 0.2s;
}

.edit-button:hover {
    background-color: #138496;
}

.product-summary {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 15px;
    align-items: center;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    margin-bottom: 15px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.summary-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.summary-value {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

/* Compact Product Summary Styles */
.product-summary-compact {
    background-color: #f8f9fa;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    border-left: 4px solid #007bff;
}

.product-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.product-name {
    font-weight: 600;
    color: #212529;
    font-size: 16px;
    line-height: 1.3;
}

.product-details-compact {
    display: flex;
    gap: 12px;
    align-items: center;
}

.unit-badge {
    background-color: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.price-badge {
    background-color: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.product-details {
    display: none;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.product-details.expanded {
    display: grid;
}

.detail-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-row-inline {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.detail-row label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.detail-input {
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.detail-input:focus {
    outline: none;
    border-color: #007bff;
}

.detail-input:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

/* Quantity Input Styles */
.quantity-input-container {
    display: flex;
    align-items: center;
    border: 1px solid #ced4da;
    border-radius: 6px;
    overflow: hidden;
}

.quantity-btn {
    background-color: #f8f9fa;
    border: none;
    padding: 10px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    border-right: 1px solid #ced4da;
}

.quantity-btn:last-child {
    border-right: none;
    border-left: 1px solid #ced4da;
}

.quantity-btn:hover:not(:disabled) {
    background-color: #e9ecef;
}

.quantity-btn:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

.quantity-input {
    border: none;
    padding: 10px;
    text-align: center;
    font-size: 14px;
    width: 60px;
    min-width: 60px;
}

.quantity-input:focus {
    outline: none;
}

/* Unit Input Styles */
.unit-input-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.unit-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    box-sizing: border-box;
}

.unit-select:focus {
    outline: none;
    border-color: #007bff;
}

.custom-unit-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ffc107;
    border-radius: 4px;
    font-size: 13px;
    background-color: #fff3cd;
    box-sizing: border-box;
}

.custom-unit-input:focus {
    outline: none;
    border-color: #ff8c00;
}

/* Price Input Styles */
.price-input-container {
    display: flex;
    align-items: center;
    border: 1px solid #ced4da;
    border-radius: 6px;
    overflow: hidden;
    background-color: white;
}

.price-input {
    border: none;
    padding: 10px;
    font-size: 16px;
    font-weight: 600;
    flex: 1;
    text-align: right;
    font-family: 'Courier New', monospace;
}

.price-input:focus {
    outline: none;
}

.currency-label {
    background-color: #f8f9fa;
    padding: 10px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    border-left: 1px solid #ced4da;
}

.price-preview {
    margin-top: 5px;
    font-size: 18px;
    font-weight: 700;
    color: #28a745;
    text-align: right;
    font-family: 'Courier New', monospace;
}

/* Suggested Products Grid Styles */
.suggested-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.suggested-product-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: white;
    text-align: center;
}

.suggested-product-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.suggested-product-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.product-image-container {
    width: 60px;
    height: 60px;
    margin: 0 auto 8px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.create-new-icon {
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.create-new-card .create-new-icon {
    color: #28a745;
}

.create-new-card.selected .create-new-icon {
    color: #007bff;
}

.product-card-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.product-card-name {
    font-size: 12px;
    font-weight: 600;
    color: #212529;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-card-price {
    font-size: 11px;
    font-weight: 500;
    color: #28a745;
    font-family: 'Courier New', monospace;
}

.no-suggestions-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-style: italic;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    justify-content: center;
}

/* Desktop Grid Layout Styles */
.products-grid-container {
    width: 100%;
    overflow-x: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.grid-header {
    display: grid;
    grid-template-columns: 120px 1fr 120px 150px 200px 180px;
    gap: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.grid-col-header {
    font-size: 14px;
    text-align: center;
    padding: 8px;
}

.products-grid {
    max-height: 70vh;
    overflow-y: auto;
}

.product-grid-row {
    display: grid;
    grid-template-columns: 120px 1fr 120px 150px 200px 180px;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
    transition: background-color 0.2s;
}

.product-grid-row:hover {
    background-color: #f8f9fa;
}

.grid-cell {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 60px;
    justify-content: center;
}

/* Image Cell Styles */
.image-cell {
    align-items: center;
}

/* Original Text Cell Styles */
.original-text-cell {
    padding: 8px;
}

.original-text {
    font-size: 12px;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    max-height: 60px;
    overflow-y: auto;
    line-height: 1.4;
    word-wrap: break-word;
}

/* Name Cell Styles */
.name-input-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
}

.original-name {
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
    padding: 2px 4px;
    background-color: #f8f9fa;
    border-radius: 3px;
    border: 1px solid #e9ecef;
    max-height: 40px;
    overflow-y: auto;
    line-height: 1.3;
    word-wrap: break-word;
}

.product-image-upload {
    position: relative;
    width: 80px;
    height: 80px;
    border: 2px dashed #ced4da;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    overflow: hidden;
}

.uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.image-placeholder {
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-actions {
    position: absolute;
    bottom: 4px;
    right: 4px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
}

.product-image-upload:hover .image-actions {
    opacity: 1;
}

.image-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.image-btn:hover {
    background-color: rgba(0, 0, 0, 0.9);
}

/* Grid Input Styles */
.grid-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
}

.grid-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.grid-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    box-sizing: border-box;
}

.grid-select:focus {
    outline: none;
    border-color: #007bff;
}

/* Price Input Group */
.price-input-group {
    display: flex;
    align-items: center;
    border: 1px solid #ced4da;
    border-radius: 6px;
    overflow: hidden;
}

.price-input-group .grid-input {
    border: none;
    border-radius: 0;
    flex: 1;
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.price-input-group .grid-input:focus {
    box-shadow: none;
}

.currency-suffix {
    background-color: #f8f9fa;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    border-left: 1px solid #ced4da;
}

/* Inventory Toggle Styles */
.inventory-toggle-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #007bff;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.inventory-inputs {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.quantity-input-container {
    display: flex;
    align-items: center;
    border: 1px solid #ced4da;
    border-radius: 6px;
    overflow: hidden;
    width: 100%;
}

.quantity-btn {
    background-color: #f8f9fa;
    border: none;
    padding: 6px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    border-right: 1px solid #ced4da;
}

.quantity-btn:last-child {
    border-right: none;
    border-left: 1px solid #ced4da;
}

.quantity-btn:hover:not(:disabled) {
    background-color: #e9ecef;
}

.quantity-btn:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

.quantity-input {
    border: none;
    padding: 6px 8px;
    text-align: center;
    font-size: 14px;
    width: 50px;
    min-width: 50px;
    flex: 1;
}

.quantity-input:focus {
    outline: none;
}

.import-price-container {
    display: flex;
    align-items: center;
    border: 1px solid #ced4da;
    border-radius: 6px;
    overflow: hidden;
}

.import-price-input {
    border: none;
    border-radius: 0;
    flex: 1;
    text-align: right;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.import-price-input:focus {
    box-shadow: none;
}

/* Suggested Products Compact */
.suggested-products-compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.suggested-dropdown {
    width: 100%;
}

.suggested-preview {
    display: flex;
    justify-content: center;
}

.suggested-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.no-suggestions-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    color: #6c757d;
    font-size: 12px;
    text-align: center;
}

.suggested-products {
    margin-top: 15px;
}

.suggested-products label {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.suggested-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    box-sizing: border-box;
}

.suggested-select:focus {
    outline: none;
    border-color: #007bff;
}

.suggested-select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.no-suggestions {
    margin-top: 15px;
    padding: 10px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    color: #856404;
    font-size: 14px;
}

@media (max-width: 768px) {
    .batch-import-container {
        padding: 15px;
    }
    
    .step-container {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .parse-button, .submit-button, .back-step-button {
        width: 100%;
        justify-content: center;
    }
    
    .product-summary {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .summary-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .product-details-compact {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }

    .suggested-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }

    .product-image-container {
        width: 50px;
        height: 50px;
    }

    .product-card-name {
        font-size: 11px;
    }

    .product-card-price {
        font-size: 10px;
    }

    .price-input {
        font-size: 14px;
    }

    .price-preview {
        font-size: 16px;
    }

    /* Mobile Grid Adjustments */
    .products-grid-container {
        overflow-x: scroll;
    }

    .grid-header,
    .product-grid-row {
        grid-template-columns: 80px 120px 80px 100px 140px 120px;
        gap: 8px;
        padding: 12px 8px;
    }

    .grid-col-header {
        font-size: 12px;
        padding: 4px;
    }

    .product-image-upload {
        width: 60px;
        height: 60px;
    }

    .grid-input,
    .grid-select {
        padding: 6px 8px;
        font-size: 12px;
    }

    .quantity-input {
        width: 40px;
        min-width: 40px;
    }

    .suggested-image {
        width: 30px;
        height: 30px;
    }
}
