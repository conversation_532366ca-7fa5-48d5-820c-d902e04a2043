.edit-shop-info-shop-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: white;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  max-width: var(--max-width-content-view-1024) !important;

  & > .edit-shop-info-shop-header {
    // width: 100%;
    // // font-size: 1.6em;
    // margin: 0;
    // text-align: center;
    // border-bottom: thin solid #ccc;
    color: white;
    white-space: nowrap;
    text-transform: uppercase;

    // & .back-button{
    //   background-color: inherit;
    // }
  }

  & .edit-shop-info-shop-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    overflow: hidden;
    font-size: 1em;

    & > .edit-shop-info-shop-content-container {
      --padding-content: 10px;
      --width-banner: calc(100dvw - var(--padding-content) * 2);
      width: 100%;
      display: flex;
      flex: 1;
      flex-direction: column;
      padding: var(--padding-content);
      border-radius: 7px;
      overflow: auto;
      background: #f4f4f4;
      font-size: 15px;
      & .shop-content {
        margin-top: 10px;
        font-size: 15px;
        background: white;
        padding: var(--padding-content);
        border-radius: 7px;

        & > .content-label {
          display: flex;
          gap: 5px;
          justify-content: center;
          text-align: center;
          line-height: 1;
          margin: 10px 0;
          & > .label-advanced {
            font-weight: 400;
            font-style: italic;
            color: #a8a7a7;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        & > .shop-info-content {
          display: flex;
          flex-direction: column;
          padding: 10px;
          border-bottom: thin solid #ececec;

          & > .input-content {
            display: flex;
            gap: 10px;

            & > .custom-select {
              color: #a8a7a7;
              font-weight: 400;
              font-size: 15px;
              animation: none !important;
              flex: 1;
              display: flex;
              justify-content: flex-end;
              align-items: center;
            
              & svg {
                font-size: 25px;
              }
            }
            & > .custom-select.selected{
              font-weight: 700;
              color: #2e2d30;
            }
          }

          & > .input-content.bio {
            display: flex;
          }

          &:last-child {
            border-bottom: none;
          }

          & .my-custom-select {
            height: 100%;
            align-items: center;
            padding: 3px 0;
            min-height: 0;
            opacity: 1;
            justify-content: flex-end;
            font-size: 15px;
            display: flex;
            flex: 1;
          }
        }
        & .label-input {
          color: #545454;
          font-weight: bold;
          white-space: nowrap;
          vertical-align: middle;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 30px;
          user-select: none;
        }
        & .label-input.required::after {
          content: attr(data-required);
          font-style: italic;
          color: var(--primary-color-2);
          font-size: 15px;
          font-weight: 500;
          margin-left: 5px;
        }
        & .label-input.optional::after {
          content: attr(data-optional);
          font-style: italic;
          color: #545454;
          font-size: 15px;
          font-weight: 500;
          margin-left: 5px;
        }
        & .input-shop {
          background-color: white;
          height: 30px;
          // padding: 10px 10px 10px 30px;
          border-radius: 5px;
          outline: none;
          color: #2e2d30;
          font-weight: 600;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 100%;
          text-align: right;

          &::placeholder {
            font-size: 15px;
            font-weight: 400;
            color: #a8a7a7;
          }
        }
        & .open-hours.input-shop {
          // height: fit-content;
          min-height: fit-content;
          // display: flex;
          // align-items: center;
          // justify-content: flex-start;
          // animation: none;
          // text-align: left;

          & svg {
            margin-left: auto;
          }

          & span.placeholder {
            font-size: 15px;
            font-weight: 400;
            color: #a8a7a7;
          }
        }
        & .counter {
          text-align: right;
          font-size: 13px;
          font-style: italic;
          font-weight: 400;
          color: #a8a7a7;
        }
        & .text-area-shop.input-shop {
          min-height: 100px;
          height: unset;

          & > textarea {
            width: 100%;
            outline: none;

            &::placeholder {
              font-size: 13px;
              font-weight: 400;
              font-style: italic;
            }
          }
        }

        & .error-message {
          font-size: 13px;
          text-align: right;
        }

        & > .business-type-select,
        > .place-select {
          background-color: white;
          height: 45px;
          padding: 10px 10px 10px 30px !important;
          border: none;
          border-radius: 5px;
          outline: none;
          color: #2e2d30;
          font-weight: 600;
          position: relative;

          & button {
            padding: 0 !important;
            display: flex;
            align-items: center;
            width: 100%;
            cursor: pointer !important;
            animation: none;

            & svg {
              font-size: 25px;
              margin-left: auto;
            }
          }
        }

        & > .submit-button {
          width: 100%;
          padding: 10px 0;
          display: flex;
          justify-content: center;
          align-items: center;
          color: white;
          gap: 5px;
          background-color: var(--primary-color-1);
          border-radius: 5px;
          text-transform: uppercase;
          font-weight: 800;
          box-shadow: 0 4px 4px 0 rgb(0, 0, 0, 15%);
        }
      }
      & .shop-content.p-0 {
        padding: 0;
      }

      & .notice{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        color: #a8a7a7;
        gap: 3px;
        line-height: 1.1;
        margin: 10px 0;

        & svg{
          font-size: 16px;
        }

        & em{
          color: var(--primary-color-2);
        }
      }
      & .language-select {
        background-color: white;
        // height: 45px;
        // padding: 10px 10px 10px 30px;
        border-radius: 5px;
        outline: none;
        color: #2e2d30;
        font-weight: 600;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;

        & .v-field {
          --v-input-control-height: 100%;
        }

        & .v-input__control {
          min-height: 30px;
          height: fit-content;
        }

        & .v-input__details {
          display: none;
        }

        & .chip-language {
          background: #a8a7a7;
          color: white;
          padding: 2px 2px 2px 10px;
          border-radius: 2em;
          font-size: 15px;
          height: fit-content;

          & .v-chip__content svg {
            font-size: 25px;
            margin-left: 5px;
          }
        }

        & .v-field__append-inner {
          padding: 0;
          align-items: center;
          font-size: 25px;
        }

        & .v-field__clearable {
          padding: 0;
          align-items: center;
          height: 30px;
        }

        & .v-field__input {
          height: 100%;
          align-items: center;
          padding: 3px 0;
          min-height: 0;
          opacity: 1;
          justify-content: flex-end;
          font-size: 15px;

          & > .v-select__selection {
            margin-inline-end: 0;
          }
        }
        & .v-field__input input {
          align-self: center;
          // flex: 0;

          &:focus {
            flex: 1;
          }
        }
        & .v-field__input input::placeholder {
          // color: #2e2d30;
          font-size: 15px;
          font-weight: 400;
          text-align: right;
        }
      }
      & .map-container {
        width: 100%;
        flex: 1;
        min-height: 250px;
        height: 250px;
        border-radius: 10px;

        & .leaflet-container {
          // border-radius: 10px;
          min-height: inherit;

          & .marker-location {
            width: 30px;
            height: 40px;
            position: absolute;
            bottom: 50%;
            left: 50%;
            object-fit: contain;
            transform: translateX(-50%);
            z-index: 450;

            & > img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
      }
      @media screen and (max-width: 500px) {
        & .shop-banner-container {
          --width-banner: calc(100dvw - var(--padding-content) * 4) !important;
        }
      }
      @media screen and (min-width: 501px) and (max-width: 1024px) {
        & .shop-banner-container {
          --width-banner: calc(100dvw - var(--padding-content) * 4) !important;
        }
      }
      @media screen and (min-width: 1025px) {
        & .shop-banner-container {
          --width-banner: calc(1024px - var(--padding-content) * 4) !important;
        }
      }
      & .shop-banner-container {
        --banner-aspect-ratio: 2;

        // aspect-ratio: var(--banner-aspect-ratio);
        height: calc((var(--width-banner)) / var(--banner-aspect-ratio));

        position: relative;
        display: flex;
        justify-content: center;
        width: var(--width-banner);
        align-items: center;
        box-shadow: 0 0 5px rgb(0, 0, 0, 15%);
        border-radius: 10px;
        overflow: hidden;
        margin: auto auto 7px;
        max-width: 100%;

        & > img.selected-banner {
          object-fit: cover;
          background-color: var(--color-background-2);
          width: 100%;
          height: 100%;
        }

        & > img:not([class*="selected"]) {
          object-fit: cover;
          width: 100%;
          height: 100%;
          // height: 250px;
          background-color: var(--color-background-2);
        }

        & > div.select-image {
          position: absolute;
          top: 10px;
          right: 10px;
          cursor: pointer;

          & > div {
            position: relative;
            width: 100%;
            font-size: 1.2em;
            background: black;
            color: white;
            height: 100%;
            display: flex;
            border-radius: 10px;
            padding: 7px;
            cursor: pointer;
            opacity: 0.5;

            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: 2;
            }

            & input::file-selector-button {
              cursor: pointer;
            }
          }
        }
      }

      & .shop-logo-container {
        position: relative;
        display: flex;
        justify-content: center;
        width: 100%;
        align-items: center;
        box-shadow: 0 0 5px rgb(0, 0, 0, 15%);
        width: 200px;
        height: 200px;
        border-radius: 50%;
        overflow: hidden;
        margin: 15px auto 7px;

        & > img.selected-logo {
          object-fit: cover;
          background-color: var(--color-background-2);
        }

        & > img:not([class*="selected"]) {
          object-fit: cover;
          width: 200px;
          height: 200px;
          background-color: var(--color-background-2);
        }

        & > div.select-image {
          position: absolute;
          top: 10px;
          right: 10px;
          cursor: pointer;

          & > div {
            position: relative;
            width: 100%;
            font-size: 1.2em;
            background: black;
            color: white;
            height: 100%;
            display: flex;
            border-radius: 10px;
            padding: 7px;
            cursor: pointer;
            opacity: 0.5;

            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: 2;
            }

            & input::file-selector-button {
              cursor: pointer;
            }
          }
        }
      }

      & .image-actions {
        display: flex;
        gap: 10px;

        & > button {
          background-color: #f4f4f4;
          color: #000000;
          border-radius: 7px;
          padding: 10px 0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          flex: 1;
          font-weight: 400;
          font-size: 17px;

          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: 2;
          }

          & input::file-selector-button {
            cursor: pointer;
          }
        }

        & > button:not(:disabled):active,
        > button.active {
          background-color: var(--primary-color-1);
          color: white;
          font-weight: 700;
        }
      }

      & .edit-image {
        display: flex;
        flex-direction: column;
        & > .action-edit-image {
          display: flex;
          flex-direction: column;

          & > .edit-image-slider {
            width: 85%;
            margin-left: auto;

            & [class*="fill"] {
              display: none;
            }

            & .v-input__details {
              display: none;
            }
          }
        }
      }
    }

    // & .edit-shop-info-shop-tab-header {
    //   color: var(--primary-color-1);
    //   font-size: 1em;
    //   width: 100%;
    // }
    // & .tab-button.active {
    //   color: var(--primary-color-1);
    //   border-color: var(--primary-color-1);
    // }
    // & .tab-button {
    //   flex: 1;
    //   text-transform: none;
    //   font-weight: 600;
    //   font-size: 1em;
    //   color: var(--color-text-note);
    //   border-bottom: 2px solid transparent;
    // }
    // & .tab-content-container {
    //   width: 100%;
    //   flex: 1;
    //   display: flex;

    //   & div[class*="v-window"] {
    //     flex: 1;
    //     display: flex;
    //     overflow: hidden;
    //   }
    // }
    // & .tab-content {
    //   padding: 0 10px;
    //   // margin-bottom: 20px;
    //   width: 100%;
    //   overflow: auto;

    //   & .shop-banner-container {
    //     position: relative;
    //     display: flex;
    //     justify-content: center;
    //     width: 100%;
    //     align-items: center;

    //     & > img {
    //       margin-bottom: 10px;
    //       border-radius: 10px;
    //       flex: 1 1;
    //       height: 250px;
    //       width: 100%;
    //       object-fit: cover;
    //       background-color: var(--color-background-2);
    //     }

    //     & > div.select-image {
    //       position: absolute;
    //       top: 10px;
    //       right: 10px;
    //       cursor: pointer;

    //       & > div {
    //         position: relative;
    //         width: 100%;
    //         font-size: 1.2em;
    //         background: black;
    //         color: white;
    //         height: 100%;
    //         display: flex;
    //         border-radius: 10px;
    //         padding: 7px;
    //         cursor: pointer;
    //         opacity: .5;

    //         & input {
    //           opacity: 0;
    //           width: 100%;
    //           height: 100%;
    //           position: absolute;
    //           top: 0;
    //           left: 0;
    //           cursor: pointer;
    //           z-index: 2;
    //         }

    //         & input::file-selector-button {
    //           cursor: pointer;
    //         }
    //       }
    //     }
    //   }

    //   & .shop-content {
    //     margin-top: 10px;

    //     & > .checkbox-input-label {
    //       gap: 5px;
    //       display: flex;
    //       cursor: pointer;
    //       user-select: none;
    //       font-weight: 500;
    //       color: var(--primary-color-1);

    //       & span {
    //         color: var(--primary-color-1);
    //       }

    //       & em {
    //         color: var(--primary-color-2);
    //       }
    //     }

    //     & em {
    //       color: var(--primary-color-1);
    //     }
    //   }

    //   & .map-container{
    //       width: 100%;
    //       flex: 1;
    //       min-height: 250px;
    //       border-radius: 10px;

    //       & .leaflet-container{
    //           border-radius: 10px;
    //       }
    //   }
    // }
    // & .search-select-empty {
    //   color: var(--color-text-note);
    // }

    // & .edit-shop-info-shop-actions{
    //   width: 100%;
    //   justify-content: space-evenly;
    //   padding: 5px;
    // }
  }

  // & .edit-shop-info-shop-tab-header {
  //   color: var(--primary-color-1);
  //   font-size: 1em;
  //   width: 100%;
  // }
  // & .tab-button.active {
  //   color: var(--primary-color-1);
  //   border-color: var(--primary-color-1);
  // }
  // & .tab-button {
  //   flex: 1;
  //   text-transform: none;
  //   font-weight: 600;
  //   font-size: 1em;
  //   color: var(--color-text-note);
  //   border-bottom: 2px solid transparent;
  // }
  // & .tab-content-container {
  //   width: 100%;
  //   flex: 1;
  //   display: flex;

  //   & div[class*="v-window"] {
  //     flex: 1;
  //     display: flex;
  //     overflow: hidden;
  //   }
  // }
  // & .tab-content {
  //   padding: 0 10px;
  //   // margin-bottom: 20px;
  //   width: 100%;
  //   overflow: auto;

  //   & .shop-banner-container {
  //     position: relative;
  //     display: flex;
  //     justify-content: center;
  //     width: 100%;
  //     align-items: center;

  //     & > img {
  //       margin-bottom: 10px;
  //       border-radius: 10px;
  //       flex: 1 1;
  //       height: 250px;
  //       width: 100%;
  //       object-fit: cover;
  //       background-color: var(--color-background-2);
  //     }

  //     & > div.select-image {
  //       position: absolute;
  //       top: 10px;
  //       right: 10px;
  //       cursor: pointer;

  //       & > div {
  //         position: relative;
  //         width: 100%;
  //         font-size: 1.2em;
  //         background: black;
  //         color: white;
  //         height: 100%;
  //         display: flex;
  //         border-radius: 10px;
  //         padding: 7px;
  //         cursor: pointer;
  //         opacity: .5;

  //         & input {
  //           opacity: 0;
  //           width: 100%;
  //           height: 100%;
  //           position: absolute;
  //           top: 0;
  //           left: 0;
  //           cursor: pointer;
  //           z-index: 2;
  //         }

  //         & input::file-selector-button {
  //           cursor: pointer;
  //         }
  //       }
  //     }
  //   }

  //   & .shop-content {
  //     margin-top: 10px;

  //     & > .checkbox-input-label {
  //       gap: 5px;
  //       display: flex;
  //       cursor: pointer;
  //       user-select: none;
  //       font-weight: 500;
  //       color: var(--primary-color-1);

  //       & span {
  //         color: var(--primary-color-1);
  //       }

  //       & em {
  //         color: var(--primary-color-2);
  //       }
  //     }

  //     & em {
  //       color: var(--primary-color-1);
  //     }
  //   }

  //   & .map-container{
  //       width: 100%;
  //       flex: 1;
  //       min-height: 250px;
  //       border-radius: 10px;

  //       & .leaflet-container{
  //           border-radius: 10px;
  //       }
  //   }
  // }
  // & .search-select-empty {
  //   color: var(--color-text-note);
  // }

  // & .edit-shop-info-shop-actions{
  //   width: 100%;
  //   justify-content: space-evenly;
  //   padding: 5px;
  // }
}
