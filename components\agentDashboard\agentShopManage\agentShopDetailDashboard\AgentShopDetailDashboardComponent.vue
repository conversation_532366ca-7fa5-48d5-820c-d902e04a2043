<script setup lang="ts">
import { appConst, domainImage } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { AuthService } from '~/services/authService/authService';
import { OrderService } from '~/services/orderService/orderService';
import { ShopService } from '~/services/shopService/shopService';
import { UserService } from '~/services/userService/userService';
import banner from '~/assets/image/remagan-banner-19_1.png';
import shop_logo from "~/assets/image_08_05_2024/shop-logo.png";
import access_denied from "~/assets/image/access_denied.png"
import { VueFinalModal } from 'vue-final-modal';
// import CreatePrivateProductComponent from './createPrivateProduct/CreatePrivateProductComponent.vue';
import { AgentService } from '~/services/agentService/agentService';

import { toast } from 'vue3-toastify';
import AvatarComponent from '~/components/avatar/AvatarComponent.vue';
import moment from 'moment';
import { HttpStatusCode } from 'axios';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const nuxtApp = useNuxtApp();

let authService = new AuthService();
let userService = new UserService();
let shopService = new ShopService();
let orderService = new OrderService();
let agentService = new AgentService()

var shop_id = ref(((route.params && route.params.id) ? route.params.id : "") as any);
var shopData = ref();
var myShopData = useState<any>('my_shop', () => { });
var listShopManaged = useState<any>('list_shop_managed', () => { });
var isFocus = ref(false);
var countOrderByStatus = ref([] as any[]);
var profileData = ref();
var showAddProductsFromSystemModal = ref(false);
var refreshing = ref(true);

var showSelectShopModal = ref(false);

useSeoMeta({
	title: t('AppRouteTitle.AgentShopDetailDashboardComponent')
})
onUnmounted(async () => { });
onMounted(async () => {
	profileData.value = JSON.parse(localStorage.getItem(appConst.storageKey.userInfo) as string);
	getShopDetail();
	if (!myShopData.value?.id) {
		getMyShop();
	}
	if (!listShopManaged.value?.length) {
		getAgentShop();
	}
	document.title = shopData.value ? shopData.value.name : t('AppRouteTitle.AgentShopDetailDashboardComponent')
});
onBeforeMount(async () => {
	
})

function getAgentShop() {
	agentService.listShopManage('', 100, 0).then(res => {
		if (res.status == HttpStatusCode.Ok) {
			listShopManaged.value = JSON.parse(JSON.stringify(res.body.data));
			if (myShopData.value?.id) {
				listShopManaged.value = [
					myShopData.value,
					...JSON.parse(JSON.stringify(res.body.data))
				];
			}

		}
		else {
			listShopManaged.value = [];
		}
	})
}
function getMyShop() {
	shopService.myShop().then(async res => {
		if (res.status && res.status == HttpStatusCode.Ok && res?.body?.data) {
			refreshing.value = false;
			myShopData.value = res.body.data;
		}
		else {
			myShopData.value = null
		}

	})
}

function getShopDetail() {
	refreshing.value = true;
	agentService.agentShopDetail(shop_id.value).then(async res => {
		if (res.status == HttpStatusCode.Ok) {
			refreshing.value = false;
			shopData.value = res.body.data;
			shop_id.value = shopData.value?.id
			useSeoMeta({
				title: shopData.value.name
			})
			if (shopData.value && shopData.value.id)
				getMyShopCountOrderByStatus();
		}
		else if (res.status == HttpStatusCode.Unauthorized) {
			router.push({
				path: appRoute.LoginComponent,
				query: {
					redirect: JSON.stringify(route.fullPath)
				}
			})
		}
		else {
			refreshing.value = false;
			toast.error(res.body?.message ?? t('AgentShopDetailDashboardComponent.khong_quyen_quan_ly'))
			// setTimeout(() => {
			// 	router.back();	
			// }, 2000);

		}
	}).catch(e => {
		refreshing.value = false;
		toast.error(t('AgentShopDetailDashboardComponent.khong_quyen_quan_ly'))
		// setTimeout(() => {
		// 		router.back();	
		// 	}, 2000);
	})
}

function getMyShopOrder() {
	orderService.orderByShopId(shopData.value.id, 0, 20, null).then(res => {
		if (res.status && res.status == HttpStatusCode.Ok) {
			let count = res.status && res.status == HttpStatusCode.Ok ? res.body.data.count : 0;
			shopData.value = {
				...shopData.value,
				orderCount: count
			}
		}

	})
}

function getMyShopCountOrderByStatus() {
	orderService.countOrderByStatus(shopData.value.id).then(async res => {
		countOrderByStatus.value = res.body.data
	})
}
function getCountOrderByStatus(status: number) {
	if (status == appConst.order_status.taken.value) {
		orderService.orderByShopId(
			shopData.value.id,
			0,
			5,
			appConst.order_status.taken.value,
			null,
			moment().format('YYYY-MM-DD'),
			moment().format('YYYY-MM-DD')
		).then(res => {
			if (res.status == HttpStatusCode.Ok) {
				let count = res.body.data.count;
				if (count < 1000) return count;
				if (count >= 1000) return '999+';
				return count;
			}
			return 0;
		});

	}
	else {
		let index = countOrderByStatus.value.findIndex(function (e: any) {
			return e.status == status
		})

		if (index != -1) {
			if (countOrderByStatus.value[index].count < 1000) return countOrderByStatus.value[index].count;
			if (countOrderByStatus.value[index].count >= 1000) return '999+';
		}
		return 0;
	}
	return 0;
}

function setSelectedShop(shop_id$: any) {
	if (shop_id$ != myShopData.value?.id && shop_id$ != shop_id.value) {
		let indexSelected = listShopManaged.value.findIndex(function (e: any) {
			return e.id == shop_id$;
		});
		if (indexSelected != -1) {
			router.push({
				path: appRoute.AgentShopDetailDashboardComponent.replaceAll(':id', listShopManaged.value?.[indexSelected]?.slug ?? listShopManaged.value?.[indexSelected]?.id)
			})
		}
	}
	else if (shop_id$ == myShopData.value.id) {
		router.push(appRoute.MyShopComponent);
	}
}
</script>
<template>
	<div class="public-container">
		<div class="agent-shop-detail-dashboard-container" v-if="refreshing">
			<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left" />
				</button>
			</div>
			<h3>{{ shopData ? shopData.name : appRouteTitle.AgentShopDetailDashboardComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
			<SubHeaderV2Component
				:title="shopData ? shopData.name : $t('AppRouteTitle.AgentShopDetailDashboardComponent')">
			</SubHeaderV2Component>
			<div class='agent-shop-detail-dashboard-detail-container'>
				<div class="primary-shop-info">
					<div class="primary-info-content skeleton">
						<v-skeleton-loader class="shop-banner"></v-skeleton-loader>

						<div class="shop-info-buttons">
							<v-skeleton-loader class="button-skeleton profile-info-button"></v-skeleton-loader>
							<v-skeleton-loader class="button-skeleton others-shop-button"></v-skeleton-loader>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class='agent-shop-detail-dashboard-container'
			v-else-if="shopData && shopData !== null && shopData.agent_id == profileData.id">
			<!-- <div class='title-header'>
			<div class="header-left">
				<button class="back-button" v-on:click="() => {
					router.options.history.state.back ? router.back() : router.push(appRoute.HomeComponent)
				}">
					<Icon name="lucide:chevron-left" />
				</button>
			</div>
			<h3>{{ shopData ? shopData.name : appRouteTitle.AgentShopDetailDashboardComponent }}</h3>
			<div class="header-right"></div>
		</div> -->
			<SubHeaderV2Component
				:title="shopData ? shopData.name : $t('AppRouteTitle.AgentShopDetailDashboardComponent')">
				<template v-slot:header_right>
					<button :title="t('ChatManageComponent.chon_cua_hang')" v-if="listShopManaged?.length" v-on:click="() => {
						if (listShopManaged?.length > 0) showSelectShopModal = true
					}">
						<Icon name="mdi:shop-settings-outline"></Icon>
					</button>
				</template>
			</SubHeaderV2Component>
			<div class='agent-shop-detail-dashboard-detail-container'>
				<div class="primary-shop-info">
					<div class="primary-info-content">
						<!-- <div class="shop-banner">
						<img :src="shop_logo" :placeholder="shop_logo" alt="" v-if="!shopData.logo"/>
						<div class="logo-origin-container" :class="{'none-style' : !(shopData.logo?.style?.length)}" v-else>
							<img
							:src="shopData?.logo?.path?.length ? (domainImage + shopData?.logo?.path) : shop_logo"
							:placeholder="shop_logo"
							v-on:click="()=>{
								router.push(appRoute.DetailShopComponent + '/' + shopData.slug)
							}"
							:style="{
								transform: (shopData.logo && shopData.logo.style) ? shopData.logo.style : 'none'
							}"
							alt="shop logo" />
						</div>
					</div> -->
						<AvatarComponent class="shop-banner" :imgTitle="shopData?.name"
							:imgStyle="shopData?.logo?.style" :imgSrc="shopData?.logo
								? (domainImage + shopData?.logo.path)
								: shopData?.banner
									? (domainImage + shopData?.banner.path)
									: ''
								" :width="90" :height="90" />
						<span class="name">
							{{ shopData ? shopData.name : "NULL" }}
						</span>
						<div class="v-stack shop-detail">
							<nuxt-link :to="{ path: appRoute.DetailShopComponent + '/' + shopData.slug }" class="first">
								<span>
									{{ shopData.address }}
								</span>
							</nuxt-link>
							<nuxt-link class="last">
								<span>
									{{
										shopData?.phone?.length ? shopData.phone :
											$t('AgentShopDetailDashboardComponent.chua_cap_sdt')
									}}
								</span>
							</nuxt-link>
						</div>

						<span class="blocked-shop" v-if="!shopData.enable">{{ $t('MyShopComponent.cua_hang_bi_khoa')
							}}</span>
						<!-- <nuxt-link :to="{ path: appRoute.ShopProductsComponent }">
						<span class="shop-detail">
							{{
								shopData.products.length
							}} <em> sản phẩm </em>
						</span>
					</nuxt-link> -->
						<div class="shop-info-buttons">
							<button class="profile-info-button" v-on:click="() => {
								router.push(appRoute.ProfileComponent)
							}">
								<Icon name="ph:user-circle-light" size="30px"></Icon> {{
									$t('AgentShopDetailDashboardComponent.trang_ca_nhan') }}
							</button>
							<button class="others-shop-button" v-on:click="() => {
								router.push(appRoute.DetailShopComponent + '/' + shopData.slug)
							}">
								<Icon name="emojione:department-store"></Icon>
								<span>{{ $t('AgentShopDetailDashboardComponent.che_do_khach') }}</span>
								<!-- <Icon name="tabler:switch-vertical" class="right" /> -->
							</button>
						</div>
					</div>
				</div>

				<div class='v-stack other-detail shop-orders'>
					<nuxt-link :to="{
						path: appRoute.AgentOrderManageComponent.replaceAll(':shop_id', shopData.slug ?? shopData.id),
					}">
						<div class='other-detail-button'>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.don_hang_cua_shop') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</div>
					</nuxt-link>
					<div class='h-stack other-detail-options'>
						<button v-on:click="() => {
							router.push({
								path: appRoute.AgentOrderManageComponent.replaceAll(':shop_id', shopData.slug ?? shopData.id),
								hash: '#waiting',
								// state: {
								// 	shop_id: shopData.id,
								// 	tabIndex: 'waiting'
								// }
							})
						}">
							<div class="button-icon">
								<Icon name="material-symbols:add-task-rounded"></Icon>
								<em v-if="getCountOrderByStatus(appConst.order_status.waiting.value)">
									{{ getCountOrderByStatus(appConst.order_status.waiting.value) }}
								</em>
							</div>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.cho_xac_nhan') }}
							</span>
						</button>
						<button v-on:click="() => {
							router.push({
								path: appRoute.AgentOrderManageComponent.replaceAll(':shop_id', shopData.slug ?? shopData.id),
								// state: {
								// 	shop_id: shopData.id,
								// 	tabIndex: 'confirmed'
								// },
								hash: '#confirmed'
							})
						}">
							<div class="button-icon">
								<Icon name="material-symbols:box-outline"></Icon>
								<em v-if="getCountOrderByStatus(appConst.order_status.confirmed.value)">
									{{ getCountOrderByStatus(appConst.order_status.confirmed.value) }}
								</em>
							</div>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.dang_xu_ly') }}
							</span>
						</button>
						<!-- <button v-on:click="() => {
    router.push({
      path: appRoute.AgentOrderManageComponent,
      hash: '#confirmed',
      state: {
        shop_id: shopData.id,
        tabIndex: 'confirmed'
      }
    })
  }">
    <div class="button-icon">
      <Icon name="mdi:truck-fast-outline"></Icon>
      <em>
        {{ getCountOrderByStatus(appConst.order_status.confirmed.value) }}
      </em>
    </div>
    <span>
      {{ $t('AgentShopDetailDashboardComponent.dang_van_chuyen') }}
    </span>
  </button> -->
						<button v-on:click="() => {
							router.push({
								path: appRoute.AgentOrderManageComponent.replaceAll(':shop_id', shopData.slug ?? shopData.id),
								hash: '#taken',
								// state: {
								// 	shop_id: shopData.id,
								// 	tabIndex: 'taken'
								// }
							})
						}">
							<div class="button-icon">
								<Icon name="fluent:box-checkmark-24-regular"></Icon>
								<em v-if="getCountOrderByStatus(appConst.order_status.taken.value)">
									{{ getCountOrderByStatus(appConst.order_status.taken.value) }}
								</em>
							</div>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.da_giao_hom_nay') }}
							</span>
						</button>
					</div>

				</div>
				<div class='v-stack other-detail my-orders'>
					<nuxt-link>
						<div class='other-detail-button'>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.san_pham') }}
							</span>
							<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
						</div>
					</nuxt-link>
					<div class='v-stack other-detail-options'>
						<nuxt-link :to="{
							path: appRoute.AgentShopManagementComponent.replaceAll(':id', shopData.slug),
							state: {
								tabIndex: 'products'
							}
						}" class="other-option">
							<Icon name="fluent:box-28-regular" class='icon-left' size="25px"></Icon>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.quan_ly_san_pham') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
					</div>


				</div>
				<div class='v-stack other-detail my-orders'>
					<nuxt-link>
						<div class='other-detail-button'>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.cua_hang') }}
							</span>
							<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
						</div>
					</nuxt-link>
					<div class='v-stack other-detail-options'>
						<nuxt-link :to="{
							path: appRoute.AgentShopDetailInfoComponent.replaceAll(':id', (shopData.slug ? shopData.slug : shopData.id))
						}" class="other-option">
						<Icon name="material-symbols:info-outline" class='icon-left' size="25px"></Icon>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.thong_tin_shop') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>

						<nuxt-link :to="{
							path: appRoute.AgentEditShopInfoComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id)
						}" class="other-option">
						<Icon name="iconamoon:edit-light" class='icon-left' size="25px"></Icon>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.sua_thong_tin') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link :to="appRoute.AgentShopChatManageComponent.replaceAll(':shop_id', shopData.id)"
							class="other-option">
							<Icon name="line-md:chat-round-dots" class='icon-left' size="25px"></Icon>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.tin_nhan') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link
							:to="appRoute.AgentStockManagementComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id)"
							class="other-option">
							<Icon name="lsicon:inventory-outline" class='icon-left' size="25px"></Icon>
							<span>
								{{ $t('StockManagement.quan_ly_kho') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link
							:to="appRoute.AgentShopManageDeliveryPartnerComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id)"
							class="other-option">
							<Icon name="iconoir:delivery-truck" class='icon-left' size="25px"></Icon>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.don_vi_van_chuyen') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>
						<nuxt-link
							:to="appRoute.AgentShopConfigComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id)"
							class="other-option">
							<Icon name="line-md:cog-filled-loop" class='icon-left' size="25px"></Icon>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.cau_hinh') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</nuxt-link>

						<!-- <button class="other-option" v-on:click="(e) => {
    router.push({
      path: appRoute.AgentShopCategoriesComponent.replaceAll(':id', shopData.slug ? shopData.slug : shopData.id)
    })
  }">
    <span>
      {{ $t('AgentShopDetailDashboardComponent.danh_muc') }}
    </span>
    <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
  </button> -->

						<!-- <button class="other-option" v-on:click="(e) => {
    router.push({
      path: appRoute.AgentShopManagementComponent.replaceAll(':id', shopData.slug),
    })
  }">
    <span>
      {{ $t('AgentShopDetailDashboardComponent.quan_ly') }}
    </span>
    <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
  </button> -->

						<!-- <button class="other-option" v-on:click="() => {
    router.push(appRoute.AgentShowProductInAShopComponent.replaceAll(':id', (shopData.slug ? shopData.slug : shopData.id)))
  }">
    <span>
      {{ $t('AgentShopDetailDashboardComponent.san_pham') }}
    </span>
    <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
  </button> -->

						<button class="other-option" v-if="false">
							<span>
								{{ $t('AgentShopDetailDashboardComponent.doanh_thu') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</button>

						<button class="other-option" v-if="false">
							<span>
								{{ $t('AgentShopDetailDashboardComponent.voucer_quang_cao') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</button>

						<button class="other-option" v-if="false">
							<span>
								{{ $t('AgentShopDetailDashboardComponent.nguoi_dung_bi_chan') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</button>

						<button class="other-option" v-if="false">
							<span>
								{{ $t('AgentShopDetailDashboardComponent.cai_dat_thong_bao') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</button>

						<button class="other-option" v-if="false">
							<span>
								{{ $t('AgentShopDetailDashboardComponent.yeu_cau_xoa_cua_hang') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</button>
					</div>


				</div>


				<div class='v-stack other-detail' v-if="false">
					<nuxt-link>
						<div class='other-detail-button'>
							<span>
								{{ $t('AgentShopDetailDashboardComponent.tro_giup') }}
							</span>
							<!-- <Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="40px"></Icon> -->
						</div>
					</nuxt-link>
					<div class='v-stack other-detail-options'>
						<button class="other-option" v-if="false">
							<span>
								{{ $t('AgentShopDetailDashboardComponent.lien_he_cskh') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</button>
						<button class="other-option" v-on:click="() => {
							router.push(appRoute.AboutComponent)
						}">
						
							<span>
								{{ $t('AgentShopDetailDashboardComponent.thong_tin_chung') }}
							</span>
							<Icon name="material-symbols:chevron-right-rounded" class='icon-right' size="25px"></Icon>
						</button>
					</div>

				</div>
			</div>
		</div>
		<!-- <div class='register-shop-button-container' v-else>
			<SubHeaderV2Component
				:title="shopData ? shopData.name : $t('AppRouteTitle.AgentShopDetailDashboardComponent')">
				<template v-slot:header_right>
					<button :title="t('ChatManageComponent.chon_cua_hang')" v-if="listShopManaged?.length" v-on:click="() => {
						if (listShopManaged?.length > 0) showSelectShopModal = true
					}">
						<Icon name="mdi:shop-settings-outline"></Icon>
					</button>
				</template>
			</SubHeaderV2Component>
			<div class='register-shop-content'>
				<img loading="lazy" :src='access_denied' :placeholder="access_denied"
					:alt="$t('AgentShopDetailDashboardComponent.alt_image')" />
				<span>
					{{ $t('AgentShopDetailDashboardComponent.khong_quyen_quan_ly') }}
				</span>
			</div>
		</div> -->
		<NoneMyShopComponent v-else :show_header="true"
			:title="shopData ? shopData.name : $t('AppRouteTitle.AgentShopDetailDashboardComponent')" :mode="'agent'"
			:message="$t('AgentShopDetailDashboardComponent.khong_quyen_quan_ly')">
			<template v-slot:header_right>
				<button :title="t('ChatManageComponent.chon_cua_hang')" v-if="listShopManaged?.length" v-on:click="() => {
					if (listShopManaged?.length > 0) showSelectShopModal = true
				}">
					<Icon name="mdi:shop-settings-outline"></Icon>
				</button>
			</template>
		</NoneMyShopComponent>
		<CustomSelectComponent v-if="showSelectShopModal" :_key="'select_shop_chat_manage'"
			:list_item="listShopManaged ?? []" :field_value="'id'" :field_title="'name'" :multiple="false"
			:title="$t('ChatManageComponent.chon_cua_hang')" :class="'my-custom-select custom-shop-select'"
			:searchable="false" :model_value="shop_id" v-on:close="() => {
				showSelectShopModal = false
			}" v-on:model:update="(e) => {
				setSelectedShop(e)
			}">
			<template v-slot:placeholder>
				<div class="h-stack">
					<span>{{ $t('RegisterShopComponent.thiet_lap') }}</span>
					<Icon name="mdi:chevron-down"></Icon>
				</div>
			</template>
			<template v-slot:title_icon_left>
				<Icon name="solar:hamburger-menu-linear"></Icon>
			</template>
			<template v-slot:render_item_option="{ item }">
				<div class="custom-shop-select-option">
					<AvatarComponent class="select-shop-logo" :imgTitle="item.name" :imgStyle="item.logo?.style"
						:imgSrc="item.logo
							? (domainImage + item.logo.path)
							: item.banner
								? (domainImage + item.banner.path)
								: ''
							" :width="40" :height="40" />
					<span>
						{{ item.name }}
						<em v-if="item.user_id == profileData.id" class="owner">{{
							$t('CustomSelectComponent.ban_la_chu_shop') }}</em>
					</span>
				</div>

			</template>
		</CustomSelectComponent>
	</div>

</template>

<style lang="scss" src="./AgentShopDetailDashboardStyles.scss"></style>