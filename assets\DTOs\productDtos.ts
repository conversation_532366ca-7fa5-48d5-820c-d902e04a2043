export class ProductCreateDto {
  public name: string;
  public is_main: string;
  public latitude: any;
  public longitude: any;
  public notes: string;
  public shop_id: string;
  public price: any;
  public price_off: any;
  public enable: boolean;
  public is_feature: boolean;
  public stock: number | null;
  public commission_percent: number | null;
  public translation: any[];
  public category_ids: any[];
  public rating: any;
  public comment_count: number;
  public description: any;
  public unit: string;
  public price_root: any;

  constructor(data: any) {
    this.name = data.name;
    this.is_main = data.is_main;
    this.latitude = data.latitude;
    this.longitude = data.longitude;
    this.notes = data.notes;
    this.shop_id = data.shop_id;
    this.price = data.price;
    this.price_off = data.price_off;
    this.enable = data.enable;
    this.is_feature = data.is_feature;
    this.stock = data.stock;
    this.commission_percent = data.commission_percent;
    this.translation = data.translation;
    this.category_ids = data.category_ids;
    this.rating = data.rating;
    this.comment_count = data.comment_count;
    this.description = data.description;
    this.unit = data.unit;
    this.price_root = data.price_root;
  }
}
export class ProductUpdateDto {
  public id: string;
  public name: string;
  public is_main: boolean;
  public brand_id: any;
  public profile_picture: string;
  public image_delete: any[];
  public images: any[];
  public latitude: any;
  public longitude: any;
  public notes: string;
  public shop_id: string;
  public price: number;
  public price_off: number;
  public enable: boolean;
  public created_by: string;
  public type: any;
  public parent_id: string;
  public extra_id: string;
  public unaccent_name: string;
  public is_feature: boolean;
  public lowercase_name: string;
  public stock: number | null;
  public commission_percent: number | null;
  public sold_count: number;
  public children_products: any[];
  public parent_product: number;
  public translation: any[];
  public category_ids: any[];
  public rating: any;
  public comment_count: number;
  public comments: any[];
  public description: any;

  constructor(data: any) {
    this.id = data.id;
    this.name = data.name;
    this.is_main = data.is_main;
    this.brand_id = data.brand_id;
    this.profile_picture = data.profile_picture;
    this.image_delete = data.image_delete;
    this.images = data.images;
    this.latitude = data.latitude;
    this.longitude = data.longitude;
    this.notes = data.notes;
    this.shop_id = data.shop_id;
    this.price = data.price;
    this.price_off = data.price_off;
    this.enable = data.enable;
    this.created_by = data.created_by;
    this.type = data.type;
    this.parent_id = data.parent_id;
    this.extra_id = data.extra_id;
    this.unaccent_name = data.unaccent_name;
    this.is_feature = data.is_feature;
    this.lowercase_name = data.lowercase_name;
    this.stock = data.stock;
    this.commission_percent = data.commission_percent;
    this.sold_count = data.sold_count;
    this.children_products = data.children_products;
    this.parent_product = data.parent_product;
    this.translation = data.translation;
    this.category_ids = data.category_ids;
    this.rating = data.rating;
    this.comment_count = data.comment_count;
    this.comments = data.comments;
    this.description = data.description ?? "";
  }
}
