<template>
  <div class="order-picker-modal">
    <div class="modal-overlay" @click="$emit('close')"></div>
    <div class="modal-content">
      <!-- Header -->
      <div class="modal-header">
        <h3>{{ $t('StockManagement.chon_don_hang') }}</h3>
        <button @click="$emit('close')" class="close-btn">
          <Icon name="solar:close-circle-bold" size="24" />
        </button>
      </div>

      <!-- Search -->
      <div class="search-section">
        <div class="search-input-wrapper">
          <Icon name="solar:magnifer-bold" size="20" class="search-icon" />
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="$t('StockManagement.tim_don_hang')"
            class="search-input"
            @input="handleSearch"
          />
          <button v-if="searchQuery" @click="clearSearch" class="clear-search-btn">
            <Icon name="solar:close-circle-bold" size="20" />
          </button>
        </div>
      </div>

      <!-- Orders List -->
      <div class="orders-list">
        <div v-if="loading" class="loading-state">
          <Icon name="solar:refresh-bold" size="32" class="loading-icon" />
          <p>{{ $t('StockManagement.dang_tai') }}</p>
        </div>

        <div v-else-if="filteredOrders.length === 0" class="empty-state">
          <Icon name="solar:document-bold" size="48" />
          <h3>{{ $t('ManageOrdersComponent.khong_tim_thay_don_hang_nao') }}</h3>
          <p>{{ $t('StockManagement.chua_co_du_lieu') }}</p>
        </div>

        <div v-else class="orders-container">
          <div 
            v-for="order in filteredOrders" 
            :key="order.id"
            class="order-item"
            @click="selectOrder(order)"
          >
            <div class="order-info">
              <div class="order-header">
                <h4 class="order-id">#{{ order.id }}</h4>
                <span class="order-status" :class="getOrderStatusClass(order.status)">
                  {{ getOrderStatusText(order.status) }}
                </span>
              </div>
              
              <div class="order-details">
                <p class="customer-name">{{ order.customer_name }}</p>
                <p class="order-date">{{ formatDate(order.created_at) }}</p>
                <p class="order-total">{{ formatCurrency(order.total_amount) }}</p>
              </div>
            </div>
            
            <div class="order-actions">
              <Icon name="solar:arrow-right-bold" size="20" />
            </div>
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreOrders && !loading" class="load-more-section">
          <button @click="loadMoreOrders" class="load-more-btn">
            {{ $t('StockManagement.tai_them') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrderService } from '~/services/orderService/orderService'
import { HttpStatusCode } from "axios";

// Props
const props = defineProps<{
  shopId: string
}>()

// Emits
const emit = defineEmits<{
  close: []
  select: [order: any]
}>()

// Composables
const { t } = useI18n()

// Services
const orderService = new OrderService()

// Reactive data
const loading = ref(false)
const searchQuery = ref('')
const orders = ref([])
const currentPage = ref(0)
const hasMoreOrders = ref(true)
const pageSize = 20

// Computed properties
const filteredOrders = computed(() => {
  if (!searchQuery.value) return orders.value
  
  const query = searchQuery.value.toLowerCase()
  return orders.value.filter(order => 
    order.id.toString().includes(query) ||
    order.customer_name?.toLowerCase().includes(query)
  )
})

// Methods
const selectOrder = (order: any) => {
  emit('select', order)
}

const handleSearch = () => {
  // Debounce search if needed
  // For now, just trigger reactive update
}

const clearSearch = () => {
  searchQuery.value = ''
}

const getOrderStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'status-pending'
    case 2: return 'status-processing'
    case 3: return 'status-completed'
    case 4: return 'status-delivered'
    case 5: return 'status-cancelled'
    default: return 'status-default'
  }
}

const getOrderStatusText = (status: number) => {
  switch (status) {
    case 1: return t('ManageOrdersComponent.cho_xac_nhan')
    case 2: return t('ManageOrdersComponent.dang_xu_ly')
    case 3: return t('ManageOrdersComponent.hoan_thanh')
    case 4: return t('ManageOrdersComponent.da_giao')
    case 5: return t('ManageOrdersComponent.huy_bo')
    default: return t('ManageOrdersComponent.moi')
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount || 0)
}

const loadOrders = async (reset = false) => {
  if (loading.value) return

  loading.value = true
  try {
    const offset = reset ? 0 : currentPage.value * pageSize
    const response = await orderService.orderByShopId(props.shopId, offset, pageSize)
    
    if (response.status === HttpStatusCode.Ok) {
      const newOrders = response.data?.orders || []
      
      if (reset) {
        orders.value = newOrders
        currentPage.value = 0
      } else {
        orders.value = [...orders.value, ...newOrders]
      }
      
      hasMoreOrders.value = newOrders.length === pageSize
      currentPage.value++
    }
  } catch (error) {
    console.error('Error loading orders:', error)
  } finally {
    loading.value = false
  }
}

const loadMoreOrders = () => {
  loadOrders(false)
}

// Lifecycle
onMounted(async () => {
  await loadOrders(true)
})
</script>

<style scoped>
.order-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 2px solid #ecf0f1;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
  }

  .close-btn {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;

    &:hover {
      color: #e74c3c;
    }
  }
}

.search-section {
  padding: 20px;
  border-bottom: 2px solid #ecf0f1;
}

.search-input-wrapper {
  position: relative;

  .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
  }

  .search-input {
    width: 100%;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 12px 15px 12px 45px;
    font-size: 14px;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #3498db;
      background-color: #f8f9fa;
    }
  }

  .clear-search-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;

    &:hover {
      color: #e74c3c;
    }
  }
}

.orders-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;

  .loading-icon {
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 15px 0 8px 0;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.orders-container {
  .order-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3498db;
      background-color: #f8f9fa;
    }

    .order-info {
      flex: 1;

      .order-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .order-id {
          font-size: 14px;
          font-weight: 600;
          margin: 0;
          color: #2c3e50;
        }

        .order-status {
          font-size: 11px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 4px;

          &.status-pending {
            background: #fef9e7;
            color: #f39c12;
          }

          &.status-processing {
            background: #e3f2fd;
            color: #2196f3;
          }

          &.status-completed {
            background: #d5f4e6;
            color: #27ae60;
          }

          &.status-delivered {
            background: #d5f4e6;
            color: #27ae60;
          }

          &.status-cancelled {
            background: #fadbd8;
            color: #e74c3c;
          }

          &.status-default {
            background: #ecf0f1;
            color: #7f8c8d;
          }
        }
      }

      .order-details {
        p {
          font-size: 12px;
          margin: 2px 0;

          &.customer-name {
            color: #2c3e50;
            font-weight: 500;
          }

          &.order-date {
            color: #7f8c8d;
          }

          &.order-total {
            color: #27ae60;
            font-weight: 600;
          }
        }
      }
    }

    .order-actions {
      color: #bdc3c7;
    }
  }
}

.load-more-section {
  text-align: center;
  margin-top: 20px;

  .load-more-btn {
    background: #ecf0f1;
    color: #7f8c8d;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #d5dbdb;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .order-picker-modal {
    padding: 10px;
  }

  .modal-content {
    max-height: 90vh;
  }

  .modal-header,
  .search-section,
  .orders-list {
    padding: 15px;
  }

  .order-item {
    .order-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
</style>
