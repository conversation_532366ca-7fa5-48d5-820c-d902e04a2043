:root {
  --color-text-title: #ff3366;
  --color-text-note: #999999;
  --color-text-primary: #333366;
  --color-text-black: #272424;
  --color-text-error: #ed4337;
  --font-size: 16px;
  --color-button-normal: #ff3366;
  --color-button-special: #333366;
  --color-button-disabled: #ff9966;
  --color-button-error: #ed4337;
  --color-background-2: #e8e8e86d;
  --color-text-price: #e18a06;
  --max-width-view: 100%;
}

@keyframes press {
  0% {
    transform: scale(1);
    -webkit-transform: scale(1);
  }

  20% {
    transform: scale(0.95);
    -webkit-transform: scale(0.95);
  }

  to {
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}
@keyframes shakeCart {
  25% {
    transform: translateX(6px);
  }
  50% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(2px);
  }
  100% {
    transform: translateX(0);
  }
}
.shake {
  animation: shakeCart 0.4s ease-in-out forwards !important;
}
@media only screen and (max-device-width: var(--max-width-view)) {
  a {
    color: your-desired-color;
    text-decoration: none; /* Remove underline */
    /* Add any other styles as needed */
  }
  input {
    appearance: none;
  }

  .my-modal-container {
    & > .my-modal-content-container {
      min-height: 100dvh !important;
      max-height: 100dvh !important;
      height: 100dvh !important;
      width: 500px !important;
      max-width: 100% !important;
    }
  }

  .new-orders-ticket {
    // right: 30px !important;
    // bottom: 100px !important;
    z-index: 1000;
  }
}
@media only screen and (min-device-width: 1024px) {
  ::-webkit-scrollbar {
    border-radius: 5px;
    width: 0;
    height: 0;
  }
  body {
    ::-webkit-scrollbar-thumb {
      background: rgb(0 0 0 / 25%);
      border-radius: 5px;
      width: 3px;
    }
  
    ::-webkit-scrollbar {
      border-radius: 5px;
      width: 3px;
      height: 3px;
    }
  }
}

* {
  box-sizing: border-box;
}
svg {
  height: unset;
  width: unset;
}

p {
  margin: 0 !important;
}

body {
  font-family: "Nunito", "Mulish", "Roboto", sans-serif, -apple-system, Tahoma,
    "Segoe UI", monospace !important;
  margin: 0;
  padding: 0 !important;
  font-size: 16px;
  color: var(--color-text-black);
}
input {
  color: var(--color-text-black);
}
a {
  text-decoration: none !important;
  color: unset;
}
button {
  padding: 0;
  cursor: pointer;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  line-height: normal !important;
  user-select: none;
}
button:active {
  animation: press 0.2s linear;
  -webkit-animation: press 0.2s linear;
}

.root-container {
  width: 100%;
  height: 100%;
  min-height: inherit;
  border-radius: 10px;
  // max-width: var(--max-width-view);
  background: transparent;
  margin: auto;
  display: flex;
  flex-direction: column;
  /* overflow: hidden; */

  & [class*="v-application"] {
    height: inherit;
    max-height: inherit;
    min-height: inherit;
    background: transparent;
    align-items: center;
  }
}

.root-container.ios {
  height: -webkit-fill-available;
}

.container-root {
  background-color: #f0f0f0;
  min-height: 100dvh !important;
  height: 100dvh !important;
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
  align-items: center;
}

.container-public {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  max-width: var(--max-width-view);
  background: white;
  margin: auto;
}
input {
  -webkit-appearance: none !important;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  border-image: none;
  font: inherit;
}
.input {
  font-size: var(--font-size);
  color: var(--primary-color-1);
  font-weight: 600;
  height: auto;
  padding: 0 5px;
  overflow: hidden;
  border: none;
  border-radius: 0;
  border-bottom: thin solid #ccc;
}
img{
  user-select: none;
}
.input-container {
  min-height: 0;
  border-bottom-color: "#ccc";
  overflow: hidden;
}
.w-100 {
  width: 100%;
}
.container-input-element {
  margin-bottom: 20;
  padding: 0;
}

/* .search-input-container:ac */
.label {
  font-size: 0.8em;
  color: var(--color-text-note);
  font-weight: bold;
  text-align: left;
}

.input-error {
  margin: 0 10px;
  font-size: calc(var(--font-size) * 0.75);
  font: "italic";
  color: "red";
}

.icon-input {
  padding: 0;
  font-size: var(--font-size);
  color: var(--primary-color-1);
}

.icon-input-right {
  padding: 10;
  font-size: var(--font-size);
  color: var(--primary-color-1);
  background-color: white;
  border-color: var(--primary-color-1);
  border-radius: 50%;
}

.icon-input-left {
  padding: 5px;
  font-size: var(--font-size);
  color: var(--primary-color-1);
}

.icon-input-right {
  padding: 5px;
  font-size: var(--font-size);
  color: var(--primary-color-1);
}

.button-action {
  margin-top: 5px;
  background-color: var(--primary-color-1);
  border-radius: 5px;
  padding: 10px;
  position: relative;
  text-align: center;
}

.icon-button-action {
  color: white;
  padding: 0 5px;
  font-size: var(--font-size);
  z-index: 30;
  margin-right: var(--font-size);
}

.my-checkbox {
  color: var(--primary-color-1);
}

.dropdown-container {
  padding: 5px 0;
}

.element-placeholder {
  color: var(--color-text-note);
  padding: 0 5px;
  font-weight: 600;
  font-size: var(--font-size);
}
.dropdown-select-container {
  border: none;
  border-bottom: thin solid #ccc;
  /* color: var(--primary-color-1); */
  padding: 0 5px;
  font-weight: 600;
  position: relative;
  font-size: 1em;

  & button {
    background-color: white;
    font-size: 1em;
    font-weight: 600;
    color: var(--color-text-black);
    padding: 10px;
    border: none;
  }
  & input {
    outline: none;
  }
  & .group {
    width: 95%;
    max-height: 500% !important;
    overflow: auto;
    box-shadow: 0 0 5px #ccc;
    border-radius: 10px;
    opacity: 1;
    z-index: 1000;
    background: white;
  }
  & ul {
    background: white !important;
    border: none !important;
    box-shadow: 0 0 5px #aaa !important;
    color: var(--color-text-black);
    margin: 0;
    border-radius: 5px;
    padding: 5px;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 5px;
    border-radius: 0;

    & li {
      color: var(--color-text-black) !important;
      padding: 5px;
      background-color: white !important;
      border-radius: 5px;
      cursor: pointer;
      display: flex;
      font-weight: 500;

      &:hover {
        background-color: #f5f6fa !important;
      }

      & span {
        color: var(--color-text-black) !important;
      }
    }
    & li[data-headlessui-state~="selected"] {
      background-color: var(--primary-color-1) !important;
      color: white !important;

      & span {
        color: white !important;
      }
    }
    & li[data-headlessui-state="selected"]:hover {
      background-color: var(--primary-color-1) !important;
      color: white !important;
    }
  }
}

// .dropdown-select-container > div[class*="control"] {
//   border: none !important;
//   outline: none !important;
//   box-shadow: none !important;
//   color: inherit;
// }
// .dropdown-select-container > div [class*="singleValue"] {
//   color: var(--primary-color-1);
//   font-size: 1.2em;
// }
// .dropdown-select-container > div [class*="indicatorSeparator"] {
//   display: none;
// }

.dropdown-input-search {
  font-size: var(--font-size);
  color: var(--color-text-note);
}

.dropdown {
  border-color: "#ccc";
  border-bottom-width: 10px;
  border-radius: 0;
  padding-bottom: 5px;
}

.dropdown-item {
  color: var(--primary-color-1);
  font-size: calc(var(--font-size) * 0.8);
}

.cancel-button {
  background-color: white;
  border-color: var(--primary-color-1);
  color: var(--primary-color-1);
}

.save-button {
  background-color: var(--primary-color-1);
  color: white;
}

.tag-count-button {
  width: 25px;
  height: 25px;
  background-color: var(--color-button-special);
  border-radius: 50%;
  padding: 0;
  justify-content: "center";
  align-items: "center";
  padding: 0;
}

.tag-count-disabled-button {
  background-color: var(--color-button-special);
}

.stack {
  display: flex;
  flex-direction: row;
}

.stack-wrap {
  display: flex;
  flex-wrap: wrap;
}

.h-stack {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.v-stack {
  display: flex;
  flex-direction: column;
}

.leaflet-div-icon,
.map-marker {
  background-color: #274abb;
  border: 5px solid #274abb;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  -webkit-transition: all 0.15s linear;
  transition: all 0.15s linear;
  z-index: 10;
}
.leaflet-div-icon::before {
  display: none;
}
.leaflet-div-icon::after {
  display: none;
}
.leaflet-div-icon {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}
.leaflet-div-icon:hover {
  top: 0;
}

.leaflet-div-icon.leaflet-editing-icon {
  background: #fff !important;
  border: 1px solid #666 !important;
  border-radius: 50% !important;
  transition: none;
}

.leaflet-div-icon::after {
  display: none;
}
.leaflet-div-icon {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}
.leaflet-div-icon:hover {
  top: 0;
}

.leaflet-div-icon.leaflet-editing-icon {
  background: #fff !important;
  border: 1px solid #666 !important;
  border-radius: 50% !important;
  transition: none;
}

.leaflet-marker-location {
  margin-left: -15px !important;
  margin-top: -40px !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}
.leaflet-driver-marker-location {
  box-shadow: none !important;
  border-radius: 0 !important;
}

.leaflet-marker-icon {
  border-radius: 50%;
  box-shadow: 0 0 5px black;
}
.leaflet-marker-icon.img-icon {
  margin-left: -20px !important;
  margin-top: -50px !important;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  border-radius: 2em;
  // padding: 2px;
  box-shadow: none !important;
  filter: drop-shadow(0px 0px 3px rgb(0, 0, 0, 0.5));
  // filter: drop-shadow(0px 0px 4px rgba(0, 0, 0, 90%));
  // clip-path: polygon(0 0, 100% 0, 100% 50%, 50% 100%, 0 50%);
  // background: var(--primary-color-1);

  & > img {
    width: 40px !important;
    height: 40px !important;
    aspect-ratio: 1/1;
    // height: 100% !important;
    object-fit: cover;
    border-radius: 2em;
    // outline: 2px solid var(--primary-color-1);
    box-shadow: 0 0 0 2px var(--primary-color-1);
    z-index: 2;
  }

  & .after {
    width: 20px;
    height: 20px;
    margin-top: -13px;
    margin-left: 10px;
    z-index: 1;
    border-radius: 100% 0 0;
    border-bottom-color: #fff;
    background-color: var(--primary-color-1);
    transform: rotate(45deg);
  }
}
.tooltip-leaflet-own {
  font-weight: 600;
  border-radius: 2em !important;
  padding: 0px 5px !important;
  margin-left: 0.5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5) !important;
}

.leaflet-marker-icon.red-shadow-icon {
  background: #eb2f06;
}

.leaflet-marker-icon.yellow-shadow-icon {
  background: #f1c40f;
}

.leaflet-marker-icon.orange-shadow-icon {
  background: #e67e22;
}

.leaflet-marker-icon.dark-green-shadow-icon {
  background: #1abc9c;
}

.leaflet-marker-icon.violet-shadow-icon {
  background: #b53471;
}

.my-cluster-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  box-shadow: 0 0 5px black;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.red-shadow-icon {
  background: #eb2f06;
}

.yellow-shadow-icon {
  background: #f1c40f;
}

.orange-shadow-icon {
  background: #e67e22;
}

.dark-green-shadow-icon {
  background: #1abc9c;
}

.violet-shadow-icon {
  background: #b53471;
}

.leaflet-control-zoom {
  height: 100px !important;
  width: 40px !important;
  border-radius: 2em !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.leaflet-bottom {
  z-index: 450 !important;
}
.leaflet-control-attribution {
  display: none;
}

.spinner-border {
  width: 2em !important;
  height: 2em !important;
  font-size: 0.5em;
}

.view-cart-button {
  position: sticky;
  bottom: 10px;
  margin: auto auto 10px;
  border-radius: 2em;
  width: calc(100% - 100px);
  background-color: var(--primary-color-1);
  border: none;
  padding: 10px 15px;
  color: white;
  font-weight: 600;
  box-shadow: 0 0 10px rgb(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  font-size: 18px;

  & > span {
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.view-cart-button > .view-cart-text {
  margin-left: auto;
  white-space: nowrap;
  width: fit-content;
  overflow: visible;
}

.modal-container {
  display: flex;
  flex: 1;
  position: relative;
  align-items: center;
  justify-content: center;
}

.modal-content-container {
  background: white;
  padding: 10px;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  border: thin solid rgba(0, 0, 0, 0.1);
}

.confirm-modal-buttons {
  margin-top: 10px;
  justify-content: space-evenly;
  gap: 10px;
  width: 100%;
}

.accept-button {
  color: white;
  width: 40%;
  height: 35px;
  border: thin solid var(--primary-color-1);
  background-color: var(--primary-color-1);
  border-radius: 2em;
  font-size: 17px;
  font-weight: 500;
}
.reject-button {
  color: var(--primary-color-1);
  width: 40%;
  height: 35px;
  border: thin solid var(--primary-color-1);
  background-color: white;
  border-radius: 2em;
  font-size: 17px;
  font-weight: 500;
}
button:disabled {
  opacity: 0.5;
}

.accept-button:disabled,
.reject-button:disabled {
  border-color: transparent;
  // background: #f5f6fa;
  opacity: 0.5;
  color: var(--color-text-note);
}

.confirm-modal {
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.confirm-modal > .modal-dialog {
  margin: 0;
}

input.input-custom,
.text-area-custom {
  background: transparent;
  border: none;
  border-bottom: thin solid #ccc;
  outline: none;
  padding: 0 5px 5px;
  color: var(--color-text-black);
}

.required::after {
  content: "*";
  margin-left: 3px;
  color: var(--primary-color-2);
}

.error-message {
  color: var(--primary-color-2);
  font-style: italic;
  font-size: 0.8em;
}
[class*="toast-container"] {
  z-index: 100000 !important;
}
// .toast-title {
//   font-weight: 500;
//   color: var(--primary-color-1);
//   font-size: 1.2em;
// }

// .toast-message {
//   font-weight: 400;
// }
// .Toastify__toast-icon {
//   width: unset !important;
// }
// .toast-icon {
// font-size: 2em;

// &.success {
//   color: rgb(15, 141, 15);
// }
// &.info {
//   color: rgb(35, 185, 255);
// }
// &.warning {
//   color: rgb(255, 208, 0);
// }
// &.error {
//   color: red;
// }
// }

.data {
  white-space: pre-line;
}

.my-modal-container {
  z-index: 1002;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: inherit;
  height: 100dvh;

  & > .my-modal-content-container {
    // min-height: 95dvh;
    // max-height: 95dvh;
    // height: 95dvh;
    width: 500px;
    // max-height: 90%;
    max-width: 95%;
    // min-height: 40dvh;
    border-radius: 10px;
    padding: 0 10px;
    background-color: white;
  }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
}
textarea {
  color: var(--color-text-black);
}
.price-text {
  color: var(--primary-color-1);
  font-size: 0.9em;
  font-style: italic;
  padding: 0 10px;
}

.my-switches {
  & .v-selection-control {
    min-height: unset !important;
  }
}

.btn-leaflet {
  position: absolute;
  z-index: 450;
}

.btn-leaflet-left {
  left: 10px;
}

.btn-leaflet-right {
  right: 10px;
}

.my-location-icon {
  font-size: 2em;
  color: rgb(0, 0, 0, 80%);
}

#fullScreen_map {
  background: white;
  z-index: 450;
  position: absolute;
  bottom: 210px;
  color: black;
  display: flex;
  right: 10px;
  width: fit-content;
  min-width: 34px;
  padding: 9px;
  margin: 0 3px;
  font-size: 1.5em;
  cursor: pointer;
  border-radius: 3px;
}

.map-type-btn {
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  margin: 5px;
  width: fit-content;
  white-space: nowrap;
  height: 60px;
  min-width: 6em;
  border: 3px solid white;
  border-radius: 15px;
  cursor: pointer;
  bottom: 10px !important;
  right: 55px;
  left: auto !important;
  color: white;
}

.map-type-btn > span {
  margin-top: auto;
  width: 100%;
  height: 40%;
  display: flex;
  padding: 0 5px;
  justify-content: center;
  align-items: self-end;
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
  background-image: linear-gradient(
    to top,
    rgb(0, 0, 0, 80%),
    rgb(255, 255, 255, 0)
  );
}

.current-location-leaflet {
  position: absolute;
  bottom: 125px;
  right: 12px;
  background: white;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
  z-index: 450;
  box-shadow: 0 0 5px rgb(0 0 0 / 20%);
}

.leaflet-marker-location {
  margin-left: -15px !important;
  margin-top: -40px !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.leaflet-control-zoom {
  height: 100px !important;
  width: 40px !important;
  border-radius: 2em !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.leaflet-control-zoom > .leaflet-control-zoom-in {
  width: 100% !important;
  height: 50% !important;
  border-radius: inherit !important;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  align-items: center;
  justify-content: center;
  display: flex;
}

.leaflet-control-zoom > .leaflet-control-zoom-out {
  width: 100% !important;
  height: 50% !important;
  border-radius: inherit !important;
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
  align-items: center;
  justify-content: center;
  display: flex;
}

.v-btn {
  letter-spacing: unset !important;
}

.title-header {
  font-size: 1rem;
  margin: 0;
  text-align: center;
  border-bottom: thin solid #ccc;
  display: flex;
  align-items: center;
  // background: #f5f6fa;
  padding: 5px 0;

  & > h3 {
    font-size: 1.2rem;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    white-space: nowrap;
  }
  & .header-left {
    display: flex;
    padding-left: 5px;
    justify-content: left;
    gap: 5px;
    flex: 1;
    margin-right: auto;
    font-size: 1.5rem;

    & > button {
      // background: var(--color-background-2);
      border-radius: 50%;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
    }
  }
  & .header-right {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
    flex: 1;
    padding-right: 5px;
    margin-left: auto;
    font-size: 1.5rem;

    & > button {
      // background: var(--color-background-2);
      border-radius: 50%;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
    }
  }
}

.access-denied {
  width: 100%;
  align-items: center !important;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center !important;
  font-size: 1.5em;
  text-align: center;
  padding: 10px;
  flex: 1;

  & img {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    align-items: center;
    object-fit: contain;
    position: relative;
  }
  & span {
    color: var(--primary-color-1);
  }
}

.spinner-container {
  width: 100px;
  height: 100px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  & > svg {
    width: 100%;
    height: 100%;
    color: #f05976;
  }
}

input:autofill {
  background-color: inherit !important;
  color: inherit !important;
}

// .logo-origin-container {
//   --width-origin: 200px;
//   --height-origin: 200px;
//   --scale-origin: 200;

//   width: var(--width-origin);
//   height: var(--height-origin);
//   // transform: scale(1);
//   transform-origin: center;
//   display: flex;
//   background: white;
//   align-items: center;
//   justify-content: center;

//   & > img {
//     object-fit: cover;
//   }
// }

// .logo-origin-container.none-style {
//   width: 100%;
//   height: 100%;
//   transform: none !important;

//   > img {
//     width: 100%;
//     height: 100%;
//     object-fit: cover;
//   }
// }

// .banner-origin-container {
//   --width-origin: 400px;
//   --height-origin: 250px;
//   --scale-origin: 250;

//   width: 100%;
//   height: var(--height-origin);
//   transform: scale(1);
//   transform-origin: center;
//   display: flex;
//   background: white;
//   align-items: center;
//   justify-content: center;

//   & > img {
//     object-fit: cover;
//   }
// }

// .banner-origin-container.none-style {
//   width: 100%;
//   height: 100%;
//   transform: none !important;

//   > img {
//     width: 100%;
//     height: 100%;
//     object-fit: cover;
//   }
// }
.my-carousel {
  width: 100%;
  height: 100%;

  & img {
    width: 100%;
    height: 100%;
    min-height: 150px;
    max-height: 150px;
    object-fit: contain;
  }
}
.stack-carousel {
  flex: 1;

  & .swiper-button-prev.swiper-button-disabled,
  .swiper-button-next.swiper-button-disabled {
    pointer-events: inherit;
  }
  & .swiper-button-prev {
    // background: linear-gradient(270deg, transparent 30%, rgb(0, 0, 0, 10%)) !important;
    left: 0;
    // border-top-right-radius: 0;
    // border-bottom-right-radius: 0;
  }
  & .swiper-button-next {
    // background: linear-gradient(90deg, transparent 30%, rgb(0, 0, 0, 10%)) !important;
    right: 0;
    // border-top-left-radius: 0;
    // border-bottom-left-radius: 0;
  }
  & .swiper-button-prev,
  > .swiper-button-next {
    color: white;
    font-size: 15px;
    width: 30px;
    padding: 5px;
    height: 100%;
    border-radius: inherit;
    top: 0;
    bottom: 0;
    margin-top: 0;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  }
  & .swiper-button-prev::after,
  > .swiper-button-next::after {
    width: 100%;
    height: 100%;
    padding: 0 5px;
    font-size: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
  }
}

@keyframes ring {
  0% {
    transform: rotate(0);
  }
  1% {
    transform: rotate(30deg);
  }
  3% {
    transform: rotate(-28deg);
  }
  5% {
    transform: rotate(34deg);
  }
  7% {
    transform: rotate(-32deg);
  }
  9% {
    transform: rotate(30deg);
  }
  11% {
    transform: rotate(-28deg);
  }
  13% {
    transform: rotate(26deg);
  }
  15% {
    transform: rotate(-24deg);
  }
  17% {
    transform: rotate(22deg);
  }
  19% {
    transform: rotate(-20deg);
  }
  21% {
    transform: rotate(18deg);
  }
  23% {
    transform: rotate(-16deg);
  }
  25% {
    transform: rotate(14deg);
  }
  27% {
    transform: rotate(-12deg);
  }
  29% {
    transform: rotate(10deg);
  }
  31% {
    transform: rotate(-8deg);
  }
  33% {
    transform: rotate(6deg);
  }
  35% {
    transform: rotate(-4deg);
  }
  37% {
    transform: rotate(2deg);
  }
  39% {
    transform: rotate(-1deg);
  }
  41% {
    transform: rotate(1deg);
  }

  43% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(0);
  }
}
.new-orders-ticket {
  position: fixed;
  // right: 30px;
  // bottom: 70px;
  width: 79px;
  display: flex;
  border-radius: 50%;
  cursor: pointer;
  filter: drop-shadow(0 0 5px rgb(0, 0, 0, 0.1));
  z-index: 1;
  transition: all 0.2s ease-in-out;
  touch-action: none;
  will-change: transform;
  user-select: none;
  & > .my-shop:disabled {
    opacity: 1;
    cursor: grabbing;
  }
  & > .my-shop {
    display: flex;
    width: 100%;
    height: 79px;
    position: relative;
    background: white;
    border-radius: 50%;
    padding: 20%;

    & > span {
      background: #eb2f06;
      color: white;
      border: 2px solid white;
      position: absolute;
      bottom: 0px;
      transform: translateX(-50%);
      left: 90%;
      border-radius: 2em;
      height: 25px;
      font-size: 15px;
      padding: 0 5px;
      display: flex;
      align-items: center;
    }

    & > img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      animation: ring 5s 5s ease-in-out infinite;
      transform-origin: 4px 50%;
    }
  }
}
.overlay-dropdown-content > div {
  background: rgb(0, 0, 0, 80%) !important;
  border-radius: 15px !important;
}
.overlay-dropdown-item {
  color: white !important;
  text-align: center !important;
  background: transparent !important;
  font-size: 1em;
  position: relative;

  & > .v-list-item__content {
    display: flex;
    gap: 15px;
    align-items: center;
    & svg {
      font-size: 25px;
    }
  }
}

.need-update-container {
  min-height: unset !important;
  background-color: white;
  gap: 10px;
  width: 500px !important;
  // max-height: 90%;
  max-width: 95% !important;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;

  & .accept-button {
    border-radius: 2em !important;
  }
}
.need-update-content {
  font-size: 1.3em;
  text-align: center;
}

.need-update-title {
  font-size: 1em;
  font-weight: bold;
}

.need-update-message {
  font-size: 1em;
  text-align: center;
}

@keyframes high-light {
  50% {
    opacity: 0;
  }
}
.error-message.hight-light:not(.success) {
  transform-origin: 0 0;
  animation: high-light 0.5s ease-in-out infinite;
}

.custom-v-select {
  background-color: white;
  // height: 45px;
  // padding: 10px 10px 10px 30px;
  border-radius: 5px;
  outline: none;
  color: #2e2d30;
  font-weight: 600;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;

  & .v-field {
    --v-input-control-height: 100% !important;
  }

  & .add-category {
    width: 100% !important;
    display: flex;
    font-size: 20px;
  }

  & .v-input__control {
    padding: 5px 10px 5px 30px;
    height: fit-content !important;
    min-height: 45px !important;
  }

  & .v-input__append {
    padding: 5px 10px 5px 0 !important;
    align-items: center !important;
    margin: 0 !important;
    display: flex;
    justify-content: center;
  }

  & .v-input__details {
    display: none !important;
  }

  & .chip {
    background: #2e2d30;
    color: white;
    padding: 0px 10px !important;
    border-radius: 2em;
  }

  & .v-field__append-inner {
    padding: 0 !important;
    align-items: center !important;
    font-size: 25px;
  }

  & .v-field__clearable {
    padding: 0 !important;
    align-items: center !important;
  }

  & .v-field__input {
    height: 100%;
    align-items: center;
    padding: 0 !important;
    min-height: 0;
  }
  & .v-field__input input::placeholder {
    // color: #2e2d30;
    opacity: 1 !important;
  }
}

.public-container {
  width: 100%;
  height: 100%;
  flex: 1;
  // min-height: 100%;
  overflow: auto;
  display: flex;
  justify-content: center;
  & > div {
    max-width: var(--max-width-view);
    overflow: unset;
    margin: auto;
    width: 100%;
    height: fit-content;
    min-height: 100%;
  }

  & > div.h-fit {
    height: fit-content;
    min-height: unset;
  }
}

.public-container.h-fit {
  height: fit-content;
  flex: 0;
  overflow: visible;
}

.my-marker-image {
  width: 40px !important;
  height: 40px !important;
  aspect-ratio: 1/1;
  // height: 100% !important;
  object-fit: cover;
  border-radius: 2em;
  // outline: 2px solid var(--primary-color-1);
  box-shadow: 0 0 0 2px var(--primary-color-1);
  z-index: 2;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;

  & > img {
    width: 100% !important;
    height: 100%;
    object-fit: cover;
  }
}
.custom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1000; /* Ensure it sits behind the modals */
}