<template>
  <div class="stock-current-component">
    <!-- Sub Header -->
    <SubHeaderV2Component
      :title="$t('StockManagement.ton_kho_hien_tai')"
      :show-back="true"
      @back="$router.back()"
    />

    <!-- Header -->
    <div class="component-header">
      <p>{{ $t('StockManagement.xem_ton_kho_san_pham') }}</p>
    </div>

    <!-- Search and Filter -->
    <div class="search-section">
      <div class="search-input-wrapper">
        <Icon name="solar:magnifer-bold" size="20" class="search-icon" />
        <input
          v-model="searchQuery"
          type="text"
          :placeholder="$t('StockManagement.tim_kiem_san_pham')"
          class="search-input"
          @input="handleSearch"
        />
        <button v-if="searchQuery" @click="clearSearch" class="clear-search-btn">
          <Icon name="solar:close-circle-bold" size="20" />
        </button>
      </div>
      
      <!-- Stock Level Filter -->
      <div class="filter-tabs">
        <button 
          v-for="filter in stockFilters" 
          :key="filter.key"
          @click="activeFilter = filter.key"
          :class="['filter-tab', { active: activeFilter === filter.key }]"
        >
          <Icon :name="filter.icon" size="16" />
          {{ filter.label }}
        </button>
      </div>
    </div>

    <!-- Stock Summary Cards -->
    <div class="summary-cards">
      <div class="summary-card">
        <div class="card-icon normal">
          <Icon name="solar:box-bold" size="24" />
        </div>
        <div class="card-content">
          <h3>{{ totalProducts }}</h3>
          <p>{{ $t('StockManagement.tong_san_pham') }}</p>
        </div>
      </div>
      
      <div class="summary-card">
        <div class="card-icon low">
          <Icon name="solar:danger-bold" size="24" />
        </div>
        <div class="card-content">
          <h3>{{ lowStockProducts }}</h3>
          <p>{{ $t('StockManagement.sap_het_hang') }}</p>
        </div>
      </div>
      
      <div class="summary-card">
        <div class="card-icon empty">
          <Icon name="solar:close-circle-bold" size="24" />
        </div>
        <div class="card-content">
          <h3>{{ outOfStockProducts }}</h3>
          <p>{{ $t('StockManagement.het_hang') }}</p>
        </div>
      </div>
    </div>

    <!-- Products List -->
    <div class="products-section">
      <div v-if="loading" class="loading-state">
        <Icon name="solar:refresh-bold" size="32" class="loading-icon" />
        <p>{{ $t('StockManagement.dang_tai') }}</p>
      </div>

      <div v-else-if="filteredProducts.length === 0" class="empty-state">
        <Icon name="solar:box-bold" size="48" />
        <h3>{{ $t('StockManagement.khong_co_san_pham') }}</h3>
        <p>{{ $t('StockManagement.khong_tim_thay_san_pham') }}</p>
      </div>

      <div v-else class="products-list">
        <div 
          v-for="product in filteredProducts" 
          :key="product.id"
          class="product-item"
          @click="viewProductHistory(product.id)"
        >
          <div class="product-image">
            <img :src="product.image || '/default-product.png'" :alt="product.name" />
          </div>
          
          <div class="product-info">
            <h4>{{ product.name }}</h4>
            <p class="product-category">{{ product.category }}</p>
            <div class="stock-info">
              <span class="stock-level" :class="getStockLevelClass(product.current_stock)">
                {{ product.current_stock || 0 }} {{ product.unit }}
              </span>
              <span class="stock-status" :class="getStockStatusClass(product.current_stock)">
                {{ getStockStatusText(product.current_stock) }}
              </span>
            </div>
          </div>
          
          <div class="product-actions">
            <Icon name="solar:arrow-right-bold" size="20" />
          </div>
        </div>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMoreProducts && !loading" class="load-more-section">
        <button @click="loadMoreProducts" class="load-more-btn">
          {{ $t('StockManagement.tai_them') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue'
import { HttpStatusCode } from "axios";

// Props
const props = defineProps<{
  mode?: string
}>()

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Use the centralized shop data composable
const {
  activeShop,
  isLoading: shopDataLoading,
  initializeShopData,
  getShopBySlug
} = useShopData()

// Services
const stockService = new StockService()

// Reactive data
const loading = ref(false)
const searchQuery = ref('')
const activeFilter = ref('all')
const products = ref([])
const currentPage = ref(0)
const hasMoreProducts = ref(true)
const pageSize = 20

// Stock filters
const stockFilters = computed(() => [
  { key: 'all', label: t('StockManagement.tat_ca'), icon: 'solar:list-bold' },
  { key: 'normal', label: t('StockManagement.binh_thuong'), icon: 'solar:check-circle-bold' },
  { key: 'low', label: t('StockManagement.sap_het'), icon: 'solar:danger-bold' },
  { key: 'empty', label: t('StockManagement.het_hang'), icon: 'solar:close-circle-bold' }
])

const shopSlug = route.params.id as string
const shopId = computed(() => {
    const shop = getShopBySlug(shopSlug)
    return shop?.id || shopSlug
})

const filteredProducts = computed(() => {
  let filtered = products.value

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(product => 
      product.name.toLowerCase().includes(query) ||
      product.category?.toLowerCase().includes(query)
    )
  }

  // Filter by stock level
  if (activeFilter.value !== 'all') {
    filtered = filtered.filter(product => {
      const stock = product.current_stock || 0
      switch (activeFilter.value) {
        case 'normal':
          return stock > 10
        case 'low':
          return stock > 0 && stock <= 10
        case 'empty':
          return stock <= 0
        default:
          return true
      }
    })
  }

  return filtered
})

const totalProducts = computed(() => products.value.length)

const lowStockProducts = computed(() => 
  products.value.filter(p => {
    const stock = p.current_stock || 0
    return stock > 0 && stock <= 10
  }).length
)

const outOfStockProducts = computed(() => 
  products.value.filter(p => (p.current_stock || 0) <= 0).length
)

// Methods
const getStockLevelClass = (stock: number) => {
  if (stock <= 0) return 'stock-empty'
  if (stock <= 10) return 'stock-low'
  return 'stock-normal'
}

const getStockStatusClass = (stock: number) => {
  if (stock <= 0) return 'status-empty'
  if (stock <= 10) return 'status-low'
  return 'status-normal'
}

const getStockStatusText = (stock: number) => {
  if (stock <= 0) return t('StockManagement.het_hang')
  if (stock <= 10) return t('StockManagement.sap_het')
  return t('StockManagement.con_hang')
}

const handleSearch = () => {
  // Debounce search if needed
  // For now, just trigger reactive update
}

const clearSearch = () => {
  searchQuery.value = ''
}

const viewProductHistory = (productId: string) => {
  if (props.mode === 'agent') {
    const shopSlug = route.params.id as string
    router.push(`/agent/shop/${shopSlug}/stock/history/${productId}`)
  } else {
    router.push(`/my-shop/stock/history/${productId}`)
  }
}

const loadProducts = async (reset = false) => {
  if (loading.value) return

  loading.value = true
  try {
    const offset = reset ? 0 : currentPage.value * pageSize
    const response = await stockService.getCurrentStock(shopId.value, offset, pageSize)

    if (response.status === HttpStatusCode.Ok) {
      const newProducts = response.data?.products || []

      if (reset) {
        products.value = newProducts
        currentPage.value = 0
      } else {
        products.value = [...products.value, ...newProducts]
      }

      hasMoreProducts.value = newProducts.length === pageSize
      currentPage.value++
    }
  } catch (error) {
    console.error('Error loading products:', error)
  } finally {
    loading.value = false
  }
}

const loadMoreProducts = () => {
  loadProducts(false)
}

const refreshProducts = () => {
  loadProducts(true)
}

// Watch for filter changes
watch(activeFilter, () => {
  // Filter is applied via computed property, no need to reload
})

// Lifecycle
onMounted(async () => {
  // Initialize shop data first
  await initializeShopData()
  // Load initial products
  await loadProducts(true)
})
</script>

<style scoped>
.stock-current-component {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750px;
}

.component-header {
  margin-bottom: 20px;
  text-align: center;

  p {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.search-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  position: relative;
  margin-bottom: 15px;

  .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
  }

  .search-input {
    width: 100%;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 12px 15px 12px 45px;
    font-size: 14px;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #3498db;
      background-color: #f8f9fa;
    }
  }

  .clear-search-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;

    &:hover {
      color: #e74c3c;
    }
  }
}

.filter-tabs {
  display: flex;
  gap: 8px;
  overflow-x: auto;

  .filter-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 2px solid #ecf0f1;
    border-radius: 20px;
    background: white;
    color: #7f8c8d;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      border-color: #3498db;
      color: #3498db;
    }

    &.active {
      background: #3498db;
      border-color: #3498db;
      color: white;
    }
  }
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;

  .summary-card {
    background: white;
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .card-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      &.normal {
        background: #d5f4e6;
        color: #27ae60;
      }

      &.low {
        background: #fef9e7;
        color: #f39c12;
      }

      &.empty {
        background: #fadbd8;
        color: #e74c3c;
      }
    }

    .card-content {
      h3 {
        font-size: 18px;
        font-weight: 700;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0;
      }
    }
  }
}

.products-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;

  .loading-icon {
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 15px 0 8px 0;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.products-list {
  .product-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3498db;
      background-color: #f8f9fa;
    }

    .product-image {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .product-info {
      flex: 1;

      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      .product-category {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0 0 8px 0;
      }

      .stock-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .stock-level {
          font-size: 12px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 4px;

          &.stock-normal {
            background: #d5f4e6;
            color: #27ae60;
          }

          &.stock-low {
            background: #fef9e7;
            color: #f39c12;
          }

          &.stock-empty {
            background: #fadbd8;
            color: #e74c3c;
          }
        }

        .stock-status {
          font-size: 11px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 4px;

          &.status-normal {
            background: #d5f4e6;
            color: #27ae60;
          }

          &.status-low {
            background: #fef9e7;
            color: #f39c12;
          }

          &.status-empty {
            background: #fadbd8;
            color: #e74c3c;
          }
        }
      }
    }

    .product-actions {
      color: #bdc3c7;
    }
  }
}

.load-more-section {
  text-align: center;
  margin-top: 20px;

  .load-more-btn {
    background: #ecf0f1;
    color: #7f8c8d;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #d5dbdb;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .stock-current-component {
    padding: 10px;
  }

  .summary-cards {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .search-section,
  .products-section {
    padding: 15px;
  }

  .filter-tabs {
    .filter-tab {
      font-size: 11px;
      padding: 6px 10px;
    }
  }

  .product-item {
    .product-image {
      width: 40px;
      height: 40px;
    }
  }
}
</style>
