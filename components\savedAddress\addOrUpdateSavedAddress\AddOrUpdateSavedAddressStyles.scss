.saved-address-modal-container{
  width: 500px !important;
  max-width: 95% !important;
  height: 95% !important;
  max-height: 95% !important;
  padding: 0 !important;
}

.edit-customer-info-container {
  background-color: #f6f5fb;
  gap: 10px;
  border-radius: 10px;
  flex: 1;
  height: 100%;
  position: relative;
  overflow: auto;
  & > .title {
    text-align: center;
    font-size: 1.2em;
    color: var(--color-text-note);
  }

  & > .edit-customer-info-content{
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
    height: 100%;
    padding: 10px;
    gap: 5px;
  }

  & .current-location-leaflet-btn{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    margin-left: auto;
    color: #3393cb;
    gap: 5px;
    margin-bottom: 5px;
    font-weight: 600;

    & > svg{
      color: #3393cb;
      font-size: 20px;
    }
  }
  & .v-stack {
    margin-bottom: 5px;

    & .label{
      color: #545454;
      font-size: 13px
    }

    & > .title-address-options{
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      padding: 0 0 10px 0;

      & .title-option {
        padding: 3px 15px;
        color: #2e2d30;
        background: #ddd;
        border-radius: 5px;
        font-size: 15px;
        border: thin solid transparent;
      }
      & .title-option.active{
        color: white;
        background: var(--primary-color-1);
        border-color: var(--primary-color-1);
      }
    }

    & > .input-order {
      background-color: white;
      height: 45px;
      padding: 10px 10px 10px 30px;
      border-radius: 5px;
      outline: none;
      color: #2e2d30;
      font-weight: 600;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 100%;
    }

    &>.v-input-order{
      background: white;
      margin: 5px 0;
      color: #545454;
      & input.v-field__input{
        padding: 5px 10px;
        height: 45px;
        min-height: unset;
      }
    }

    &>.input-address{
      background-color: white;
      border-radius: 5px;
      outline: none;
      color: #545454;
      font-weight: 400;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 100%;
      display: flex;

      & > textarea{
        width: 100%;
        padding: 10px;
        outline: none;
        height: 100% !important;
      }
    }

    & > .textarea-order{
      background-color: white;
      padding: 10px 10px 10px 30px;
      border-radius: 5px;
      outline: none;
      color: #2e2d30;
      font-weight: 400;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 100%;
    }

    & .coordinate-button {
      font-size: 15px;
      color: var(--primary-color-1);
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 5px;
      font-weight: 600;

      &>svg{
        font-size: 20px;
      }
    }

    &>.change-address-on-moving {
      display: flex;
      gap: 5px;
      align-items: center;
      color: #545454;
      font-weight: 600;
      cursor: pointer;
      font-size: 15px;
      margin-left: auto;
      margin-top: 0;

      &.active {
        color: #3393cb;
      }

      &>svg {
        font-size: 25px;
      }
    }

    & .coordinate-input-container{
      background: white;
      border-radius: 5px;
      color: #545454;
      box-shadow: 0 0 10px rgb(0,0,0, .2);
      height: 45px;
      width: 300px;
      margin-top: 3px;
      margin-right: 5px;
      display: flex;
      align-items: center;
      justify-content: center;

      & > button{
        display: flex;
        align-items: center;
        font-size: 17px;
        color: var(--primary-color-1);
        height: 100%;
        padding: 10px;

        &:hover{
          background: color-mix(in srgb, var(--primary-color-1) 20%, transparent);
        }
      }

      & > input{
        height: 100%;
        padding: 10px;
        flex: 1;
        outline: none;
      }
    }

    &.image {
      display: flex;
      flex-direction: column;
      gap: 5px;
      & > .image-list {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;

        & > .select-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: 3px dashed #9ca3af;
          color: #9ca3af;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          cursor: pointer;

          & > label {
            width: 100%;
            height: 100%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            font-size: 45px;
            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: -1;
            }
          }
        }
        & > .selected-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: thin solid #e7e9ec;
          color: #e7e9ec;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          position: relative;

          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: inherit;
          }

          & > .action-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 20px;
            // width: 100%;
            // height: 100%;
            // display: none;
            display: flex;
            color: white;
            justify-content: flex-end;
            align-items: flex-end;
            // padding: 5px;
            gap: 5px;
            background-color: rgb(0, 0, 0, 0.5);
            border-radius: 10px 0;

            & > button {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }

        & > .selected-image:hover {
          & > .action-overlay {
            display: flex;
          }
        }
      }
    }
  }

  & > .save-info-container > label {
    justify-content: center;
    align-items: center;
    display: flex;
    cursor: pointer;

    & > .save-user-info-icon {
      appearance: none;
      width: 1em;
      height: 1em;
      border-radius: 50%;
      margin-right: 5px;
      border: 2px solid var(--primary-color-1);
      outline: none;
      cursor: pointer;
    }

    & > .save-user-info-icon:checked {
      background-color: var(--primary-color-1);
      box-shadow: inset 0px 0px 0 3px white;
    }
  }

  & .map-container {
    width: 100%;
    flex: 1;
    min-height: 350px;
    height: 350px;
    border-radius: 10px;
    margin-top: 5px;

    & .leaflet-container {
      border-radius: 10px 10px 0 0;
      height: 100%;
      min-height: inherit !important;

      & .marker-location {
        width: 30px;
        height: 40px;
        position: absolute;
        bottom: 50%;
        left: 50%;
        object-fit: contain;
        transform: translateX(-50%);
        z-index: 450;

        & > img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }

  & > .edit-customer-info-actions {
    width: 100%;
    padding: 10px 5px;
    gap: 5px;
    justify-content: space-evenly;
  }
}

.edit-customer-info-modal {
  padding: 0 !important;
  overflow: hidden;

  & .label {
    color: #2e2d30;
    font-weight: bold;
    font-size: 13px;
  }

  & .accept-button:disabled {
    color: white;
    background-color: var(--primary-color-1);
    border-radius: 2em;
    opacity: 0.5;
  }
}
