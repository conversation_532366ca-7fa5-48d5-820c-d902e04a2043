<template>
    <VueFinalModal class="my-modal-container" :click-to-close="false" :overlay-behavior="'persist'"
        content-class="my-modal-content-container form-modal edit-customer-info-modal" v-model="isEditingCustomerInfo"
        v-on:closed="() => {
            isEditingCustomerInfo = false;
        }" contentTransition="vfm-slide-down">
        <div class='v-stack edit-customer-info-container'>
            <SubHeaderV2Component :title="$t('EditReceiverInfoComponent.thong_tin_nguoi_nhan')">
                <template v-slot:header_left></template>
            </SubHeaderV2Component>
            <div class="edit-customer-info-content">
                <button class="select-user-saved" v-if="props.user_info" v-on:click="() => {
                    emit('show_select_user')
                }">
                    {{ $t('EditReceiverInfoComponent.chon_tu_so_dia_chi') }}
                    <!-- <Icon name="material-symbols:person-pin-circle-outline"></Icon> -->
                </button>
                <div class='v-stack'>
                    <!-- <span class='label required'>
                        {{ $t('EditReceiverInfoComponent.ten') }}
                    </span>
                    <input :title="$t('EditReceiverInfoComponent.ten')" name='customer-name' maxLength=255 autoComplete="on" class='input-order'
                        :placeholder="$t('EditReceiverInfoComponent.ten_placeholder')" :value="name || null" v-on:input="($event: any) => {
                            name = $event.target.value;
                            nameValidation()
                        }" v-on:blur="() => {
                            nameValidation()
                        }" /> -->
                    <v-text-field :label="$t('EditReceiverInfoComponent.ten')" variant="outlined" name="customer-name"
                        maxLength=255 autoComplete="on" class="v-input-order" v-model="name" hide-details
                        v-on:update:model-value="() => {
                            nameValidation()
                        }" v-on:update:focused="() => {
                            nameValidation()
                        }">
                        <template v-slot:label>
                            <span class='label required'>
                                {{ $t('EditReceiverInfoComponent.ten') }}
                            </span>
                        </template>
                    </v-text-field>
                    <span class='error-message'>{{ nameErr }}</span>
                </div>
                <div class='v-stack'>
                    <!-- <span class='label required'>
                        {{ $t('EditReceiverInfoComponent.so_dien_thoai') }}
                    </span>
                    <input :title="$t('EditReceiverInfoComponent.so_dien_thoai')" name='customer-phone' maxLength=255
                        type="phone" autoComplete="on" class='input-order'
                        :placeholder="$t('EditReceiverInfoComponent.so_dien_thoai_placeholder')" :value="phone || null"
                        v-on:input="($event: any) => {
                            phone = validPhone($event.target.value);
                            phoneValidation();
                        }" v-on:blur="() => {
                            phoneValidation();
                        }" /> -->
                    <v-text-field :label="$t('EditReceiverInfoComponent.so_dien_thoai')" variant="outlined"
                        name="customer-phone" type="phone" maxLength=255 autoComplete="on" class="v-input-order"
                        v-model="phone" hide-details v-on:update:model-value="() => {
                            phoneValidation()
                        }" v-on:update:focused="() => {
                            phoneValidation()
                        }">
                        <template v-slot:label>
                            <span class='label required'>
                                {{ $t('EditReceiverInfoComponent.so_dien_thoai') }}
                            </span>
                        </template>
                    </v-text-field>
                    <span class='error-message'>{{ phoneErr }}</span>
                </div>

                <div class='v-stack'>
                    <div class="h-stack">
                        <span class='label required'>
                            {{ $t('EditReceiverInfoComponent.vi_tri_dia_chi') }}
                        </span>

                        <button class="current-location-leaflet-btn"
                            :title="$t('EditReceiverInfoComponent.lay_vi_tri_hien_tai')" v-on:click="() => {
                                gotoCurrentLocationLeaflet();
                            }
                            ">
                            <Icon name="hugeicons:location-09" class="my-location-icon" />
                            {{ $t('EditReceiverInfoComponent.lay_vi_tri_hien_tai') }}
                        </button>
                        <v-menu class="bootstrap-dropdown-container coordinate-input-menu" location="bottom right"
                            :close-on-content-click="false" contained>
                            <template v-slot:activator="{ props }">
                                <button class="coordinate-button" v-bind="props">
                                    <Icon name="ic:outline-edit-location"></Icon>
                                    <span>{{ $t('EditReceiverInfoComponent.nhap_toa_do') }}</span>
                                </button>
                            </template>
                            <div class="coordinate-input-container">
                                <input type="text" v-model="coordinatesText"
                                    :placeholder="$t('EditReceiverInfoComponent.toa_do_x_y')"
                                    v-on:keydown.enter="() => { jumpToCoordinate() }">
                                <button class="submit-coordinate" v-on:click="() => { jumpToCoordinate() }">
                                    <Icon name="gis:location-poi"></Icon>
                                </button>
                            </div>

                        </v-menu>
                    </div>

                    <div class="map-container">
                        <client-only>
                            <LMap id="leaflet_map_order" height="200" v-on:ready="(e: any) => {
                                leafletMap = e;
                                leafletMap.setMaxZoom(appConst.leafletMapTileOption.maxZoom);

                                initLeafletMap();
                            }" :max-zoom="appConst.leafletMapTileOption.maxZoom" v-on:update:center="async (bounds: any) => {
                                console.log('center change');
                                latitude = leafletMap.getCenter().lat;
                                longitude = leafletMap.getCenter().lng;
                                await getUserAddress();

                            }" :options="{ zoomControl: false, zIndex: 1 }" :world-copy-jump="true"
                                :use-global-leaflet="true">
                                <LControlZoom position="bottomright"></LControlZoom>
                                <span class="current-location-leaflet" v-if="false"
                                    :title="$t('EditReceiverInfoComponent.vi_tri_cua_ban')" v-on:click="() => {
                                        gotoCurrentLocationLeaflet();
                                    }
                                    ">
                                    <Icon name="line-md:my-location-loop" class="my-location-icon" />
                                </span>
                                <div id="leaflet-map-tile" class="map-type-btn btn-leaflet" data-toggle="tooltip"
                                    data-placement="right"
                                    :title="$t('EditReceiverInfoComponent.nhan_de_chuyen_loai_map')" v-bind:style="{
                                        backgroundImage: `url(` + buttonMapTileBackgound + `)`,
                                    }" v-on:click="(event: any) => {
                                        if (event.isTrusted) {
                                            if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.roadmap
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.hyprid;
                                                mapTypeTitle = $t('EditReceiverInfoComponent.ve_tinh');
                                                mapType = 'hyprid';
                                                buttonMapTileBackgound = map_sateline;
                                            } else if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.hyprid
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.streetmap;
                                                mapTypeTitle = $t('EditReceiverInfoComponent.co_dien');
                                                mapType = 'hyprid';
                                                buttonMapTileBackgound = map_streetmap;
                                            } else if (
                                                leafletMapTileUrl ==
                                                appConst.leafletMapTileUrl.streetmap
                                            ) {
                                                leafletMapTileUrl =
                                                    appConst.leafletMapTileUrl.roadmap;
                                                mapTypeTitle = $t('EditReceiverInfoComponent.ve_tinh_nhan');
                                                mapType = 'roadmap';
                                                buttonMapTileBackgound = map_sateline;
                                            }
                                        } else event.preventDefault();
                                    }
                                    ">
                                    <span>{{ mapTypeTitle }}</span>
                                </div>
                                <LTileLayer :url="leafletMapTileUrl" :options="appConst.leafletMapTileOption"
                                    :max-zoom="appConst.leafletMapTileOption.maxZoom" :min-zoom="5" layer-type="base"
                                    name="GoogleMap">
                                </LTileLayer>
                                <div class="marker-location">
                                    <img loading="lazy" :src="marker_location_icon" :placeholder="marker_location_icon"
                                        alt="" />
                                </div>
                            </LMap>
                        </client-only>
                    </div>

                    <!-- <input :title="$t('EditReceiverInfoComponent.dia_chi')" name='customer-address' maxLength=255
                        autoComplete="on" class='input-order'
                        :placeholder="$t('EditReceiverInfoComponent.dia_chi_placeholder')" :value="address || null"
                        v-on:input="($event: any) => {
                            address = $event.target.value;
                            addressValidation()
                        }" v-on:blur="() => {
                            addressValidation()
                        }" /> -->
                    <label class="change-address-on-moving" :class="{
                        'active': changeAddressOnMoving
                    }" for="change_address_on_moving" v-on:click="() => {
                        changeAddressOnMoving = !changeAddressOnMoving
                    }">
                        <v-switch :model-value="changeAddressOnMoving" v-on:update:model-value="(e: any) => {
                            changeAddressOnMoving = e;
                        }" flat color="#3393cb" hide-details class="my-switches" name="change_address_on_moving">

                        </v-switch>
                        <span>{{ $t('EditReceiverInfoComponent.cap_nhat_dia_chi_khi_di_chuyen_ban_do') }}</span>
                    </label>
                    
                    <!-- Address Dropdown -->
                    <div class="address-dropdown-container" v-if="addressSearchResults.length > 0 && showAddressDropdown">
                        <v-select
                            :items="addressSearchResults"
                            item-title="address"
                            item-value="address"
                            :label="$t('EditReceiverInfoComponent.dia_chi')"
                            variant="outlined"
                            v-model="selectedAddressFromDropdown"
                            v-on:update:model-value="selectAddressFromDropdown"
                            hide-details
                            class="address-dropdown"
                        >
                            <template v-slot:item="{ props, item }">
                                <v-list-item v-bind="props" :title="item.raw.address"></v-list-item>
                            </template>
                        </v-select>
                    </div>

                    <UTextarea autoresize variant="outline" :maxrows="5" :rows="1"
                        :label="$t('EditReceiverInfoComponent.dia_chi')" name="customer-address" class='input-address'
                        :placeholder="$t('EditReceiverInfoComponent.dia_chi_placeholder')" v-model="address"
                        v-on:update:model-value="(e: any) => {
                            addressValidation()
                            hideAddressDropdown()
                        }" v-on:blur="(e: any) => {
                            addressValidation()
                        }" v-on:focus="() => {
                            if (addressSearchResults.length > 0) {
                                showAddressDropdown = true
                            }
                        }" id="customer_address"></UTextarea>
                    <span class='error-message'>{{ addressErr }}</span>


                </div>

                <div class="v-stack image">
                    <span class="label">{{ t('EditReceiverInfoComponent.anh') }} <em>({{ images?.length ??
                        0 }}/3)</em></span>
                    <div class="image-list">
                        <div class="select-image" v-if="images.length < 3">
                            <label :disabled="images.length >= 3">
                                <Icon name="ion:plus-round"></Icon>
                                <input type="file" accept='image/*' :multiple="true" v-on:click="(e) => {
                                    if (images.length >= 3) {
                                        e.preventDefault()
                                    }
                                }" v-on:change="($event: any) => {
                                    fileChangeInput($event)
                                }" ref="imageFilesName" />
                            </label>
                        </div>
                        <div class="selected-image" v-for="(itemImamge, index) in images">
                            <img :src="itemImamge?.id ? (domainImage + itemImamge.path) : itemImamge.src" />
                            <div class="action-overlay">
                                <button class="delete-image" v-on:click="() => {
                                    images.splice(index, 1)
                                }">
                                    <Icon name="bi:trash3-fill"></Icon>
                                </button>
                            </div>

                        </div>
                    </div>
                </div>

                <div class='v-stack'>
                    <span class='label optional'>
                        {{ $t('EditReceiverInfoComponent.ghi_chu_cho_dia_chi') }} <em>({{ note.length ?? 0 }})</em>
                    </span>
                    <UTextarea autoresize variant="outline" :title="$t('EditReceiverInfoComponent.ghi_chu_cho_dia_chi')"
                        name='address-note' :maxlength="appConst.max_text_long" type="text" :maxrows="5" :rows="3"
                        autoComplete="on" class='input-address'
                        :placeholder="$t('EditReceiverInfoComponent.ghi_chu_cho_dia_chi_placeholder')" v-model="note" />
                </div>
            </div>


            <div class='h-stack edit-customer-info-actions'>
                <button class='reject-button' :disabled=isSavingUserInfo v-on:click="() => {
                    close()

                }">
                    {{ $t('EditReceiverInfoComponent.huy') }}
                </button>
                <button class='accept-button' :disabled="isSavingUserInfo == true
                    || nameErr.length > 0
                    || phoneErr.length > 0
                    || addressSeacrhing
                    " v-on:click="() => {
                        submitInfo()
                    }">
                    {{ $t('EditReceiverInfoComponent.xac_nhan') }}
                </button>
            </div>
        </div>


    </VueFinalModal>

</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal';
import { appConst, appDataStartup, domainImage, formatCurrency, showTranslateProductName, showTranslateProductDescription, validPhone } from "~/assets/AppConst";
import marker_location_icon from "~/assets/image/marker-location.png";
import map_sateline from "~/assets/image/map-satellite.jpg";
import map_streetmap from "~/assets/image/map-streetmap.jpg";
import { PlaceService } from "~/services/placeService/placeService";
import { HttpStatusCode } from 'axios';
import { LMap, LTileLayer, LControlZoom } from '@vue-leaflet/vue-leaflet';
import exifr from "exifr";
import { toast } from 'vue3-toastify';
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue';

var placeService = new PlaceService();

const props = defineProps({
    user_info: null,
    init_data: null,
})
const emit = defineEmits(['close', 'show_select_user', 'submit_user']);
const nuxtApp = useNuxtApp();

var router = useRouter();
var route = useRoute();
const { t, locale } = useI18n();

var user_latitude = useState<any>('user_latitude', () => { return });
var user_longitude = useState<any>('user_longitude', () => { return });

var isEditingCustomerInfo = ref(false);
var latitude = ref<any>(props.init_data.latitude ?? null);
var longitude = ref<any>(props.init_data.longitude ?? null);

var name = ref(props.init_data.name ?? "");
var nameErr = ref("");
var phone = ref(props.init_data.phone ?? "");
var phoneErr = ref("");
var address = ref(props.init_data.address ?? "");
var addressErr = ref("");
var note = ref(props.init_data.note ?? "");
var images = ref<any>(props.init_data.images ?? []);
var province_id = ref(props.init_data.province_id ?? null);
var district_id = ref(props.init_data.district_id ?? null);
var ward_id = ref(props.init_data.ward_id ?? null);

var leafletMap: L.Map;
var tempLeafletMap: L.Map;
var localeMarkerLeaflet: L.Marker;
var markersCluster: any;
var mapType = ref("roadmap");

var leafletMapTileUrl = ref(appConst.leafletMapTileUrl.roadmap);
var mapTypeTitle = ref(t('EditReceiverInfoComponent.ve_tinh_nhan'));
var disabledDragTab = ref(false);
var addressSeacrhing = ref(false);
var buttonMapTileBackgound = ref(map_sateline);

var isSavingUserInfo = ref(false);
var searchAddressTimeout: any;

var changeAddressOnMoving = ref(true);

var coordinatesText = ref("");

var imageFilesName = ref(null as any);
var isFirstLoad = ref(true);
var addressSearchResults = ref([] as any[]);
var showAddressDropdown = ref(false);
var selectedAddressFromDropdown = ref("");

watch(() => [latitude.value, longitude.value], () => {
    coordinatesText.value = `${latitude.value}, ${longitude.value}`
})

onUnmounted(() => {
    nuxtApp.$unsubscribe(appConst.event_key.user_moving)
})

onBeforeMount(async () => {

    nuxtApp.$listen(appConst.event_key.user_moving, (coor: any) => {
        console.log('moving', coor);
        user_latitude.value = coor.latitude;
        user_longitude.value = coor.longitude;
    });
    // let dataSavedUsersTemp = await localStorage.getItem(appConst.storageKey.savedInfo);

    // dataSavedUsers.value = dataSavedUsersTemp ? JSON.parse(dataSavedUsersTemp as string) : [];
});
onMounted(() => {
    console.log(images.value);
    isEditingCustomerInfo.value = true;

})

function close() {
    emit('close')
}
function submitInfo() {
    nameValidation();
    phoneValidation();
    addressValidation();
    if (!nameErr.value?.length && !phoneErr.value?.length && !addressErr.value?.length) {
        emit('submit_user', {
            name: name.value,
            phone: phone.value,
            address: address.value,
            latitude: latitude.value,
            longitude: longitude.value,
            province_id: province_id.value,
            district_id: district_id.value,
            ward_id: ward_id.value,
            note: note.value,
            images: images.value
        })
    }

}
function nameValidation() {
    if (!name.value || !name.value.length) {
        nameErr.value = t('EditReceiverInfoComponent.vui_long_nhap_ten_nguoi_nhan')
    }
    else {
        nameErr.value = '';
    }
}

function phoneValidation() {
    let re = appConst.validateValue.phone;
    if (!validPhone(phone.value) || !validPhone(phone.value).length) {
        phoneErr.value = t('EditReceiverInfoComponent.vui_long_nhap_sdt')
        return;
    }
    if (!re.test(validPhone(phone.value))) {
        phoneErr.value = t('EditReceiverInfoComponent.sdt_khong_dung');
        return;
    }
    else {
        phoneErr.value = '';
    }
}

function addressValidation() {
    if (address.value?.length) {
        addressErr.value = "";
    }
    else addressErr.value = t('EditReceiverInfoComponent.vui_long_nhap_dia_chi');
}

async function initLeafletMap() {
    // markersCluster = new nuxtApp.$L.MarkerClusterGroup({
    // 	maxClusterRadius: 5,
    // 	iconCreateFunction: (cluster) => createClusterElement(cluster),
    // }).addTo(leafletMap);
    console.log("init map")
    await setCurrentLocationLeaflet();
    setTimeout(() => {
        isFirstLoad.value = false;
    }, 1000);
    // (leafletMap as any)["gestureHandling"].enable();
}
async function setCurrentLocationLeaflet() {
    if (latitude.value && longitude.value) {
        console.log("có lat lng");
        leafletMap.setView([latitude.value, longitude.value], 17);
        // setLocationLeafletMarker(latitude.value, longitude.value);
    } else {
        latitude.value = user_latitude.value ?? appConst.defaultCoordinate.latitude;
        longitude.value = user_longitude.value ?? appConst.defaultCoordinate.longitude;
        leafletMap.setView(
            [user_latitude.value ?? appConst.defaultCoordinate.latitude, user_longitude.value ?? appConst.defaultCoordinate.longitude],
            17
        );
    }

}
function getUserAddress() {
    if (changeAddressOnMoving.value && !isFirstLoad.value) {
        clearTimeout(searchAddressTimeout);
        addressSeacrhing.value = true;
        showAddressDropdown.value = false;
        searchAddressTimeout = setTimeout(() => {
            placeService
                .myGeocoderByLatLngToAddress(latitude.value, longitude.value)
                .then((res: any) => {
                    if (res.body.data && res.body.data.length) {
                        // Store all search results
                        addressSearchResults.value = res.body.data;
                        
                        // Set the first result as default
                        const firstResult = res.body.data[0];
                        address.value = firstResult.address ? firstResult.address : "";
                        province_id.value = firstResult.province_id ? firstResult.province_id : null;
                        district_id.value = firstResult.district_id ? firstResult.district_id : null;
                        ward_id.value = firstResult.ward_id ? firstResult.ward_id : null;

                        // Show dropdown if there are multiple results
                        if (res.body.data.length > 1) {
                            showAddressDropdown.value = true;
                            // Set the first result as selected in dropdown
                            selectedAddressFromDropdown.value = firstResult.address;
                        }

                        addressValidation()
                    }
                    addressSeacrhing.value = false;
                });
        }, 500);
    }
}

// Add new function to handle address selection from dropdown
function selectAddressFromDropdown(selectedAddress: string) {
    const selectedResult = addressSearchResults.value.find((item: any) => item.address === selectedAddress);
    if (selectedResult) {
        address.value = selectedResult.address;
        province_id.value = selectedResult.province_id;
        district_id.value = selectedResult.district_id;
        ward_id.value = selectedResult.ward_id;
        addressValidation();
    }
    hideAddressDropdown();
}

// Add new function to hide dropdown
function hideAddressDropdown() {
    setTimeout(() => {
        showAddressDropdown.value = false;
    }, 200);
}

async function gotoCurrentLocationLeaflet(event?: Event) {
    if (!event || event.isTrusted == true) {

        leafletMap.flyTo(
            [user_latitude.value, user_longitude.value],
            17, {
            duration: 1.5
        }
        );
        latitude.value = user_latitude.value;
        longitude.value = user_longitude.value;
        getUserAddress()

    }
}
function jumpToCoordinate() {
    let coorArr = coordinatesText.value?.split(",");
    console.log(coorArr);
    if (coorArr.length == 2) {
        latitude.value = coorArr[0];
        longitude.value = coorArr[1];

        if (leafletMap) {
            leafletMap.flyTo([latitude.value, longitude.value], 17, {
                duration: 1
            })
        }
    }
}

async function fileChangeInput(fileInput: any, childProduct?: any) {
    if (fileInput.target.files.length) {
        if ((fileInput.target.files.length + images.value.length) > 3) {
            toast.error(t('EditReceiverInfoComponent.so_luong_toi_da', { amount: 3 }))
        }
        else {
            imageFilesName.value = fileInput.target.files;
            for (let i = fileInput.target.files.length - 1; i >= 0; i--) {
                if (fileInput.target.files[i].size > appConst.image_size.max) {
                    let imgErr = t('EditReceiverInfoComponent.dung_luong_anh_toi_da', { size: appConst.image_size.max / 1000000 });
                    // toast.error(imgErr);
                }
                else {
                    const reader = new FileReader();
                    reader.onload = async (e: any) => {
                        const image = new Image();
                        image.src = e.target.result;

                        let orientationExif;
                        let imgTemp = {
                            src: "",
                            orientation: 0,

                        };
                        if (fileInput.target.files[i].type != 'image/webp') {
                            orientationExif = await exifr.orientation(image) || 0;
                        }
                        else orientationExif = 0;
                        imgTemp.src = image.src;
                        imgTemp.orientation = orientationExif ? orientationExif : 0;
                        images.value.push(imgTemp);
                    }
                    await reader.readAsDataURL(fileInput.target.files[i]);
                }
            };
        }
    }
}
</script>

<style lang="scss" src="./EditReceiverInfoStyles.scss"></style>