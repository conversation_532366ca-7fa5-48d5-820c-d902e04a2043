.edit-customer-info-container {
  background-color: #f6f5fb;
  gap: 10px;
  border-radius: 10px;
  flex: 1;
  height: 100%;
  position: relative;
  overflow: auto;
 
  & > .edit-customer-info-content{
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
    height: 100%;
    padding: 10px;
    gap: 5px;
  }
  & .current-location-leaflet-btn{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    margin-left: auto;
    color: #3393cb;
    gap: 5px;
    margin-bottom: 0;
    font-weight: 600;

    & > svg{
      color: #3393cb;
      font-size: 20px;
    }
  }

  & .v-stack {
    margin-bottom: 5px;

    & .label {
      color: #2e2d30;
      font-weight: bold;
      font-size: 13px;

      & > em{
        font-size: 13px;
        font-weight: 500;
        color: #707070;
      }
    }

    &>.input-order {
      background-color: white;
      height: 45px;
      padding: 10px 10px 10px 30px;
      border-radius: 5px;
      outline: none;
      color: #2e2d30;
      font-weight: 600;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 100%;
    }

    &>.v-input-order{
      background: white;
      margin: 5px 0;

      & .v-field--focused{
        border: 0 !important;
      }

      & input.v-field__input{
        padding: 5px 10px;
        height: 45px;
        min-height: unset;
      }
    }

    &>.input-address{
      background-color: white;
      border-radius: 5px;
      outline: none;
      color: #545454;
      font-weight: 400;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 100%;
      display: flex;

      & > textarea{
        width: 100%;
        padding: 10px;
        outline: none;
        height: 100% !important;
      }
    }

    &>.change-address-on-moving {
      display: flex;
      gap: 5px;
      align-items: center;
      color: #545454;
      font-weight: 600;
      cursor: pointer;
      font-size: 15px;
      margin-left: auto;
      margin-top: 10px;

      &.active {
        color: #3393cb;
      }

      &>svg {
        font-size: 25px;
      }
    }

    & .coordinate-button {
      font-size: 15px;
      color: var(--primary-color-1);
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 5px;
      font-weight: 600;

      &>svg{
        font-size: 20px;
      }
    }

    & .coordinate-input-container{
      background: white;
      border-radius: 5px;
      color: #545454;
      box-shadow: 0 0 10px rgb(0,0,0, .2);
      height: 45px;
      width: 300px;
      margin-top: 3px;
      margin-right: 5px;
      display: flex;
      align-items: center;
      justify-content: center;

      & > button{
        display: flex;
        align-items: center;
        font-size: 17px;
        color: var(--primary-color-1);
        height: 100%;
        padding: 10px;

        &:hover{
          background: color-mix(in srgb, var(--primary-color-1) 20%, transparent);
        }
      }

      & > input{
        height: 100%;
        padding: 10px;
        flex: 1;
        outline: none;
      }
    }

    &.image {
      display: flex;
      flex-direction: column;
      gap: 5px;
      & > .image-list {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;

        & > .select-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: 3px dashed #9ca3af;
          color: #9ca3af;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          cursor: pointer;

          & > label {
            width: 100%;
            height: 100%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            font-size: 45px;
            & input {
              opacity: 0;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              cursor: pointer;
              z-index: -1;
            }
          }
        }
        & > .selected-image {
          width: calc(25% - 5px);
          aspect-ratio: 1;
          min-width: 100px;
          min-height: 100px;
          border-radius: 10px;
          border: thin solid #9ca3af;
          color: #9ca3af;
          font-size: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          line-height: 1;
          position: relative;

          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: inherit;
          }

          & > .action-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 20px;
            // width: 100%;
            // height: 100%;
            // display: none;
            display: flex;
            color: white;
            justify-content: flex-end;
            align-items: flex-end;
            // padding: 5px;
            gap: 5px;
            background-color: rgb(0, 0, 0, 0.5);
            border-radius: 10px 0;

            & > button {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }

        & > .selected-image:hover {
          & > .action-overlay {
            display: flex;
          }
        }
      }
    }
  }

  & .select-user-saved {
    // position: absolute;
    // top: 0;
    // right: 5px;
    color: var(--primary-color-1);
    font-size: 15px;
    font-weight: 600;
    margin-left: auto;
  }

  &>.save-info-container>label {
    justify-content: center;
    align-items: center;
    display: flex;
    cursor: pointer;

    &>.save-user-info-icon {
      appearance: none;
      width: 1em;
      height: 1em;
      border-radius: 50%;
      margin-right: 5px;
      border: 2px solid var(--primary-color-1);
      outline: none;
      cursor: pointer;
    }

    &>.save-user-info-icon:checked {
      background-color: var(--primary-color-1);
      box-shadow: inset 0px 0px 0 3px white;
    }
  }

  & .map-container {
    width: 100%;
    flex: 1;
    min-height: 350px;
    height: 350px;
    border-radius: 0;
    margin-top: 5px;

    & .leaflet-container {
      border-radius: 0;
      height: 100%;
      min-height: inherit !important;

      & .marker-location {
        width: 30px;
        height: 40px;
        position: absolute;
        bottom: 50%;
        left: 50%;
        object-fit: contain;
        transform: translateX(-50%);
        z-index: 450;

        &>img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }

  &>.edit-customer-info-actions {
    width: 100%;
    padding: 5px;
    gap: 5px;
    justify-content: space-evenly;

    & .accept-button:disabled {
      color: white;
      border: thin solid var(--primary-color-1);
      background-color: var(--primary-color-1);
      opacity: 0.5;
    }

    & .reject-button:disabled {
      color: var(--primary-color-1);
      border: thin solid var(--primary-color-1);
      background-color: white;
      opacity: 0.5;
    }
  }

  @keyframes slide-up {
    0% {
      bottom: -50%;
    }

    100% {
      bottom: 0;
    }
  }

  & .list-saved-user-container {
    width: 100%;
    height: 60%;
    background-color: white;
    position: absolute;
    bottom: 0;
    left: unset;
    display: flex;
    flex-direction: column;
    text-align: center;
    font-size: 1rem;
    border-radius: 10px 10px 0 0;
    padding: 10px 0 20px;
    animation: slide-up 0.5s ease;
    align-items: center;

    &>.close {
      position: absolute;
      top: 10px;
      right: 5px;
      cursor: pointer;
      text-align: left;
    }

    &>.title {
      font-weight: bold;
      font-size: 20px;
      border-bottom: thin solid #bcbcbc;
      width: 100%;
      padding-bottom: 5px;
    }

    &>.saved-user-list {
      display: flex;
      flex-direction: column;
      gap: 5px;
      width: 100%;
      overflow: auto;

      &>.item-saved-user {
        display: flex;
        flex-direction: column;
        text-align: left;
        padding: 5px 10px;
        border-bottom: thin solid #bcbcbc;
        cursor: pointer;

        &>.name-phone {
          display: flex;
          justify-content: space-between;
          color: #36353b;
          font-size: 20px;
          font-weight: 600;
          align-items: center;

          &>.phone {
            padding-left: 10px;
            border-left: thin solid #d5d5d5;
          }
        }

        &>.address {
          color: #707070;

          &>.address-type {
            background: #ddd;
            color: #2e2d30;
            padding: 0px 10px;
            border-radius: 5px;
            font-weight: 600;
            margin-right: 5px;
          }

          &>.address-type.home {
            background: #eafff5;
            color: #00ad53;
          }

          &>.address-type.work {
            background: #ebf3ff;
            color: #0a68fe;
          }
        }

        &>.selected {
          display: flex;
          color: #f05976;
          gap: 5px;
        }
      }
    }

    &>.none-user-list {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px;
      height: 100%;
      overflow: hidden;

      &>img {
        height: 100%;
        width: 80%;
        object-fit: contain;
      }

      &>span {
        font-weight: 600;
        font-size: 20px;
        color: #828187;
      }
    }
  }
}

.edit-customer-info-modal {
  width: 500px !important;
  max-width: 95% !important;
  height: 95%;
  // height: 800px !important;
  max-height: 95% !important;
  padding: 0 !important;
}