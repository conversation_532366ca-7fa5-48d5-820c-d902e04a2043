.stock-management-dashboard {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 95%;
  max-width: 900px;

  // Dashboard Header
  .dashboard-header {
    margin-bottom: 20px;
    text-align: center;

    .dashboard-title {
      font-size: 24px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .dashboard-subtitle {
      font-size: 14px;
      color: #7f8c8d;
      margin: 0;
    }
  }

  // Summary Cards
  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 25px;

    .summary-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      padding: 15px;
      color: white;
      display: flex;
      align-items: center;
      gap: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      &.low-stock {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      }

      .card-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .card-content {
        flex: 1;

        h3 {
          font-size: 18px;
          font-weight: 700;
          margin: 0 0 4px 0;
        }

        p {
          font-size: 12px;
          margin: 0;
          opacity: 0.9;
        }
      }
    }
  }

  // Action Cards Grid
  .action-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 25px;

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }

    .action-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      min-height: 140px;
      position: relative;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      .card-icon-wrapper {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;

        &.import {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
        }

        &.export {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          color: white;
        }

        &.waste {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          color: white;
        }

        &.history {
          background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          color: #2c3e50;
        }

        &.reports {
          background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          color: #2c3e50;
        }

        &.current {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }
      }

      .card-content {
        flex: 1;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          margin: 0 0 6px 0;
        }

        p {
          font-size: 12px;
          color: #7f8c8d;
          margin: 0;
          line-height: 1.4;
        }
      }

      .card-arrow {
        position: absolute;
        top: 15px;
        right: 15px;
        color: #bdc3c7;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover .card-arrow {
        opacity: 1;
      }
    }
  }

  // Recent Activities
  .recent-activities {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 15px 0;
    }

    .activity-list {
      .activity-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid #ecf0f1;

        &:last-child {
          border-bottom: none;
        }

        .activity-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          &.import {
            background: rgba(79, 172, 254, 0.1);
            color: #4facfe;
          }

          &.export {
            background: rgba(67, 233, 123, 0.1);
            color: #43e97b;
          }

          &.waste {
            background: rgba(250, 112, 154, 0.1);
            color: #fa709a;
          }
        }

        .activity-content {
          flex: 1;

          .activity-title {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin: 0 0 4px 0;
          }

          .activity-time {
            font-size: 12px;
            color: #7f8c8d;
            margin: 0;
          }
        }

        .activity-value {
          .value-positive {
            color: #27ae60;
            font-weight: 600;
          }

          .value-negative {
            color: #e74c3c;
            font-weight: 600;
          }
        }
      }
    }
  }

  // Loading State
  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #7f8c8d;

    .loading-icon {
      animation: spin 1s linear infinite;
      margin-bottom: 10px;
    }

    p {
      font-size: 14px;
      margin: 0;
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Mobile optimizations
@media (max-width: 480px) {
  .stock-management-dashboard {
    padding: 10px;
    width: 100%;


    .action-cards-grid {
      // grid-template-columns: 1fr;
      gap: 12px;

      .action-card {
        padding: 15px;
        min-height: 120px;

        .card-icon-wrapper {
          width: 50px;
          height: 50px;
        }

        .card-content h3 {
          font-size: 14px;
        }

        .card-content p {
          font-size: 11px;
        }
      }
    }

    .summary-cards {
      grid-template-columns: 1fr;
      gap: 10px;

      .summary-card {
        padding: 12px;

        .card-content h3 {
          font-size: 16px;
        }

        .card-content p {
          font-size: 11px;
        }
      }
    }
  }
}

// Touch-friendly targets
@media (hover: none) and (pointer: coarse) {
  .action-card {
    min-height: 44px !important;
    
    &:hover {
      transform: none;
    }

    &:active {
      transform: scale(0.98);
      background-color: #f8f9fa;
    }
  }
}
