<template>
    <BatchImportComponent :mode="'agent'" v-if="is_authorized == true"></BatchImportComponent>
    <UnauthorizedComponent v-else-if="is_authorized == false"></UnauthorizedComponent>
</template>

<script lang="ts" setup>
import { appConst } from '~/assets/AppConst';
import BatchImportComponent from '~/components/myShop/batchImport/BatchImportComponent.vue';
import { AuthService } from '~/services/authService/authService';

const nuxtApp = useNuxtApp();
const route = useRoute();
var is_authorized = ref(null as any);

onBeforeMount(()=>{
    nuxtApp.$emit(appConst.event_key.show_footer, false)
})

onMounted(async () => {
    let authService = new AuthService();
    let profile = await authService.checkAuth();
    if (profile?.role_id == appConst.role_id.agent) {
        is_authorized.value = true;
    }
    else {
        is_authorized.value = false;
    }
})
</script>
