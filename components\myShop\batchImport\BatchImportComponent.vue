<template>
    <div class="batch-import-container">
        <div class="header">
            <button class="back-button" @click="goBack">
                <Icon name="mdi:arrow-left" size="24" />
            </button>
            <h1 class="title">{{ $t('BatchImportComponent.tieu_de') }}</h1>
        </div>

        <!-- Step 1: Text Input -->
        <div v-if="currentStep === 1" class="step-container">
            <div class="step-header">
                <h2>{{ $t('BatchImportComponent.buoc_1') }}</h2>
            </div>
            
            <div class="form-container">
                <label class="form-label">{{ $t('BatchImportComponent.nhap_van_ban') }}</label>
                <div class="text-input-container">
                    <textarea
                        v-model="inputText"
                        class="text-input"
                        :placeholder="$t('BatchImportComponent.placeholder_van_ban')"
                        rows="10"
                        :disabled="isLoading"
                    ></textarea>
                    <button
                        class="speech-button"
                        :class="{ recording: isRecording }"
                        @click="toggleSpeechRecognition"
                        :disabled="isLoading"
                        :title="isRecording ? 'Stop recording' : 'Start speech to text'"
                    >
                        <Icon name="mdi:microphone" size="20" v-if="!isRecording" />
                        <Icon name="mdi:stop" size="20" v-else />
                    </button>
                </div>

                <div class="form-actions">
                    <button
                        class="parse-button"
                        @click="parseText"
                        :disabled="!inputText.trim() || isLoading"
                    >
                        <Icon name="eos-icons:loading" size="20" v-if="isLoading" />
                        <span>{{ isLoading ? $t('BatchImportComponent.dang_phan_tich') : $t('BatchImportComponent.phan_tich') }}</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: Review and Confirm -->
        <div v-if="currentStep === 2" class="step-container">
            <div class="step-header">
                <h2>{{ $t('BatchImportComponent.buoc_2') }}</h2>
                <p class="results-count">{{ $t('BatchImportComponent.ket_qua_phan_tich') }}: {{ parsedProducts.length }} {{ $t('BatchImportComponent.san_pham_goc') }}</p>
            </div>

            <!-- Desktop Grid View -->
            <div class="products-grid-container">
                <div class="grid-header">
                    <div class="grid-col-header">{{ $t('BatchImportComponent.hinh_anh') }}</div>
                    <div class="grid-col-header">{{ $t('BatchImportComponent.ten_san_pham') }}</div>
                    <div class="grid-col-header">{{ $t('BatchImportComponent.don_vi') }}</div>
                    <div class="grid-col-header">{{ $t('BatchImportComponent.gia_ban') }}</div>
                    <div class="grid-col-header">{{ $t('BatchImportComponent.nhap_kho') }}</div>
                    <div class="grid-col-header">{{ $t('BatchImportComponent.san_pham_de_xuat') }}</div>
                </div>

                <div class="products-grid">
                    <div v-for="(product, index) in parsedProducts" :key="index" class="product-grid-row">
                        <!-- Image Column -->
                        <div class="grid-cell image-cell">
                            <div class="product-image-upload">
                                <img
                                    v-if="product.imageUrl"
                                    :src="product.imageUrl"
                                    :alt="product.name"
                                    class="uploaded-image"
                                />
                                <div v-else class="image-placeholder">
                                    <Icon name="mdi:image-outline" size="24" />
                                </div>
                                <div class="image-actions">
                                    <label class="image-btn upload-btn">
                                        <Icon name="ion:plus-round" size="16" />
                                        <input
                                            type="file"
                                            accept="image/*"
                                            @change="handleImageUpload(product, $event)"
                                            style="display: none"
                                            :disabled="isSubmitting"
                                        />
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Product Name Column -->
                        <div class="grid-cell name-cell">
                            <div class="name-input-container">
                                <input
                                    v-model="product.name"
                                    type="text"
                                    class="grid-input name-input"
                                    :disabled="isSubmitting"
                                    :placeholder="$t('BatchImportComponent.ten_san_pham')"
                                />
                                <div class="original-name" v-if="product.originalText">
                                    {{ product.originalText }}
                                </div>
                            </div>
                        </div>

                        <!-- Unit Column -->
                        <div class="grid-cell unit-cell">
                            <select
                                v-model="product.unit"
                                class="grid-select unit-select"
                                :disabled="isSubmitting"
                                @change="onUnitChange(product, $event)"
                            >
                                <option v-for="unit in commonUnits" :key="unit" :value="unit">{{ unit }}</option>
                                <option value="custom">{{ $t('BatchImportComponent.don_vi_khac') }}</option>
                            </select>
                            <input
                                v-if="product.unit === 'custom'"
                                v-model="product.customUnit"
                                type="text"
                                class="grid-input custom-unit-input"
                                :placeholder="$t('BatchImportComponent.nhap_don_vi')"
                                :disabled="isSubmitting"
                                @blur="updateCustomUnit(product)"
                            />
                        </div>

                        <!-- Sale Price Column -->
                        <div class="grid-cell price-cell">
                            <div class="price-input-group">
                                <input
                                    v-model="product.priceDisplay"
                                    type="text"
                                    class="grid-input price-input"
                                    :disabled="isSubmitting"
                                    @input="updatePrice(product, $event)"
                                    @blur="formatPriceDisplay(product)"
                                />
                                <span class="currency-suffix">VNĐ</span>
                            </div>
                        </div>

                        <!-- Inventory Import Column -->
                        <div class="grid-cell inventory-cell">
                            <div class="inventory-toggle-container">
                                <label class="toggle-switch">
                                    <input
                                        type="checkbox"
                                        v-model="product.enableInventory"
                                        :disabled="isSubmitting"
                                    />
                                    <span class="toggle-slider"></span>
                                </label>
                                <div v-if="product.enableInventory" class="inventory-inputs">
                                    <div class="quantity-input-container">
                                        <button
                                            class="quantity-btn decrease"
                                            @click="decreaseQuantity(product)"
                                            :disabled="isSubmitting || product.quantity <= 1"
                                        >
                                            <Icon name="mdi:minus" size="14" />
                                        </button>
                                        <input
                                            v-model.number="product.quantity"
                                            type="number"
                                            class="quantity-input"
                                            min="1"
                                            :disabled="isSubmitting"
                                        />
                                        <button
                                            class="quantity-btn increase"
                                            @click="increaseQuantity(product)"
                                            :disabled="isSubmitting"
                                        >
                                            <Icon name="mdi:plus" size="14" />
                                        </button>
                                    </div>
                                    <div class="import-price-container">
                                        <input
                                            v-model="product.importPriceDisplay"
                                            type="text"
                                            class="grid-input import-price-input"
                                            :placeholder="$t('BatchImportComponent.gia_nhap')"
                                            :disabled="isSubmitting"
                                            @input="updateImportPrice(product, $event)"
                                            @blur="formatImportPriceDisplay(product)"
                                        />
                                        <span class="currency-suffix">VNĐ</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Suggested Products Column -->
                        <div class="grid-cell suggested-cell">
                            <div v-if="product.suggestedProducts && product.suggestedProducts.length > 0" class="suggested-products-compact">
                                <div class="suggested-dropdown">
                                    <select
                                        v-model="product.selectedProductId"
                                        class="grid-select suggested-select"
                                        :disabled="isSubmitting"
                                        @change="onSuggestedProductChange(product)"
                                    >
                                        <option value="">{{ $t('BatchImportComponent.tao_moi') }}</option>
                                        <option
                                            v-for="suggested in product.suggestedProducts"
                                            :key="suggested.id"
                                            :value="suggested.id"
                                        >
                                            {{ suggested.name }}
                                        </option>
                                    </select>
                                </div>
                                <div v-if="product.selectedProductId" class="suggested-preview">
                                    <img
                                        :src="getSelectedProduct(product)?.profile_picture || '/default-product.png'"
                                        :alt="getSelectedProduct(product)?.name"
                                        class="suggested-image"
                                        @error="handleImageError"
                                    />
                                </div>
                            </div>
                            <div v-else class="no-suggestions-compact">
                                <Icon name="mdi:plus-circle" size="20" />
                                <span>{{ $t('BatchImportComponent.tao_moi') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
        </div>

            <div class="form-actions">
                <button class="back-step-button" @click="goBackToStep1" :disabled="isSubmitting">
                    {{ $t('BatchImportComponent.quay_lai') }}
                </button>
                <button 
                    class="submit-button"
                    @click="submitBatch"
                    :disabled="isSubmitting"
                >
                    <Icon name="eos-icons:loading" size="20" v-if="isSubmitting" />
                    <span>{{ isSubmitting ? $t('BatchImportComponent.dang_nhap') : $t('BatchImportComponent.xac_nhan_nhap') }}</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { toast } from 'vue3-toastify';
import { appConst, formatCurrency } from '~/assets/AppConst';
import { appRoute } from '~/assets/appRoute';
import { ProductService } from '~/services/productService/productService';
import { ShopService } from '~/services/shopService/shopService';
import { AgentService } from '~/services/agentService/agentService';
import { HttpStatusCode } from 'axios';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

var props = defineProps({
    shopData: {},
    mode: null
});

useSeoMeta({
    title: t('AppRouteTitle.BatchImportComponent')
});

// Services
var productService = new ProductService();
var shopService = new ShopService();
var agentService = new AgentService();

// State
var currentStep = ref(1);
var inputText = ref('');
var isLoading = ref(false);
var isSubmitting = ref(false);
var isRecording = ref(false);
var parsedProducts = ref([] as any[]);
var shopData = ref(props.shopData || null as any);
var recognition = ref(null as any);

// Common units for Vietnamese products
var commonUnits = ref([
    'cái', 'chiếc', 'con', 'cuốn', 'gói', 'hộp', 'chai', 'lọ', 'túi', 'thùng',
    'kg', 'gram', 'lít', 'ml', 'mét', 'cm', 'tấn', 'tá', 'chục', 'trăm'
]);

onMounted(async () => {
    if (!shopData.value?.id) {
        if (props.mode != 'agent') {
            await getMyShop();
        } else {
            await getShopDetailByAgent();
        }
    }

    // Initialize speech recognition
    if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
        recognition.value = new (window as any).webkitSpeechRecognition();
        recognition.value.continuous = true;
        recognition.value.interimResults = true;
        recognition.value.lang = 'vi-VN';

        recognition.value.onresult = (event: any) => {
            let finalTranscript = '';
            for (let i = event.resultIndex; i < event.results.length; i++) {
                if (event.results[i].isFinal) {
                    finalTranscript += event.results[i][0].transcript;
                }
            }
            if (finalTranscript) {
                inputText.value += (inputText.value ? '\n' : '') + finalTranscript;
            }
        };

        recognition.value.onend = () => {
            isRecording.value = false;
        };

        recognition.value.onerror = () => {
            isRecording.value = false;
        };
    }
});

function goBack() {
    router.back();
}

function goBackToStep1() {
    currentStep.value = 1;
    parsedProducts.value = [];
}

function toggleProductEdit(index: number) {
    parsedProducts.value[index].isEditing = !parsedProducts.value[index].isEditing;
}

function toggleSpeechRecognition() {
    if (!recognition.value) {
        toast.error('Speech recognition not supported in this browser');
        return;
    }

    if (isRecording.value) {
        recognition.value.stop();
        isRecording.value = false;
    } else {
        recognition.value.start();
        isRecording.value = true;
    }
}

function increaseQuantity(product: any) {
    product.quantity = (product.quantity || 0) + 1;
}

function decreaseQuantity(product: any) {
    if (product.quantity > 1) {
        product.quantity = product.quantity - 1;
    }
}

function selectSuggestedProduct(product: any, suggested: any) {
    product.selectedProductId = suggested.id;
    product.name = suggested.name;
}

function selectCreateNew(product: any) {
    product.selectedProductId = '';
    // Keep the original parsed name when creating new
}

function handleImageError(event: any) {
    event.target.src = '/default-product.png';
}

function handleImageUpload(product: any, event: any) {
    console.log('Handle image upload called', event);
    const file = event.target.files[0];
    console.log('Selected file:', file);
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            console.log('File read successfully');
            product.imageUrl = e.target?.result as string;
            console.log('Image URL set:', product.imageUrl?.substring(0, 50) + '...');
        };
        reader.readAsDataURL(file);
    }
}



function updateImportPrice(product: any, event: any) {
    const value = event.target.value;
    const numericValue = value.replace(/[^\d.,]/g, '');
    const price = parseFloat(numericValue.replace(/,/g, '.')) || 0;
    product.importPrice = price;
    product.importPriceDisplay = numericValue;
}

function formatImportPriceDisplay(product: any) {
    if (product.importPrice) {
        product.importPriceDisplay = product.importPrice.toLocaleString('vi-VN');
    }
}

function getSelectedProduct(product: any) {
    if (product.selectedProductId && product.suggestedProducts) {
        return product.suggestedProducts.find((p: any) => p.id === product.selectedProductId);
    }
    return null;
}

function onSuggestedProductChange(product: any) {
    if (product.selectedProductId) {
        const selectedProduct = getSelectedProduct(product);
        if (selectedProduct) {
            product.name = selectedProduct.name;
        }
    }
}

function onUnitChange(product: any, event: any) {
    const selectedUnit = event.target.value;
    if (selectedUnit === 'custom') {
        product.customUnit = '';
    } else {
        product.customUnit = '';
    }
}

function updateCustomUnit(product: any) {
    if (product.customUnit && product.customUnit.trim()) {
        product.unit = product.customUnit.trim();
    } else {
        product.unit = commonUnits.value[0]; // Default to first unit
    }
}

function updatePrice(product: any, event: any) {
    const value = event.target.value;
    // Remove all non-numeric characters except dots and commas
    const numericValue = value.replace(/[^\d.,]/g, '');
    // Convert to number (handle both comma and dot as decimal separator)
    const price = parseFloat(numericValue.replace(/,/g, '.')) || 0;
    product.price = price;
    product.priceDisplay = numericValue;
}

function formatPriceDisplay(product: any) {
    if (product.price) {
        // Format with thousand separators
        product.priceDisplay = product.price.toLocaleString('vi-VN');
    }
}



async function getMyShop() {
    try {
        const res = await shopService.myShop();
        if (res.status === HttpStatusCode.Ok && res?.body?.data) {
            shopData.value = res.body.data;
        } else {
            toast.error('Failed to load shop data');
            router.back();
        }
    } catch (error) {
        toast.error('Failed to load shop data');
        router.back();
    }
}

async function getShopDetailByAgent() {
    try {
        const res = await agentService.agentShopDetail(route.params.id as any);
        if (res.status === HttpStatusCode.Ok && res?.body?.data) {
            shopData.value = res.body.data;
        } else {
            toast.error('Failed to load shop data');
            router.back();
        }
    } catch (error) {
        toast.error('Failed to load shop data');
        router.back();
    }
}

async function parseText() {
    if (!inputText.value.trim()) {
        toast.error(t('BatchImportComponent.vui_long_nhap_van_ban'));
        return;
    }

    isLoading.value = true;
    try {
        const res = await productService.parseBatch(inputText.value);
        if (res.status === 200 && res.body?.success) {
            parsedProducts.value = res.body.data.map((product: any) => {
                // Set default name from suggested product if exists
                let defaultName = product.name;
                let defaultSelectedId = product.selectedProductId;

                if (product.suggestedProducts && product.suggestedProducts.length > 0) {
                    const firstSuggested = product.suggestedProducts[0];
                    defaultName = firstSuggested.name;
                    defaultSelectedId = firstSuggested.id;
                }

                const processedProduct = {
                    ...product,
                    name: defaultName || product.name || 'Sản phẩm mới',
                    originalText: product.description || product.name || '',
                    selectedProductId: defaultSelectedId,
                    isEditing: false,
                    priceDisplay: product.price ? product.price.toLocaleString('vi-VN') : '',
                    customUnit: '',
                    quantity: 1, // Default quantity for batch import
                    enableInventory: false, // Default inventory toggle off
                    importPrice: 0,
                    importPriceDisplay: '',
                    imageUrl: ''
                };

                console.log('Processed product:', processedProduct);
                return processedProduct;
            });
            currentStep.value = 2;
        } else {
            toast.error(t('BatchImportComponent.loi_phan_tich'));
        }
    } catch (error) {
        toast.error(t('BatchImportComponent.loi_phan_tich'));
    } finally {
        isLoading.value = false;
    }
}

async function submitBatch() {
    if (!shopData.value?.id) {
        toast.error('Shop data not available');
        return;
    }

    isSubmitting.value = true;
    try {
        // Prepare products for batch create
        const products = parsedProducts.value.map(product => ({
            product_id: product.selectedProductId || '',
            name: product.name,
            quantity: product.quantity,
            unit: product.unit,
            price: product.price
        }));

        const res = await productService.batchCreate(shopData.value.id, products);
        if (res.status === 200 && res.body?.success) {
            toast.success(t('BatchImportComponent.nhap_thanh_cong', { count: res.body.data.imported_count }));
            router.push({ path: appRoute.MyShopManageComponent });
        } else {
            toast.error(t('BatchImportComponent.nhap_that_bai'));
        }
    } catch (error) {
        toast.error(t('BatchImportComponent.nhap_that_bai'));
    } finally {
        isSubmitting.value = false;
    }
}
</script>

<style scoped src="./BatchImportComponent.css"></style>


