.create-private-product-container {
  width: 100%;
  height: 100%;
  flex: 1 1;
  max-width: var(--max-width-view);
  // max-height: 90vh;
  background: white;
  margin: auto;
  display: flex;
  flex-direction: column;
  font-size: 1.2em;
  overflow: visible;
  position: relative;

  // &>.title-header {
  //     // font-size: 1.3em;

  //     margin: 0;
  //     text-align: center;
  //     border-bottom: thin solid #ccc;
  // }

  & > .product-content-container {
    display: flex;
    flex: 1;
    // overflow: hidden auto;
    flex-direction: column;
    background: #f1f2f6;
    padding: 15px;

    & .product-image-list {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
      justify-content: center;
      background: white;
      padding: 10px;
      margin: 0;
      border-radius: 10px;
      border: 3px dashed #8e97a587;

      & > .select-image {
        width: calc(25% - 5px);
        aspect-ratio: 1;
        min-width: 100px;
        min-height: 100px;
        border-radius: 10px;
        border: 2px solid #8e97a5;
        color: #8e97a5;
        font-size: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        cursor: pointer;

        & > label {
          width: 100%;
          height: 100%;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: -1;
          }
        }
      }
      & .selected-image {
        width: calc(25% - 5px);
        aspect-ratio: 1;
        min-width: 100px;
        min-height: 100px;
        border-radius: 10px;
        border: thin solid #e7e9ec;
        color: #e7e9ec;
        font-size: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        position: relative;
        cursor: grab;

        & > img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: inherit;
        }

        & > .action-overlay {
          position: absolute;
          bottom: 0;
          right: 0;
          font-size: 20px;
          // width: 100%;
          // height: 100%;
          // display: none;
          display: flex;
          color: white;
          justify-content: flex-end;
          align-items: flex-end;
          // padding: 5px;
          gap: 5px;
          background-color: rgb(0, 0, 0, 0.5);
          border-radius: 10px 0;

          & > button {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
      & .selected-image.sortable-chosen {
        cursor: grabbing;
      }
      & .drag-drop-container {
        width: 100%;
      }
      & .selected-image:hover {
        & > .action-overlay {
          display: flex;
        }
      }
    }

    & .product-avt-container {
      position: relative;
      display: flex;
      justify-content: center;
      width: 150px;
      align-items: center;
      margin: 10px auto;

      & > img {
        border-radius: 10px;
        flex: 1 1;
        height: 150px;
        width: 150px;
        object-fit: cover;
        // background-color: var(--color-background-2);
      }
    }
    & .product-avatar-actions {
      display: flex;
      gap: 15px;
      justify-content: center;

      & .select-image {
        // position: absolute;
        // top: 10px;
        // right: 10px;
        width: 25%;
        min-width: fit-content;
        white-space: nowrap;
        cursor: pointer;

        & > div {
          position: relative;
          width: 100%;
          font-size: 1.2em;
          background: white;
          color: #343434;
          height: 100%;
          display: flex;
          justify-content: center;
          font-weight: bold;
          border-radius: 7px;
          border: thin solid #e7e9ec;
          padding: 5px 15px;
          cursor: pointer;
          font-size: 15px;

          & input {
            opacity: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            z-index: 2;
          }

          & input::file-selector-button {
            cursor: pointer;
          }
        }
      }
    }
    & .language-options {
      display: flex;
      gap: 5px;
      font-size: 15px;
      justify-content: center;
      width: 100%;
      padding: 10px 0;
      white-space: nowrap;
      flex-wrap: wrap;

      & > .lang-button {
        background-color: #dbdbdb;
        color: #202020;
        padding: 0 10px;
        border-radius: 2em;
      }
      & > .lang-button.active {
        background-color: #202020;
        color: white;
        font-weight: 600;
      }
    }
    & > .product-content {
      margin: 10px 0;
      font-size: 15px;
      color: #2b2b2b;
      font-weight: 600;

      & > .content-header {
        display: flex;
        gap: 5px 10px;
        flex-wrap: wrap;
        justify-content: space-between;

        & > .add-category {
          display: flex;
          gap: 5px;
          justify-content: center;
          align-items: center;
          color: var(--primary-color-1);
        }
      }

      & > .checkbox-input-label {
        gap: 5px;
        display: flex;
        cursor: pointer;
        user-select: none;
        font-weight: 500;
        color: var(--primary-color-1);
        font-size: 20px;
        min-height: 25px;

        & span {
          font-size: 17px;
          color: var(--primary-color-1);
        }

        & em {
          color: var(--primary-color-1);
          font-size: 14px;
          line-height: normal;
          align-self: center;
        }
      }

      & .label {
        color: #262a3c;
        font-size: 15px;
      }

      &  .input-custom,
      > .text-area-custom {
        width: 80%;
        background: white;
        border-radius: 7px;
        margin: 5px 0;
        padding: 10px;
        font-size: 15px;
        font-weight: 600;
        border: 1px solid rgb(231, 233, 236);
        transition: all 0.2s ease;

        &:focus {
          border-color: var(--primary-color-1);
          box-shadow: 0 0 0 3px rgba(var(--primary-color-1), 0.1);
          outline: none;
        }

        &:hover {
          border-color: #c0c4c7;
        }
      }

      & > .text-area-custom {
        height: 120px;
        min-height: 80px;
        max-height: 300px;
        resize: vertical;
        font-family: inherit;
        line-height: 1.5;
        font-weight: 500;

        // Mobile styles
        @media (max-width: 767px) {
          height: 100px;
          min-height: 80px;
          font-size: 14px;
          padding: 12px;
        }

        // Tablet and desktop
        @media (min-width: 768px) {
          height: 140px;
          min-height: 100px;
          padding: 14px 16px;
          font-size: 15px;
        }

        // Placeholder styling
        &::placeholder {
          color: #9aa0a6;
          font-style: italic;
          opacity: 1;
        }

        // Scrollbar styling for webkit browsers
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f3f4;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #dadce0;
          border-radius: 3px;

          &:hover {
            background: #bdc1c6;
          }
        }
      }
      & > .dropdown-select-container {
        background: white;
        border-radius: 7px;
        margin-top: 5px;
        padding: 10px;
        border: none;
        font-size: 17px;

        & button {
          padding: 0;
        }
      }
      & em {
        color: var(--primary-color-1);
      }

      & .price-text.notice {
        color: #00823e;
      }
    }

    & > .product-notice {
      font-style: italic;
      color: var(--color-text-note);
      display: flex;
      align-items: flex-start;
      gap: 5px;
      font-size: 0.85em;

      & > em {
        color: var(--primary-color-2);
      }
    }
  }

  & .price-input {
    border: thin solid var(--color-text-note);
    border-width: 0 0 1px 0;
    font-weight: 600;
    margin: 0 5px 10px;
    outline: none;
  }

  & .form-actions {
    gap: 15px;
    justify-content: space-evenly;
    margin-top: auto;

    & > button {
      border-radius: 5px;
      flex: 1;
      padding: 5px 20px;
      border: none;
      font-size: 17px;
      font-weight: bold;
    }

    & > .cancel-button {
      color: #343434;
      border: thin solid #e2e5eb;
      background: #e2e5eb;
    }

    & > .save-button {
      color: white;
      border: thin solid #00823e;
      background: #00823e;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  & .dropdown-select-container.category-select .group {
    width: 95%;
    max-height: 500% !important;
    overflow: auto;
    box-shadow: 0 0 15px #ccc;
    border-radius: 10px;
    opacity: 1;
    background: white;
    & ul {
      border-radius: 0;
    }
    & .search-category-empty {
      color: var(--color-text-note);
    }
  }

  // Advanced toggle section - Mobile First
  & .advanced-toggle-section {
    margin: 20px 0 15px 0;
    display: flex;
    justify-content: center;
    padding: 0 10px;

    & .toggle-advanced-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px 24px;
      width: 100%;
      max-width: 280px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: 1px solid #dee2e6;
      border-radius: 12px;
      color: #495057;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
      }

      &:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        border-color: #ced4da;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      // Icon styling
      .icon {
        font-size: 16px;
        transition: transform 0.3s ease;
      }

      // Desktop styles
      @media (min-width: 768px) {
        width: auto;
        min-width: 240px;
        padding: 10px 20px;
        font-size: 15px;
      }
    }
  }

  // Advanced info section - Mobile First
  & .advanced-info-section {
    background: #fafbfc;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin: 10px 0 20px 0;
    padding: 20px 15px;
    position: relative;
    overflow: hidden;

    // Mobile animation
    animation: slideDownMobile 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    @keyframes slideDownMobile {
      from {
        opacity: 0;
        transform: translateY(-20px);
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
      }
      to {
        opacity: 1;
        transform: translateY(0);
        max-height: 2000px;
        padding-top: 20px;
        padding-bottom: 20px;
      }
    }

    // Decorative elements
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--primary-color-1), var(--primary-color-2));
      border-radius: 12px 12px 0 0;
    }

    &::after {
      content: '';
      position: absolute;
      top: 10px;
      right: 15px;
      width: 6px;
      height: 6px;
      background: var(--primary-color-1);
      border-radius: 50%;
      opacity: 0.3;
    }

    // Enhanced product content styling within advanced section
    & .product-content {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin: 10px 0;
      border: 1px solid #f1f3f4;
      transition: all 0.2s ease;

      &:hover {
        border-color: #e8eaed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }

      // Label styling
      & .label {
        color: #3c4043;
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
        font-size: 14px;
      }

      // Input styling
      & .input-custom, & .text-area-custom {
        border: 1px solid #dadce0;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: #fafbfc;
        font-family: inherit;

        &:focus {
          border-color: var(--primary-color-1);
          background: white;
          box-shadow: 0 0 0 3px rgba(var(--primary-color-1), 0.1);
          outline: none;
          transform: translateY(-1px);
        }

        &:hover {
          border-color: #bdc1c6;
          background: white;
        }

        &::placeholder {
          color: #9aa0a6;
          font-style: italic;
          opacity: 1;
        }
      }

      // Enhanced textarea styling for advanced section
      & .text-area-custom {
        border: 1px solid #dadce0;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: #fafbfc;
        font-family: inherit;

        // Mobile specific
        @media (max-width: 767px) {
          min-height: 80px;
          height: 100px;
          font-size: 13px;
          padding: 10px;
          line-height: 1.5;
        }

        // Tablet and up
        @media (min-width: 768px) {
          min-height: 120px;
          height: 140px;
          padding: 14px 16px;
          font-size: 15px;
        }

        // Custom scrollbar
        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-track {
          background: #f8f9fa;
          border-radius: 4px;
          margin: 4px 0;
        }

        &::-webkit-scrollbar-thumb {
          background: linear-gradient(180deg, #dadce0 0%, #bdc1c6 100%);
          border-radius: 4px;
          border: 1px solid #e8eaed;

          &:hover {
            background: linear-gradient(180deg, #bdc1c6 0%, #9aa0a6 100%);
          }

          &:active {
            background: #9aa0a6;
          }
        }

        // Character counter styling (if needed)
        & + .character-count {
          font-size: 12px;
          color: #5f6368;
          text-align: right;
          margin-top: 4px;

          &.warning {
            color: #ea4335;
          }
        }
      }

      // Select styling
      & select.input-custom {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px;
        padding-right: 40px;
        appearance: none;
      }
    }

    // Desktop styles
    @media (min-width: 768px) {
      padding: 25px 20px;
      margin: 15px 0 25px 0;

      // Desktop animation
      animation: slideDownDesktop 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      @keyframes slideDownDesktop {
        from {
          opacity: 0;
          transform: translateY(-15px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      & .product-content {
        padding: 18px 20px;
        margin: 12px 0;

        & .label {
          font-size: 15px;
          margin-bottom: 10px;
        }

        & .input-custom, & .text-area-custom {
          padding: 14px 16px;
          font-size: 15px;
        }
      }
    }

    // Large desktop styles
    @media (min-width: 1024px) {
      padding: 30px 25px;

      & .product-content {
        padding: 20px 25px;
        margin: 15px 0;
      }
    }
  }

  // Inventory Toggle Section - Mini Toggle Button
  & .inventory-toggle-section {
    margin: 15px 0 10px 0;
    display: flex;
    justify-content: flex-start;
    padding: 0 5px;

    & .mini-toggle-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
      border: 1px solid #dadce0;
      border-radius: 20px;
      color: #3c4043;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      position: relative;
      min-width: 140px;

      &:hover {
        background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
        border-color: #bdc1c6;
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      // Icon styling
      .icon {
        font-size: 14px;
        transition: transform 0.2s ease;

        &:first-child {
          color: #1a73e8;
        }
      }

      .chevron-icon {
        margin-left: auto;
        font-size: 12px;
        opacity: 0.7;
      }

      // Desktop styles
      @media (min-width: 768px) {
        padding: 10px 18px;
        font-size: 14px;
        min-width: 160px;
        border-radius: 24px;
      }
    }
  }

  // Inventory Expanded Section
  & .inventory-expanded-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin: 5px 0 15px 0;
    padding: 15px;
    animation: expandInventory 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    @keyframes expandInventory {
      from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
      }
      to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
        padding-top: 15px;
        padding-bottom: 15px;
      }
    }

    // Enhanced styling for inventory fields
    & .product-content {
      background: white;
      border-radius: 8px;
      padding: 12px 15px;
      margin: 8px 0;
      border: 1px solid #e9ecef;
      transition: all 0.2s ease;

      &:hover {
        border-color: #dee2e6;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
      }

      // Label styling
      & .label {
        color: #495057;
        font-weight: 600;
        margin-bottom: 6px;
        display: block;
        font-size: 13px;
      }

      // Input styling specific to inventory
      & .input-custom {
        border: 1px solid #ced4da;
        border-radius: 6px;
        padding: 10px 12px;
        font-size: 14px;
        background: #fff;
        transition: all 0.2s ease;

        &:focus {
          border-color: #1a73e8;
          box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
          outline: none;
        }

        &:hover {
          border-color: #adb5bd;
        }
      }

      // Price text styling
      & .price-text {
        font-size: 12px;
        color: #6c757d;
        margin-top: 4px;
        font-weight: 500;

        &.notice {
          color: #28a745;
          font-weight: 600;
        }
      }

      // Error message styling
      & .error-message {
        color: #dc3545;
        font-size: 12px;
        margin-top: 4px;
        font-weight: 500;
      }
    }

    // Desktop styles
    @media (min-width: 768px) {
      padding: 18px 20px;
      margin: 8px 0 20px 0;

      & .product-content {
        padding: 15px 18px;
        margin: 10px 0;

        & .label {
          font-size: 14px;
          margin-bottom: 8px;
        }

        & .input-custom {
          padding: 12px 14px;
          font-size: 15px;
        }

        & .price-text {
          font-size: 13px;
        }
      }
    }
  }

  & .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 300px;

    .loading-spinner {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;

      svg {
        animation: spin 1s linear infinite;
      }
    }

    p {
      color: var(--app-primary-color);
      font-size: 16px;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

// Global category selection styles
.category-item {
  display: flex;
  align-items: center;
  gap: 8px;

  .private-note {
    font-size: 0.85em;
    color: #666;
    font-style: italic;
  }
}

.selected-category {
  display: flex;
  align-items: center;
  gap: 8px;

  .private-note {
    font-size: 0.85em;
    color: #666;
    font-style: italic;
  }
}

.category-path-container {
  margin-top: 8px;

  .category-breadcrumb {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 6px;
    font-size: 0.9em;

    .breadcrumb-item {
      color: #333;
      font-weight: 500;
    }

    .breadcrumb-separator {
      color: #666;
      font-size: 0.8em;
    }
  }
}

// Global Category Modal Styles
.category-modal-container {
  .category-modal-content {
    height: 90vh;
    max-height: 90vh;
    width: 500px;
    max-width: 95vw;
    border-radius: 12px;
    overflow: hidden;

    @media (max-width: 767px) {
      height: 95vh;
      max-height: 95vh;
      width: 100vw;
      max-width: 100vw;
      border-radius: 0;
    }
  }
}

.global-category-selector {
  width: 100%;
  min-height: 48px;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .selection-display {
    flex: 1;
    text-align: left;

    .selected-text {
      color: #333;
      font-weight: 500;

      .private-note {
        font-size: 0.85em;
        color: #666;
        font-style: italic;
        font-weight: normal;
      }
    }

    .placeholder {
      color: #999;
    }
  }

  .dropdown-icon {
    color: #666;
    transition: transform 0.2s;
  }
}

.category-selection-modal {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;

  .modal-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;

    .back-button, .search-toggle, .close-button {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;

      &:hover {
        background: #e9ecef;
      }

      svg {
        width: 20px;
        height: 20px;
        color: #666;
      }
    }

    .modal-title {
      flex: 1;
      margin: 0 16px;
      font-size: 1.1em;
      font-weight: 600;
      color: #333;
      text-align: center;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .search-container {
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;

    .search-input {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      font-size: 16px; // Prevent zoom on iOS

      &:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
      }
    }
  }

  .categories-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px 20px;
    min-height: 0; // Important for flex scrolling
    -webkit-overflow-scrolling: touch; // Smooth scrolling on iOS

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .category-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      margin-bottom: 8px;
      background: #f8f9fa;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      min-height: 44px; // Touch target

      &:hover {
        background: #e9ecef;
        border-color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
      }

      .category-info {
        flex: 1;

        .category-name {
          font-weight: 500;
          color: #333;
          font-size: 1em;
        }

        .private-note {
          display: block;
          font-size: 0.85em;
          color: #666;
          font-style: italic;
          margin-top: 2px;
        }
      }

      .nav-icon {
        color: #666;
        width: 16px;
        height: 16px;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #666;
      font-style: italic;
    }
  }
}

.cancel-create-container {
  max-height: 95dvh;
  width: 500px;
  max-width: 95%;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
  font-size: 15px;
}

.error-message.hight-light:not(.success) {
  transform-origin: 0 0;
  animation: high-light 0.5s ease-in-out infinite;
}
.category-content-container {
  max-height: 95dvh;
  // height: 95dvh;
  width: 500px;
  // max-height: 90%;
  max-width: 95%;
  // min-height: 40dvh;
  border-radius: 10px;
  padding: 10px;
  background-color: white;
}

.speech-button {
    float: right;
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    z-index: 1;
}

.speech-button:hover:not(:disabled) {
    background-color: #218838;
}

.speech-button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.speech-button.recording {
    background-color: #dc3545;
    animation: pulse 1s infinite;
}