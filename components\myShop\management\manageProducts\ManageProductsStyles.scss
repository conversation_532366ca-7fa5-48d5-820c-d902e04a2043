@media screen and (min-width:1025px) {
  .add-product-button{
    right: calc((100vw - var(--max-width-view)) / 2 + 15px) !important;
  }
}
.manage-products-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: var(--color-background-2);
  // position: relative;
  align-items: center;
  // justify-content: center;
  // overflow: auto;
  &  .list-product-amount {
    position: sticky;
    top: 0;
    left: 0;
    background: var(--color-background-2);
    padding: 5px 10px;
  }
  & .list-product-container {
    flex: 1;
    width: 100%;
    // overflow: auto;
    background: var(--color-background-2);

    
    & .product-loading {
      font-size: 1em;
      font-weight: 500;
      text-align: center;
      flex: 1;
      width: 100%;
    }
    & > .list-product-show {
      padding: 0 10px 50px 10px;
      flex-wrap: wrap;
      gap: 10px;
      display: flex;

      & > .product-item {
        --product-item-width: 100%;
        display: flex;
        padding: 5px;
        margin-top: 10px;
        width: var(--product-item-width);
        align-items: flex-start;
        gap: 10px;
        background-color: white;
        // box-shadow: 0 0 5px #ccc;
        border-radius: 5px;
        cursor: pointer;

        // @media screen and (min-width: 1025px) and (max-width: 1320px) {
        //   --product-item-width: calc(50% - 5px);
        // }

        // @media screen and (min-width: 1321px) {
        //   --product-item-width: calc(50% - 5px);
        // }

        & > img {
          margin-right: 5px;
          height: 100px;
          width: 100px;
          aspect-ratio: 1;
          border-radius: 10px;
          object-fit: cover;
          background-color: var(--color-background-2);
        }

        & > .product-detail {
          flex: 1;
          height: 100%;
          align-items: flex-start;
          // gap: 5px;
          // font-size: em;

          & > .name {
            font-weight: 500;
            color: var(--color-text-black);
            /* display: -webkit-box;
                          -webkit-box-orient: vertical;
                          -webkit-line-clamp: 2;
                          overflow: hidden;
                          text-overflow: ellipsis; */
          }

          & > .categories{
            font-weight: 600;
            font-size: 14px;
            color: var(--color-text-note);

            & > .categories-name{
              font-weight: 500;
              font-style: italic;
              color: #2b5bcd;
            }

            & > .not-classified{
              font-weight: 500;
              font-style: italic;
              color: var(--primary-color-2);
            }
          }

          & > .price {
            color: var(--primary-color-1);
            font-weight: bold;

            & > em {
              font-weight: 500;
              color: var(--color-text-note);
              text-decoration: line-through;
            }
          }

          & > .action {
            margin-left: auto;
            margin-top: auto;
            color: var(--primary-color-1);
            border: thin solid var(--primary-color-1);
            border-radius: 5px;
            width: 40px;
            height: 40px;
            font-size: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: white;
          }          & > .product-stock{
            font-size: 14px;
  
            & > .label{
              font-weight: 500;
              font-size: 14px;
            }
  
            & > .content{
              font-weight: 500;
              color: #00823e;
              font-style: italic;
            }
          }

          & > .product-commission{
            font-size: 14px;
  
            & > .label{
              font-weight: 500;
              font-size: 14px;
            }
  
            & > .content{
              font-weight: 500;
              color: #ff6b35;
              font-style: italic;
            }
          }

          & > .product-enable{
            display: flex;
            gap: 5px;
            height: 20px;
            & > .label{
              font-weight: 500;
              font-size: 14px;
            }
          }
        }
      }
      & .product-item:hover,.product-item:focus {
        box-shadow: #bbcac5 0px 0px 15px 0px;
      }

      & > .product-item.grid {
        --product-item-grid-width: calc(50% - 5px);
        flex-direction: column;
        width: var(--product-item-grid-width);
        @media screen and (min-width: 501px) and (max-width: 1024px) {
          --product-item-grid-width: calc((100% - 10px*2) / 3)
        }
        @media screen and (min-width: 1025px) and (max-width: 1320px) {
          --product-item-grid-width: calc((1024px - 10px*2 - 20px) / 3)
        }

        @media screen and (min-width: 1321px) {
          --product-item-grid-width: calc((1024px - 10px*2 - 20px) / 3)
        }
        & > img {
          width: 100%;
          height: auto;
          aspect-ratio: 1;
        }

        & > .product-detail {
          width: 100%;

          & > .name {
            display: -webkit-box;
            line-clamp: 2;
            box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          
        }
      }
    }
    & > .none-list-product {
      height: 100%;
      flex: 1;
      justify-content: center;
      align-items: center;
      gap: 10px;

      & > img {
        margin: 10px auto;
        justify-content: center;
        border-radius: 50%;
        width: 150px;
        height: 150px;
        object-fit: contain;
      }

      & > span {
        font-weight: 500;
        font-size: 1em;
        color: var(--color-text-note);
      }
    }
  }
  & .categories-container {
    justify-content: flex-start;
    display: flex;
    // gap: 10px;
    padding: 5px;
    overflow-x: auto;
    overflow-y: hidden;
    height: fit-content;
    width: 100%;
    flex: none;
    // height: 100px;
    // background: var(--color-background-2);
    // min-height: 50px;
    // max-height: unset;

    & .categories-tab {
      text-transform: none;
      font-size: 0.8em;
      color: var(--color-text-note);
      // box-shadow: 0 0 5px #ccc;
      z-index: 1;
      height: fit-content;
      align-items: center;
      gap: 10px;
      width: 100%;

      & .category-item-tab {
        // margin: 5px;
        padding: 0 5px;
        background-color: white;
        border: 1px solid #cccccc;
        align-self: flex-start;
        border-radius: 7px !important;
        height: unset !important;
        width: -moz-fit-content;
        width: fit-content;
        color: #868686;
        text-align: center;
        font-size: 15px;
        cursor: pointer;

        & div.tab-title {
          display: flex;
          align-items: center;
          gap: 5px;

          & span {
            text-transform: none;
            line-height: normal;
            // color: var(--color-text-black);
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-style: normal;
            font-weight: 600;
          }
        }
      }

      & .category-item-tab.active {
        color: #2b5bcd;
        border-color: #2b5bcd;
        font-weight: 600;
        // background-color: var(--primary-color-1);
      }

      & .v-slide-group__next,
      .v-slide-group__prev {
        color: var(--primary-color-1);
        min-width: unset;
      }
    }

    & > .category-item-button {
      margin: 5px;
      padding: 5px;
      background-color: white;
      border: 2px solid var(--primary-color-1);
      align-self: flex-start;
      border-radius: 10px;
      width: fit-content;
      color: var(--primary-color-1);
      text-align: center;
    }

    & > button.category-item-button > .name {
      font-size: 0.9em;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      white-space: nowrap;
    }

    & > button.category-item-button.active {
      background-color: var(--primary-color-1);
      color: white;
      font-weight: 500;
    }

    & > button.category-item-button > .name > span {
      font-style: italic;
    }
  }

  & .search-bar-container {
    align-items: center;
    padding: 0 3px;
    margin: 10px 0 5px 0;
    position: relative;
    height: 40px;
    margin-bottom: 5px;
    width: 100%;
    flex: none;
  }

  & .search-input-container {
    width: 100%;
    background-color: white;
    border-radius: 0;
    border-color: transparent;
    outline: none;
    height: 100%;
    font-size: var(--font-size);
  }

  & .search-button {
    display: flex;
    align-items: center;
    background: white;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    padding: 0 10px;
    color: var(--color-text-note);
    height: 100%;
    border: none;
  }
  & .loading-more {
    background-color: transparent;
    text-align: center;
    width: 100%;
    position: sticky;
    bottom: 0;
  }

  .share-dropdown-content > div {
    background: rgb(0, 0, 0, 80%) !important;
    border-radius: 15px !important;
  }
  .share-dropdown-item {
    color: white !important;
    text-align: center !important;
    background: transparent !important;
    font-size: 1em;
    position: relative;

    & .custom-share-button {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }
    & .custom-share-button:hover {
      opacity: calc(var(--v-hover-opacity) * var(--v-theme-overlay-multiplier));
    }
  }
  
}
 
.add-product-button {
  color: white;
  background-color: var(--primary-color-1);
  gap: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  font-size: 25px;
  font-weight: 600;
  position: fixed;
  bottom: 20px;
  right: 20px;
  border-radius: 2em;
  width: -moz-fit-content;
  width: 40px;
  height: 40px;
  cursor: pointer;
  z-index: 2; 
}
.zalo-share-button {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 0;
  opacity: 0;
}

.add-product-option {
  color: var(--primary-color-1) !important;
  text-align: center !important;
  background: transparent !important;
  font-size: 1em;
  position: relative;
}