<template>
  <div class="stock-history-component">
    <!-- Sub Header -->
    <SubHeaderV2Component
      :title="$t('StockManagement.lich_su_ton_kho')"
      :show-back="true"
      @back="$router.back()"
    />

    <!-- Product Info Header -->
    <div v-if="selectedProduct" class="product-header">
      <div class="product-info">
        <img :src="selectedProduct.image || '/default-product.png'" :alt="selectedProduct.name" class="product-image">
        <div class="product-details">
          <h2>{{ selectedProduct.name }}</h2>
          <p class="product-category">{{ selectedProduct.category }}</p>
          <div class="current-stock">
            <span class="stock-label">{{ $t('StockManagement.ton_kho_hien_tai') }}:</span>
            <span class="stock-value" :class="getStockLevelClass(selectedProduct.current_stock)">
              {{ selectedProduct.current_stock || 0 }} {{ selectedProduct.unit }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Date Filter -->
    <div class="filter-section">
      <div class="date-filter">
        <div class="date-range">
          <div class="date-input-group">
            <label>{{ $t('StockManagement.tu_ngay') }}</label>
            <input
              v-model="dateFilter.from"
              type="date"
              class="date-input"
              @change="loadHistory"
            />
          </div>
          <div class="date-input-group">
            <label>{{ $t('StockManagement.den_ngay') }}</label>
            <input
              v-model="dateFilter.to"
              type="date"
              class="date-input"
              @change="loadHistory"
            />
          </div>
        </div>
        
        <!-- Transaction Type Filter -->
        <div class="type-filters">
          <button 
            v-for="filter in typeFilters" 
            :key="filter.key"
            @click="activeTypeFilter = filter.key"
            :class="['type-filter-btn', { active: activeTypeFilter === filter.key }]"
          >
            <Icon :name="filter.icon" size="16" />
            {{ filter.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- History Timeline -->
    <div class="history-section">
      <div v-if="loading" class="loading-state">
        <Icon name="solar:refresh-bold" size="32" class="loading-icon" />
        <p>{{ $t('StockManagement.dang_tai') }}</p>
      </div>

      <div v-else-if="filteredHistory.length === 0" class="empty-state">
        <Icon name="solar:history-bold" size="48" />
        <h3>{{ $t('StockManagement.chua_co_lich_su') }}</h3>
        <p>{{ $t('StockManagement.chua_co_giao_dich') }}</p>
      </div>

      <div v-else class="history-timeline">
        <div 
          v-for="(transaction, index) in filteredHistory" 
          :key="transaction.id"
          class="timeline-item"
        >
          <div class="timeline-marker" :class="getTransactionTypeClass(transaction.type)">
            <Icon :name="getTransactionIcon(transaction.type)" size="16" />
          </div>
          
          <div class="timeline-content">
            <div class="transaction-header">
              <h4>{{ getTransactionTitle(transaction.type) }}</h4>
              <span class="transaction-date">{{ formatDate(transaction.created_at) }}</span>
            </div>
            
            <div class="transaction-details">
              <div class="quantity-change">
                <span class="change-label">{{ $t('StockManagement.thay_doi') }}:</span>
                <span class="change-value" :class="getQuantityChangeClass(transaction.type)">
                  {{ getQuantityChangeText(transaction.type, transaction.quantity) }}
                </span>
              </div>
              
              <div v-if="transaction.price" class="transaction-price">
                <span class="price-label">{{ getPriceLabel(transaction.type) }}:</span>
                <span class="price-value">{{ formatCurrency(transaction.price) }}</span>
              </div>
              
              <div v-if="transaction.reason" class="transaction-reason">
                <span class="reason-label">{{ $t('StockManagement.ly_do') }}:</span>
                <span class="reason-value">{{ getWasteReasonText(transaction.reason) }}</span>
              </div>
              
              <div v-if="transaction.notes" class="transaction-notes">
                <span class="notes-label">{{ $t('StockManagement.ghi_chu') }}:</span>
                <span class="notes-value">{{ transaction.notes }}</span>
              </div>
              
              <div class="stock-after">
                <span class="stock-label">{{ $t('StockManagement.ton_kho_sau_giao_dich') }}:</span>
                <span class="stock-value">{{ transaction.stock_after || 0 }} {{ selectedProduct?.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMoreHistory && !loading" class="load-more-section">
        <button @click="loadMoreHistory" class="load-more-btn">
          {{ $t('StockManagement.tai_them') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue'
import { HttpStatusCode } from "axios";

// Props
const props = defineProps<{
  mode?: string
}>()

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Use the centralized shop data composable
const {
  activeShop,
  isLoading: shopDataLoading,
  initializeShopData,
  getShopBySlug
} = useShopData()

// Services
const stockService = new StockService()

// Reactive data
const loading = ref(false)
const selectedProduct = ref(null)
const stockHistory = ref([])
const currentPage = ref(0)
const hasMoreHistory = ref(true)
const pageSize = 20
const activeTypeFilter = ref('all')

const dateFilter = ref({
  from: '',
  to: ''
})

// Transaction type filters
const typeFilters = computed(() => [
  { key: 'all', label: t('StockManagement.tat_ca'), icon: 'solar:list-bold' },
  { key: 'import', label: t('StockManagement.nhap_kho'), icon: 'solar:import-bold' },
  { key: 'export', label: t('StockManagement.xuat_kho'), icon: 'solar:export-bold' },
  { key: 'waste', label: t('StockManagement.hang_huy'), icon: 'solar:trash-bin-trash-bold' }
])

const shopSlug = route.params.id as string
const shopId = computed(() => {
    const shop = getShopBySlug(shopSlug)
    return shop?.id || shopSlug
})


const productId = computed(() => {
  return route.params.product_id || route.params.productId
})

const filteredHistory = computed(() => {
  let filtered = stockHistory.value

  // Filter by transaction type
  if (activeTypeFilter.value !== 'all') {
    filtered = filtered.filter(transaction => transaction.type === activeTypeFilter.value)
  }

  // Filter by date range
  if (dateFilter.value.from && dateFilter.value.to) {
    const fromDate = new Date(dateFilter.value.from)
    const toDate = new Date(dateFilter.value.to)
    filtered = filtered.filter(transaction => {
      const transactionDate = new Date(transaction.created_at)
      return transactionDate >= fromDate && transactionDate <= toDate
    })
  }

  return filtered
})

// Methods
const getStockLevelClass = (stock: number) => {
  if (stock <= 0) return 'stock-empty'
  if (stock <= 10) return 'stock-low'
  return 'stock-normal'
}

const getTransactionTypeClass = (type: string) => {
  switch (type) {
    case 'import': return 'type-import'
    case 'export': return 'type-export'
    case 'waste': return 'type-waste'
    default: return 'type-default'
  }
}

const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'import': return 'solar:import-bold'
    case 'export': return 'solar:export-bold'
    case 'waste': return 'solar:trash-bin-trash-bold'
    default: return 'solar:history-bold'
  }
}

const getTransactionTitle = (type: string) => {
  switch (type) {
    case 'import': return t('StockManagement.nhap_kho')
    case 'export': return t('StockManagement.xuat_kho')
    case 'waste': return t('StockManagement.hang_huy')
    default: return t('StockManagement.giao_dich')
  }
}

const getQuantityChangeClass = (type: string) => {
  switch (type) {
    case 'import': return 'change-positive'
    case 'export':
    case 'waste': return 'change-negative'
    default: return 'change-neutral'
  }
}

const getQuantityChangeText = (type: string, quantity: number) => {
  const sign = type === 'import' ? '+' : '-'
  return `${sign}${quantity}`
}

const getPriceLabel = (type: string) => {
  switch (type) {
    case 'import': return t('StockManagement.gia_nhap')
    case 'export': return t('StockManagement.gia_ban')
    default: return t('StockManagement.gia_tri')
  }
}

const getWasteReasonText = (reason: string) => {
  const reasons = {
    expired: t('StockManagement.het_han'),
    damaged: t('StockManagement.hong_hoc'),
    spoiled: t('StockManagement.bi_hong'),
    contaminated: t('StockManagement.bi_nhiem'),
    quality_issue: t('StockManagement.chat_luong_kem'),
    customer_return: t('StockManagement.khach_tra_lai'),
    other: t('StockManagement.ly_do_khac')
  }
  return reasons[reason] || reason
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount || 0)
}

const loadProductInfo = async () => {
  try {
    // Load product information
    // This would typically come from a product service
    // For now, we'll simulate it
    selectedProduct.value = {
      id: productId.value,
      name: 'Sample Product',
      category: 'Sample Category',
      current_stock: 50,
      unit: 'kg',
      image: '/default-product.png'
    }
  } catch (error) {
    console.error('Error loading product info:', error)
  }
}

const loadHistory = async (reset = false) => {
  if (loading.value || !productId.value) return

  loading.value = true
  try {
    const offset = reset ? 0 : currentPage.value * pageSize
    const response = await stockService.getStockHistory(productId.value, offset, pageSize)

    if (response.status === HttpStatusCode.Ok) {
      const newHistory = response.data?.history || []

      if (reset) {
        stockHistory.value = newHistory
        currentPage.value = 0
      } else {
        stockHistory.value = [...stockHistory.value, ...newHistory]
      }

      hasMoreHistory.value = newHistory.length === pageSize
      currentPage.value++
    }
  } catch (error) {
    console.error('Error loading stock history:', error)
  } finally {
    loading.value = false
  }
}

const loadMoreHistory = () => {
  loadHistory(false)
}

const setDefaultDateRange = () => {
  const today = new Date()
  const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

  dateFilter.value.from = oneMonthAgo.toISOString().split('T')[0]
  dateFilter.value.to = today.toISOString().split('T')[0]
}

// Watch for filter changes
watch([activeTypeFilter, dateFilter], () => {
  // Filters are applied via computed property, no need to reload
}, { deep: true })

// Lifecycle
onMounted(async () => {
  // Initialize shop data first
  await initializeShopData()
  // Load product info
  await loadProductInfo()
  // Set default date range
  setDefaultDateRange()
  // Load initial history
  await loadHistory(true)
})
</script>

<style scoped>
.stock-history-component {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750px;
}

.product-header {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .product-info {
    display: flex;
    align-items: center;
    gap: 15px;

    .product-image {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      object-fit: cover;
      flex-shrink: 0;
    }

    .product-details {
      flex: 1;

      h2 {
        font-size: 18px;
        font-weight: 700;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      .product-category {
        font-size: 14px;
        color: #7f8c8d;
        margin: 0 0 8px 0;
      }

      .current-stock {
        display: flex;
        align-items: center;
        gap: 8px;

        .stock-label {
          font-size: 12px;
          color: #7f8c8d;
        }

        .stock-value {
          font-size: 14px;
          font-weight: 600;
          padding: 4px 8px;
          border-radius: 4px;

          &.stock-normal {
            background: #d5f4e6;
            color: #27ae60;
          }

          &.stock-low {
            background: #fef9e7;
            color: #f39c12;
          }

          &.stock-empty {
            background: #fadbd8;
            color: #e74c3c;
          }
        }
      }
    }
  }
}

.filter-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;

  .date-input-group {
    label {
      display: block;
      font-size: 12px;
      font-weight: 600;
      color: #7f8c8d;
      margin-bottom: 5px;
    }

    .date-input {
      width: 100%;
      border: 2px solid #ecf0f1;
      border-radius: 8px;
      padding: 10px 12px;
      font-size: 14px;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #3498db;
        background-color: #f8f9fa;
      }
    }
  }
}

.type-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .type-filter-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 2px solid #ecf0f1;
    border-radius: 20px;
    background: white;
    color: #7f8c8d;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      border-color: #3498db;
      color: #3498db;
    }

    &.active {
      background: #3498db;
      border-color: #3498db;
      color: white;
    }
  }
}

.history-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;

  .loading-icon {
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 15px 0 8px 0;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.history-timeline {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ecf0f1;
  }

  .timeline-item {
    position: relative;
    padding-left: 60px;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .timeline-marker {
      position: absolute;
      left: 8px;
      top: 8px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      z-index: 1;

      &.type-import {
        background: #4facfe;
      }

      &.type-export {
        background: #e74c3c;
      }

      &.type-waste {
        background: #f39c12;
      }

      &.type-default {
        background: #7f8c8d;
      }
    }

    .timeline-content {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      border-left: 3px solid #ecf0f1;

      .transaction-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        h4 {
          font-size: 14px;
          font-weight: 600;
          margin: 0;
          color: #2c3e50;
        }

        .transaction-date {
          font-size: 12px;
          color: #7f8c8d;
        }
      }

      .transaction-details {
        display: flex;
        flex-direction: column;
        gap: 6px;

        > div {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;

          .quantity-change,
          .transaction-price,
          .transaction-reason,
          .transaction-notes,
          .stock-after {
            span:first-child {
              color: #7f8c8d;
              font-weight: 500;
            }

            span:last-child {
              color: #2c3e50;
              font-weight: 600;
            }
          }

          .change-value {
            &.change-positive {
              color: #27ae60;
            }

            &.change-negative {
              color: #e74c3c;
            }

            &.change-neutral {
              color: #7f8c8d;
            }
          }

          .price-value {
            color: #3498db;
          }

          .reason-value {
            color: #f39c12;
          }
        }
      }
    }
  }
}

.load-more-section {
  text-align: center;
  margin-top: 20px;

  .load-more-btn {
    background: #ecf0f1;
    color: #7f8c8d;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #d5dbdb;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .stock-history-component {
    padding: 10px;
  }

  .date-range {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .product-header,
  .filter-section,
  .history-section {
    padding: 15px;
  }

  .type-filters {
    .type-filter-btn {
      font-size: 11px;
      padding: 6px 10px;
    }
  }

  .history-timeline {
    .timeline-item {
      padding-left: 50px;

      .timeline-marker {
        left: 6px;
        width: 20px;
        height: 20px;
      }
    }

    &::before {
      left: 16px;
    }
  }
}
</style>
