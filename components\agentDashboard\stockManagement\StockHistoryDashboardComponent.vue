<template>
  <div class="stock-history-dashboard">
    <!-- Header -->
    <SubHeaderV2Component
      :title="$t('StockManagement.dashboard_lich_su_ton_kho')"
      ><template #header_left>
        <button class="back-button" @click="router.push(`/agent/shop/${shopSlug}/stock`)">
          <Icon name="solar:round-alt-arrow-left-linear"></Icon>
        </button>
      </template>
    </SubHeaderV2Component>

    <!-- Dashboard Stats -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon import">
          <Icon name="solar:import-bold" size="24" />
        </div>
        <div class="stat-content">
          <h3>{{ dashboardStats.totalImports }}</h3>
          <p>{{ $t('StockManagement.tong_nhap_kho') }}</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon export">
          <Icon name="solar:export-bold" size="24" />
        </div>
        <div class="stat-content">
          <h3>{{ dashboardStats.totalExports }}</h3>
          <p>{{ $t('StockManagement.tong_xuat_kho') }}</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon waste">
          <Icon name="solar:trash-bin-trash-bold" size="24" />
        </div>
        <div class="stat-content">
          <h3>{{ dashboardStats.totalWaste }}</h3>
          <p>{{ $t('StockManagement.tong_hang_huy') }}</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon value">
          <Icon name="solar:dollar-bold" size="24" />
        </div>
        <div class="stat-content">
          <h3>{{ formatCurrency(dashboardStats.totalValue) }}</h3>
          <p>{{ $t('StockManagement.tong_gia_tri') }}</p>
        </div>
      </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
      <div class="date-filter">
        <div class="date-range">
          <div class="date-input-group">
            <label>{{ $t('StockManagement.tu_ngay') }}</label>
            <input
              v-model="dateFilter.from"
              type="date"
              class="date-input"
              @change="loadDashboardData"
            />
          </div>
          <div class="date-input-group">
            <label>{{ $t('StockManagement.den_ngay') }}</label>
            <input
              v-model="dateFilter.to"
              type="date"
              class="date-input"
              @change="loadDashboardData"
            />
          </div>
        </div>

        <!-- Quick Date Filters -->
        <div class="quick-filters">
          <button 
            v-for="filter in quickDateFilters" 
            :key="filter.key"
            @click="setQuickDateFilter(filter.key)"
            :class="['quick-filter-btn', { active: activeQuickFilter === filter.key }]"
          >
            {{ filter.label }}
          </button>
        </div>
      </div>

      <!-- Search and Category Filter -->
      <div class="search-filter">
        <div class="search-input-group">
          <Icon name="solar:magnifer-bold" size="16" />
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="$t('StockManagement.tim_kiem_san_pham')"
            class="search-input"
          />
        </div>

        <select v-model="selectedCategory" class="category-select" @change="loadDashboardData">
          <option value="">{{ $t('StockManagement.tat_ca_danh_muc') }}</option>
          <option v-for="category in categories" :key="category.id" :value="category.id">
            {{ category.name }}
          </option>
        </select>
      </div>
    </div>

    <!-- Recent Activities -->
    <div class="recent-activities">
      <div class="section-header">
        <h2>{{ $t('StockManagement.hoat_dong_gan_day') }}</h2>
        <button @click="showAllActivities" class="view-all-btn">
          {{ $t('StockManagement.xem_tat_ca') }}
        </button>
      </div>

      <div v-if="loading" class="loading-state">
        <Icon name="solar:refresh-bold" size="32" class="loading-icon" />
        <p>{{ $t('StockManagement.dang_tai') }}</p>
      </div>

      <div v-else-if="filteredActivities.length === 0" class="empty-state">
        <Icon name="solar:history-bold" size="48" />
        <h3>{{ $t('StockManagement.chua_co_hoat_dong') }}</h3>
        <p>{{ $t('StockManagement.chua_co_giao_dich_nao') }}</p>
      </div>

      <div v-else class="activities-list">
        <div 
          v-for="activity in filteredActivities" 
          :key="activity.id"
          class="activity-item"
          @click="viewProductHistory(activity.product_id)"
        >
          <div class="activity-icon" :class="getActivityTypeClass(activity.type)">
            <Icon :name="getActivityIcon(activity.type)" size="16" />
          </div>

          <div class="activity-content">
            <div class="activity-header">
              <h4>{{ activity.product_name }}</h4>
              <span class="activity-time">{{ formatTime(activity.created_at) }}</span>
            </div>

            <div class="activity-details">
              <span class="activity-type">{{ getActivityTitle(activity.type) }}</span>
              <span class="activity-quantity" :class="getQuantityChangeClass(activity.type)">
                {{ getQuantityChangeText(activity.type, activity.quantity) }} {{ activity.unit }}
              </span>
              <span v-if="activity.price" class="activity-price">
                {{ formatCurrency(activity.price) }}
              </span>
            </div>

            <div v-if="activity.reason" class="activity-reason">
              <span class="reason-label">{{ $t('StockManagement.ly_do') }}:</span>
              <span class="reason-text">{{ getWasteReasonText(activity.reason) }}</span>
            </div>
          </div>

          <div class="activity-arrow">
            <Icon name="solar:arrow-right-bold" size="16" />
          </div>
        </div>
      </div>
    </div>

    <!-- Top Products by Activity -->
    <div class="top-products">
      <div class="section-header">
        <h2>{{ $t('StockManagement.san_pham_hoat_dong_nhieu') }}</h2>
      </div>

      <div class="products-grid">
        <div 
          v-for="product in topActiveProducts" 
          :key="product.id"
          class="product-card"
          @click="viewProductHistory(product.id)"
        >
          <img :src="product.image || '/default-product.png'" :alt="product.name" class="product-image">
          
          <div class="product-info">
            <h4>{{ product.name }}</h4>
            <p class="product-category">{{ product.category_name }}</p>
            
            <div class="activity-summary">
              <div class="activity-count import">
                <Icon name="solar:import-bold" size="12" />
                <span>{{ product.import_count || 0 }}</span>
              </div>
              <div class="activity-count export">
                <Icon name="solar:export-bold" size="12" />
                <span>{{ product.export_count || 0 }}</span>
              </div>
              <div class="activity-count waste">
                <Icon name="solar:trash-bin-trash-bold" size="12" />
                <span>{{ product.waste_count || 0 }}</span>
              </div>
            </div>

            <div class="current-stock">
              <span class="stock-label">{{ $t('StockManagement.ton_kho') }}:</span>
              <span class="stock-value" :class="getStockLevelClass(product.current_stock)">
                {{ product.current_stock || 0 }} {{ product.unit }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Export Data Section -->
    <div class="export-section">
      <div class="section-header">
        <h2>{{ $t('StockManagement.xuat_bao_cao') }}</h2>
      </div>

      <div class="export-options">
        <button @click="exportToExcel" class="export-btn excel">
          <Icon name="solar:document-bold" size="16" />
          {{ $t('StockManagement.xuat_excel') }}
        </button>
        
        <button @click="exportToPDF" class="export-btn pdf">
          <Icon name="solar:file-text-bold" size="16" />
          {{ $t('StockManagement.xuat_pdf') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import { CategoryService } from '~/services/categoryService/categoryService'
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue'
import { HttpStatusCode } from "axios"

// Props
const props = defineProps<{
  mode?: string
}>()

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Use the centralized shop data composable
const {
  activeShop,
  isLoading: shopDataLoading,
  initializeShopData,
  getShopBySlug
} = useShopData()

// Services
const stockService = new StockService()
const categoryService = new CategoryService()

// Reactive data
const loading = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const activeQuickFilter = ref('7days')
const categories = ref([])
const recentActivities = ref([])
const topActiveProducts = ref([])
const dashboardStats = ref({
  totalImports: 0,
  totalExports: 0,
  totalWaste: 0,
  totalValue: 0
})

const dateFilter = ref({
  from: '',
  to: ''
})

// Quick date filters
const quickDateFilters = computed(() => [
  { key: 'today', label: t('StockManagement.hom_nay') },
  { key: '7days', label: t('StockManagement.7_ngay') },
  { key: '30days', label: t('StockManagement.30_ngay') },
  { key: '90days', label: t('StockManagement.90_ngay') },
  { key: 'custom', label: t('StockManagement.tuy_chinh') }
])

const shopSlug = route.params.id as string
const shopId = computed(() => {
    const shop = getShopBySlug(shopSlug)
    return shop?.id || shopSlug
})

const filteredActivities = computed(() => {
  let filtered = recentActivities.value

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(activity => 
      activity.product_name?.toLowerCase().includes(query)
    )
  }

  // Filter by category
  if (selectedCategory.value) {
    filtered = filtered.filter(activity => 
      activity.category_id === selectedCategory.value
    )
  }

  return filtered.slice(0, 10) // Show only recent 10 activities
})

// Methods
const getStockLevelClass = (stock: number) => {
  if (stock <= 0) return 'stock-empty'
  if (stock <= 10) return 'stock-low'
  return 'stock-normal'
}

const getActivityTypeClass = (type: string) => {
  switch (type) {
    case 'import': return 'activity-import'
    case 'export': return 'activity-export'
    case 'waste': return 'activity-waste'
    default: return 'activity-default'
  }
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'import': return 'solar:import-bold'
    case 'export': return 'solar:export-bold'
    case 'waste': return 'solar:trash-bin-trash-bold'
    default: return 'solar:history-bold'
  }
}

const getActivityTitle = (type: string) => {
  switch (type) {
    case 'import': return t('StockManagement.nhap_kho')
    case 'export': return t('StockManagement.xuat_kho')
    case 'waste': return t('StockManagement.hang_huy')
    default: return t('StockManagement.giao_dich')
  }
}

const getQuantityChangeClass = (type: string) => {
  switch (type) {
    case 'import': return 'change-positive'
    case 'export':
    case 'waste': return 'change-negative'
    default: return 'change-neutral'
  }
}

const getQuantityChangeText = (type: string, quantity: number) => {
  const sign = type === 'import' ? '+' : '-'
  return `${sign}${quantity}`
}

const getWasteReasonText = (reason: string) => {
  const reasons = {
    expired: t('StockManagement.het_han'),
    damaged: t('StockManagement.hong_hoc'),
    spoiled: t('StockManagement.bi_hong'),
    contaminated: t('StockManagement.bi_nhiem'),
    quality_issue: t('StockManagement.chat_luong_kem'),
    customer_return: t('StockManagement.khach_tra_lai'),
    other: t('StockManagement.ly_do_khac')
  }
  return reasons[reason] || reason
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    return t('StockManagement.vua_xong')
  } else if (diffInHours < 24) {
    return t('StockManagement.gio_truoc', { hours: diffInHours })
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return t('StockManagement.ngay_truoc', { days: diffInDays })
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount || 0)
}

const setQuickDateFilter = (filterKey: string) => {
  activeQuickFilter.value = filterKey
  const today = new Date()
  
  switch (filterKey) {
    case 'today':
      dateFilter.value.from = today.toISOString().split('T')[0]
      dateFilter.value.to = today.toISOString().split('T')[0]
      break
    case '7days':
      const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      dateFilter.value.from = sevenDaysAgo.toISOString().split('T')[0]
      dateFilter.value.to = today.toISOString().split('T')[0]
      break
    case '30days':
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      dateFilter.value.from = thirtyDaysAgo.toISOString().split('T')[0]
      dateFilter.value.to = today.toISOString().split('T')[0]
      break
    case '90days':
      const ninetyDaysAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
      dateFilter.value.from = ninetyDaysAgo.toISOString().split('T')[0]
      dateFilter.value.to = today.toISOString().split('T')[0]
      break
    case 'custom':
      // Keep current date range
      break
  }
  
  if (filterKey !== 'custom') {
    loadDashboardData()
  }
}

const loadCategories = async () => {
  try {
    const response = await categoryService.getAllCategories()
    if (response.status === HttpStatusCode.Ok) {
      categories.value = response.data?.categories || []
    }
  } catch (error) {
    console.error('Error loading categories:', error)
  }
}

const loadDashboardStats = async () => {
  try {
    const response = await stockService.getDashboardStats(shopId.value, {
      from: dateFilter.value.from,
      to: dateFilter.value.to,
      category: selectedCategory.value
    })

    if (response.status === HttpStatusCode.Ok) {
      dashboardStats.value = response.data?.stats || {
        totalImports: 0,
        totalExports: 0,
        totalWaste: 0,
        totalValue: 0
      }
    }
  } catch (error) {
    console.error('Error loading dashboard stats:', error)
  }
}

const loadRecentActivities = async () => {
  try {
    const response = await stockService.getRecentActivities(shopId.value, {
      from: dateFilter.value.from,
      to: dateFilter.value.to,
      category: selectedCategory.value,
      limit: 20
    })

    if (response.status === HttpStatusCode.Ok) {
      recentActivities.value = response.data?.activities || []
    }
  } catch (error) {
    console.error('Error loading recent activities:', error)
  }
}

const loadTopActiveProducts = async () => {
  try {
    const response = await stockService.getTopActiveProducts(shopId.value, {
      from: dateFilter.value.from,
      to: dateFilter.value.to,
      category: selectedCategory.value,
      limit: 6
    })

    if (response.status === HttpStatusCode.Ok) {
      topActiveProducts.value = response.data?.products || []
    }
  } catch (error) {
    console.error('Error loading top active products:', error)
  }
}

const loadDashboardData = async () => {
  if (!shopId.value) return
  
  loading.value = true
  try {
    await Promise.all([
      loadDashboardStats(),
      loadRecentActivities(),
      loadTopActiveProducts()
    ])
  } finally {
    loading.value = false
  }
}

const viewProductHistory = (productId: string) => {
  router.push(`/agent/shop/${shopSlug}/stock-management/history/${productId}`)
}

const showAllActivities = () => {
  router.push(`/agent/shop/${shopSlug}/stock-management/history`)
}

const exportToExcel = async () => {
  try {
    const response = await stockService.exportStockHistory(shopId.value, {
      from: dateFilter.value.from,
      to: dateFilter.value.to,
      category: selectedCategory.value,
      format: 'excel'
    })

    // Handle file download
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `stock-history-${Date.now()}.xlsx`
    a.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error exporting to Excel:', error)
  }
}

const exportToPDF = async () => {
  try {
    const response = await stockService.exportStockHistory(shopId.value, {
      from: dateFilter.value.from,
      to: dateFilter.value.to,
      category: selectedCategory.value,
      format: 'pdf'
    })

    // Handle file download
    const blob = new Blob([response.data], { type: 'application/pdf' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `stock-history-${Date.now()}.pdf`
    a.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error exporting to PDF:', error)
  }
}

// Watch for search query changes
watch(searchQuery, () => {
  // Search is handled reactively by computed property
})

// Lifecycle
onMounted(async () => {
  // Initialize shop data first
  await initializeShopData()
  // Load categories
  await loadCategories()
  // Set default date range (7 days)
  setQuickDateFilter('7days')
})
</script>

<style scoped>
.stock-history-dashboard {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750px;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;

  .stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;

      &.import {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.export {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }

      &.waste {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      }

      &.value {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      }
    }

    .stat-content {
      flex: 1;

      h3 {
        font-size: 18px;
        font-weight: 700;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0;
      }
    }
  }
}

.filter-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;

  .date-input-group {
    label {
      display: block;
      font-size: 12px;
      font-weight: 600;
      color: #7f8c8d;
      margin-bottom: 5px;
    }

    .date-input {
      width: 100%;
      border: 2px solid #ecf0f1;
      border-radius: 8px;
      padding: 10px 12px;
      font-size: 14px;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #3498db;
        background-color: #f8f9fa;
      }
    }
  }
}

.quick-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 15px;

  .quick-filter-btn {
    padding: 8px 12px;
    border: 2px solid #ecf0f1;
    border-radius: 20px;
    background: white;
    color: #7f8c8d;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      border-color: #3498db;
      color: #3498db;
    }

    &.active {
      background: #3498db;
      border-color: #3498db;
      color: white;
    }
  }
}

.search-filter {
  display: flex;
  gap: 10px;

  .search-input-group {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;

    .search-input {
      width: 100%;
      border: 2px solid #ecf0f1;
      border-radius: 8px;
      padding: 10px 12px 10px 35px;
      font-size: 14px;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #3498db;
        background-color: #f8f9fa;
      }
    }

    > svg {
      position: absolute;
      left: 12px;
      color: #7f8c8d;
      z-index: 1;
    }
  }

  .category-select {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    background: white;
    color: #2c3e50;
    cursor: pointer;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
}

.recent-activities,
.top-products,
.export-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  h2 {
    font-size: 16px;
    font-weight: 700;
    margin: 0;
    color: #2c3e50;
  }

  .view-all-btn {
    background: none;
    border: none;
    color: #3498db;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #ebf3fd;
    }
  }
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;

  .loading-icon {
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 15px 0 8px 0;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.activities-list {
  .activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #ecf0f1;

    &:hover {
      background-color: #f8f9fa;
      border-color: #3498db;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .activity-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;

      &.activity-import {
        background: #4facfe;
      }

      &.activity-export {
        background: #e74c3c;
      }

      &.activity-waste {
        background: #f39c12;
      }

      &.activity-default {
        background: #7f8c8d;
      }
    }

    .activity-content {
      flex: 1;

      .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        h4 {
          font-size: 14px;
          font-weight: 600;
          margin: 0;
          color: #2c3e50;
        }

        .activity-time {
          font-size: 11px;
          color: #7f8c8d;
        }
      }

      .activity-details {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        margin-bottom: 4px;

        .activity-type {
          color: #7f8c8d;
        }

        .activity-quantity {
          font-weight: 600;

          &.change-positive {
            color: #27ae60;
          }

          &.change-negative {
            color: #e74c3c;
          }
        }

        .activity-price {
          color: #3498db;
          font-weight: 600;
        }
      }

      .activity-reason {
        font-size: 11px;
        color: #f39c12;

        .reason-label {
          color: #7f8c8d;
        }
      }
    }

    .activity-arrow {
      color: #bdc3c7;
      flex-shrink: 0;
    }
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;

  .product-card {
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3498db;
      box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
    }

    .product-image {
      width: 100%;
      height: 80px;
      object-fit: cover;
      border-radius: 6px;
      margin-bottom: 10px;
    }

    .product-info {
      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #2c3e50;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-category {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0 0 8px 0;
      }

      .activity-summary {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;

        .activity-count {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 11px;
          font-weight: 600;

          &.import {
            color: #4facfe;
          }

          &.export {
            color: #e74c3c;
          }

          &.waste {
            color: #f39c12;
          }
        }
      }

      .current-stock {
        display: flex;
        align-items: center;
        gap: 4px;

        .stock-label {
          font-size: 11px;
          color: #7f8c8d;
        }

        .stock-value {
          font-size: 11px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 3px;

          &.stock-normal {
            background: #d5f4e6;
            color: #27ae60;
          }

          &.stock-low {
            background: #fef9e7;
            color: #f39c12;
          }

          &.stock-empty {
            background: #fadbd8;
            color: #e74c3c;
          }
        }
      }
    }
  }
}

.export-options {
  display: flex;
  gap: 10px;

  .export-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    background: white;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3498db;
      color: #3498db;
    }

    &.excel:hover {
      border-color: #27ae60;
      color: #27ae60;
    }

    &.pdf:hover {
      border-color: #e74c3c;
      color: #e74c3c;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .stock-history-dashboard {
    padding: 10px;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 10px;

    .stat-card {
      padding: 15px;

      .stat-icon {
        width: 40px;
        height: 40px;
      }

      .stat-content h3 {
        font-size: 16px;
      }
    }
  }

  .date-range {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .search-filter {
    flex-direction: column;
    gap: 10px;
  }

  .quick-filters {
    .quick-filter-btn {
      font-size: 11px;
      padding: 6px 10px;
    }
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .export-options {
    flex-direction: column;
  }

  .recent-activities,
  .top-products,
  .export-section {
    padding: 15px;
  }
}
</style>
